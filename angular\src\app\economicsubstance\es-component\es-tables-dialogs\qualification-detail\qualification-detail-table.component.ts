import { Component, OnInit, Input, Injector } from '@angular/core';
import { EmployeeQualificationDetailsDto } from '@shared/service-proxies/service-proxies';
import { AppComponentBase } from '@shared/common/app-component-base';
import * as _ from 'lodash';
import { AppConsts } from '@shared/AppConsts';


@Component({
  selector: 'app-qualification-detail-table',
  templateUrl: './qualification-detail-table.component.html',
  styleUrls: ['./qualification-detail-table.component.css']
})
export class QualificationDetailTableComponent extends AppComponentBase implements OnInit {

    @Input() details: EmployeeQualificationDetailsDto[];
    @Input() headingSeq: string;
    @Input() sequenceNo: string;
    @Input() readOnlyMode: boolean;
    @Input() importOnlyMode: boolean = false;
    @Input() showHideCondition: boolean = false;

    constructor(injector: Injector) { super(injector); }

    ngOnInit() {
    }

    removesource(id: any): void {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            'Are you sure you want to delete it?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.handleDelete(id);
                }
            }
        );
    }


    handleDelete(id: any) {
        let index = this.details.findIndex(x => x.id == id);
        this.details[index].isDeleted = index != -1 ? true : this.details[index].isDeleted;
    }

    //update table
    updateSource(sp: any) {
        let persondetail = _.cloneDeep(sp);
        let index = this.details.findIndex(x => x.id == sp.id);
        if (index == -1) this.details.push(persondetail);
        else this.details[index] = persondetail;
    }

    returnyesno(item: any): string {
        if (item === undefined || item === null) return '';
        return item ? 'Yes' : 'No';

    }

}
