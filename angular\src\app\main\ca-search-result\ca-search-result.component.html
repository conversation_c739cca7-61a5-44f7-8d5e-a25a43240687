<div class="row-flex-space ess-sr-container app-ca-search-result-container">
    <div class="ess-sr-grid col-flex">
        <div class="row-flex-space">
            <p class="ess-title">{{l("ES Search")}}</p>
            <div class="row-flex-end">
                <div class="row-flex-align-center">
                    <p-overlayPanel #colorLegend class="legend-overlay">
                        <div class="col-flex-center">
                            <li *ngFor="let colorCode of colorCodeList" class="add-padding"
                                [style.color]="colorCode.color">
                                {{colorCode.name}}
                            </li>
                            <li class="add-padding" style='color:red;text-decoration:line-through'>
                                {{l('Deleted')}}
                            </li>
                        </div>
                    </p-overlayPanel>
                    <p style="font-size:12px; color: black">Legend</p>
                    <i class="pi pi-info-circle" style="font-size:24px;" (mouseenter)="colorLegend.show($event)"
                       (mouseleave)="colorLegend.hide($event)"></i>
                </div>
                <button id="newSearch" pButton type="button" (click)="newSearch()" label="{{l('New Search')}}"
                        class="ui-button-rounded ui-button-warning"></button>
            </div>
        </div>
        <div class="row-flex-space margin-top">
            <div>{{(viewModelObservable | async).searchCriteria}}</div>
            <div *ngIf="(viewModelObservable | async).showPeriodEnd">{{l("ES Period End")}}:</div>
            <div *ngIf="(viewModelObservable | async).showPeriodEnd">{{(viewModelObservable | async).searchYear}}</div>
        </div>
        <div class="margin-top">
            <app-ca-search-result-table [resultItems]="(viewModelObservable | async).resultItems" (setColorCodeList)="setColorCodes($event)">
            </app-ca-search-result-table>
        </div>
    </div>
    <div class="ess-sr-details col-flex">
        <div *ngIf="!((viewModelObservable | async).resultItems.selectedEntityObservable | async)">
            <p class="ess-title">{{l("Entity Details")}}</p>
            <hr />
            <p>Select an entity on the left to view its details.</p>
        </div>

        <div *ngIf="(viewModelObservable | async).resultItems.selectedEntityObservable | async">
            <div class="ess-sr-details-entity col-flex">
                <p class="ess-title">{{l("Entity Details")}}</p>
                <hr />
                <div class="ess-sr-details-entity-body">
                    <app-entity-display [entity]="(viewModelObservable | async).resultItems.selectedEntityObservable | async"></app-entity-display>
                </div>
            </div>

            <div class="ess-sr-details-es col-flex">
                <hr />
                <p class="ess-title">{{l("Economic Substance Declarations History and Status")}}</p>
                <hr />
                <div class="ess-sr-details-es-body">
                    <app-ca-declaration-history [entity]="(viewModelObservable | async).resultItems.selectedEntityObservable | async"
                                                [declarations]="(viewModelObservable | async).resultItems.declarationsObservable | async">
                    </app-ca-declaration-history>
                </div>
            </div>
        </div>
    </div>
</div>

<entityChangeHistoryModal #entityChangeHistoryModal [hideIdentifier]="true"></entityChangeHistoryModal>
