<div bsModal #entitymodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="entitymodal"
    (onShown)="shown()" aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                   {{entityType}} {{l('ESParentDetails')}}
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #entityform="ngForm">
                    <p-card>
                        <ngFormValidation #entityFormValidation [extraValidation]="extraValidation"></ngFormValidation>
                        <div class="row-flex-justified-rows">
                            <div class="row-flex-justified-largefont margin-left">
                                <div>{{entityType}} {{l('ESParentC1')}}<span class="required">*</span></div>
                            </div>
                            <div class="row-justified margin-left">
                                <div *ngIf="!importOnlyMode">
                                    <input pInputText type="text" class="form-control" 
                                         [(ngModel)]="details.name"
                                        id="parentNameId" name="Name" required [appNameLength]="100">
                                </div>

                                <div *ngIf="importOnlyMode">
                                    <input pInputText type="text" class="form-control"
                                        [(ngModel)]="details.name" id="parentNameIdImport"
                                        name="Name" required>
                                </div>

                            </div>                          
                        </div>
                        <div class="row-flex-justified-rows">
                            <div class="row-flex-justified-largefont margin-left">
                                <div>{{entityType}} {{l('ESParentC2')}}</div>
                            </div>
                            <div class="row-justified margin-left">
                                <div *ngIf="!importOnlyMode">
                                    <input pInputText type="text" class="form-control" 
                                        [(ngModel)]="details.alternativeName"
                                        id="parentAlternativeNameId" maxlength="100"
                                        name="AlternativeName">
                                </div>
                                <div *ngIf="importOnlyMode">
                                    <input pInputText type="text" class="form-control"
                                        [(ngModel)]="details.alternativeName"
                                        id="parentAlternativeNameIdImport"
                                        name="AlternativeName">
                                </div>
                            </div>                           
                        </div>
                        <div class="row-flex-justified-rows">
                            <div class="row-flex-justified-largefont margin-left">
                                <div>{{entityType}} {{l('ESParentC3')}}<span class="required">*</span></div>
                            </div>
                            <div class="row-justified margin-left">
                                <p-dropdown [options]="listOfCountry" optionLabel="countryName"
                                    [(ngModel)]="details.jurisdiction"
                                    id="parentJurisdictionId" name="Jurisdiction"
                                    (onChange)="setJurisdictionId($event)" >
                                </p-dropdown>
                            </div>                            
                        </div>
                        <div class="row-flex-justified-rows">
                            <div class="row-flex-justified-largefont margin-left">
                                <div>{{entityType}} {{l('ESParentC4')}}<span class="required">*</span></div>
                            </div>
                            <div class="row-justified margin-left">
                                <div *ngIf="!importOnlyMode">
                                    <input pInputText type="text" class="form-control" maxlength="100"
                                        [(ngModel)]="details.incorporationNumber"
                                        id="parentIncorporationNumberId"
                                        name="IncorporationNumber" required>
                                </div>

                                <div *ngIf="importOnlyMode">
                                    <input pInputText type="text" class="form-control"
                                        [(ngModel)]="details.incorporationNumber"
                                        id="parentIncorporationNumberIdImport"
                                        name="IncorporationNumber" required>
                                </div>
                            </div>                           
                        </div>
                        <div class="row-flex-justified-rows">
                            <div class="row-flex-justified-largefont margin-left">
                                <div>{{entityType}} {{l('ESParentC5')}}<span class="required">*</span></div>
                            </div>
                            <div class="row-justified margin-left">
                                <div *ngIf="!importOnlyMode">
                                    <input pInputText type="text" class="form-control" maxlength="100" 
                                        [(ngModel)]="details.identificationNumber"
                                        id="parentIdentificationNumberId"
                                        name="IdentificationNumber" required>
                                </div>

                                <div *ngIf="importOnlyMode">
                                    <input pInputText type="text" class="form-control"
                                        [(ngModel)]="details.identificationNumber"
                                        id="parentIdentificationNumberIdImport"
                                        name="IdentificationNumber" required>
                                </div>

                            </div>                            
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>