import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router'

@Component({
  selector: 'app-entity-edit-page',
  templateUrl: './entity-edit-page.component.html',
  styleUrls: ['./entity-edit-page.component.css']
})
export class EntityEditPageComponent implements OnInit {

  entityId: string

  constructor(private router: Router, private route: ActivatedRoute) {
    this.route.params.subscribe(x => {
      this.entityId = x.id
    })
  }

  ngOnInit() {
  }

  handleCancel() {
    console.log('add new entity cancelled')
  }

  handleReview(id) {
    this.router.navigate(['../../searchresult'], {
      relativeTo: this.route, queryParams: {
        type: "SingleEntity",
        id: id
      }
    })
  }
}
