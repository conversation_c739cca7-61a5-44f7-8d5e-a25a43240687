import { Component, OnInit, Injector, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CaNonResidentReportViewModel } from '../viewmodel/ca-non-resident-report-viewmodel';
import { CASearchServiceServiceProxy, CaNonResidentReportItem } from '@shared/service-proxies/service-proxies';
import { Router, ActivatedRoute } from '@angular/router';
import * as moment from 'moment';

@Component({
  selector: 'app-ca-non-resident-report',
  templateUrl: './ca-non-resident-report.component.html',
  styleUrls: ['./ca-non-resident-report.component.css']
})

export class CaNonResidentReportComponent extends AppComponentBase implements OnInit {

  @Input() viewModel: CaNonResidentReportViewModel

  constructor(injector: Injector, private router: Router, private route: ActivatedRoute) {
    super(injector)
  }

  ngOnInit() {
  }

  showSearchResult(item: CaNonResidentReportItem) {
    this.router.navigate(['../caesssearch'], {
      relativeTo: this.route,
      queryParams: {
        type: "nonresident",
        startDate: moment(item.startDate).format("YYYY-MM-DD"),
        endDate: moment(item.endDate).format("YYYY-MM-DD"),
        country: item.countryCode
      }
    })
  }

  getSearchResultLink(item: CaNonResidentReportItem) {
    const start = moment(item.startDate).format("YYYY-MM-DD")
    const end = moment(item.endDate).format("YYYY-MM-DD")
    return `/app/main/caesssearch?type=nonresident&year=${this.viewModel.year}&startDate=${start}&endDate=${end}&country=${item.countryCode}`
  }
}
