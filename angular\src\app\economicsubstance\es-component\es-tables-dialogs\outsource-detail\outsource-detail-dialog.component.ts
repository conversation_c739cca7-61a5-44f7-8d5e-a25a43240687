import { Component, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CountryDto, ServiceProvidersDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../../shared/utils/validation/ng-form-validation.component';

@Component({
  selector: 'app-outsource-detail-dialog',
  templateUrl: './outsource-detail-dialog.component.html',
  styleUrls: ['./outsource-detail-dialog.component.css']
})
export class OutsourceDetailDialogComponent extends AppComponentBase implements OnInit {

    @Output() submitted: EventEmitter<ServiceProvidersDto> = new EventEmitter<ServiceProvidersDto>();
    details: ServiceProvidersDto = new ServiceProvidersDto();
    country: CountryDto = new CountryDto();
    importOnlyMode: boolean;

    @ViewChild('outsourcemodal', { static: true }) modal: ModalDirective;
    @ViewChild('outsourceform', { static: true }) outsourceform: FormGroup;
    @ViewChild('outsourceFormValidation', { static: true }) outsourceFormValidation: NgFormValidationComponent;

    constructor(injector: Injector) { super(injector); }

    ngOnInit()
    {
        this.outsourceFormValidation.formGroup = this.outsourceform;
    }
    shown(): void {
    }
    show(item, country, isNew, importOnlyMode): void
    {
        this.importOnlyMode = importOnlyMode;

        this.country = _.cloneDeep(country);
        this.details = new ServiceProvidersDto();
        if (!isNew) this.details = _.cloneDeep(item);
        this.modal.show();
    }

    close(): void {
        this.outsourceform.reset();
        this.modal.hide();
    }

    save(): void
    {
        if (!this.outsourceFormValidation.isFormValid()) {
            return;
        }

        if (!this.details.id) {
            this.details.id = uuid.v4();
            this.details.isNew = true;
        }
        this.submitted.emit(this.details);
        this.close();

    }

}
