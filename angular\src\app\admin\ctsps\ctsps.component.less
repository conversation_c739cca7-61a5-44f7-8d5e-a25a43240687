.green-color-icon {
    color: green !important;
    font-size: 2rem !important;
}

.red-color-icon {
    color: red !important;
    font-size: 2rem !important;
}

.ess-csp-grid,
.ess-csp-details {
    padding: 1em;
}

.ess-csp-grid {
    .ess-csp-form-control {
        width: 150px;

        + .ess-csp-form-control,
        + .ess-csp-form-item {
            margin-left: 10px;
        }
    }
}

.ess-csp-details {
    background-color: #f3fafd;

    .ess-csp-details-container {
        margin-top: 10px;
        width: 500px;
        height: calc(100vh - 130px);
        overflow-y: auto;
    }

    .ess-csp-form-control {
        label {
            width: 30%;
            padding: 0.8rem 0;
        }

        input,
        textarea {
            width: 70%;
            margin: 5px auto;
        }

        textarea {
            resize: none;
        }
    }
}

.ess-csp-pager {
    margin-top: 5px;

    .ui-paginator {
        background-color: white;

        .ui-paginator-page.ui-state-active {
            color: white !important;
        }
    }
}
