using Bdo.Ess.Common.Audit;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;

namespace Bdo.Ess.Services.Audit
{
    /// <summary>
    /// Base class for when source types are the same
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public abstract class AuditChangeTrackerBase<T> : AuditChangeTrackerBaseCore<T, T>
    {
        protected abstract Func<T, string> IdentifierFunc { get; }
        protected sealed override Func<T, string> OldIdentifierFunc => IdentifierFunc;
        protected sealed override Func<T, string> NewIdentifierFunc => IdentifierFunc;

        protected virtual IAuditMember<T>[] Members => new IAuditMember<T>[0];
        protected sealed override IAuditMember<T, T>[] IntMembers => Members;
    }

    /// <summary>
    /// Base class for when source types are different
    /// </summary>
    /// <typeparam name="TOld"></typeparam>
    /// <typeparam name="TNew"></typeparam>
    public abstract class AuditChangeTrackerBase<TOld, TNew> : AuditChangeTrackerBaseCore<TOld, TNew>
    {
        protected virtual IAuditMember<TOld, TNew>[] Members => new IAuditMember<TOld, TNew>[0];
        protected sealed override IAuditMember<TOld, TNew>[] IntMembers => Members;
    }

    /// <summary>
    /// Base class for defining audit behaviour, contains the core logic
    /// </summary>
    /// <typeparam name="TOld"></typeparam>
    /// <typeparam name="TNew"></typeparam>
    public abstract class AuditChangeTrackerBaseCore<TOld, TNew>
    {
        protected string Key { get; private set; }
        protected TOld OldObj { get; private set; }
        protected TNew NewObj { get; private set; }
        protected AuditAction? Action { get; private set; }

        protected abstract string IdentifierName { get; }
        protected abstract Func<TOld, string> OldIdentifierFunc { get; }
        protected abstract Func<TNew, string> NewIdentifierFunc { get; }

        protected abstract IAuditMember<TOld, TNew>[] IntMembers { get; }

        public object GetChanges(string key, TOld oldObj, TNew newObj, AuditAction? action = null)
        {
            Key = key;
            OldObj = oldObj;
            NewObj = newObj;
            Action = action;

            return Execute();
        }

        private object Execute()
        {
            var changes = new ExpandoObject();
            var diffCtr = 1;

            var oldIdentifier = OldObj != null ? OldIdentifierFunc(OldObj) ?? string.Empty : string.Empty;
            var newIdentifier = NewObj != null ? NewIdentifierFunc(NewObj) ?? string.Empty : string.Empty;
            var auditAction = Action ?? (OldObj == null ? AuditAction.Create : NewObj == null ? AuditAction.Delete : AuditAction.Update);
            var auditActionString = ((int)auditAction).ToString();

            changes.SetKey(Key.ToString());
            changes.SetAction(auditActionString);
            changes.SetIdentifiers(IdentifierName, oldIdentifier, newIdentifier);

            foreach (var member in IntMembers)
            {
                var field = member as IAuditField<TOld, TNew>;

                if (field != null)
                {
                    var oldValue = field.GetOldValue(OldObj);
                    var newValue = field.GetNewValue(NewObj);

                    changes.AddFieldChange(diffCtr++, field.Name, oldValue, newValue);
                }

                var child = member as IAuditChild<TOld, TNew>;

                if (child != null)
                {
                    var oldChildren = child.GetOldChildren(OldObj);
                    var newChildren = child.GetNewChildren(NewObj);
                    var oldChildrenKeys = new HashSet<string>(oldChildren.Select(x => x.Key));

                    var changeTracker = Activator.CreateInstance(child.ChangeTrackerType, args: child.ConstructorParameters);
                    var method = changeTracker
                        .GetType()
                        .GetMethod(nameof(AuditChangeTrackerBaseCore<TOld, TNew>.GetChanges), BindingFlags.Public | BindingFlags.Instance);
                    var childrenChanges = new List<ExpandoObject>();

                    foreach (var oldChildObj in oldChildren)
                    {
                        var key = oldChildObj.Key;
                        var oldChild = oldChildObj.Object;
                        var newChild = newChildren.FirstOrDefault(x => x.Key == key)?.Object;
                        var childChanges = method.Invoke(changeTracker, new object[] { key, oldChild, newChild, null }) as ExpandoObject;

                        if (child.UseSequenceNumberAsIdentifier)
                        {
                            var childIdentifierName = childChanges.GetIdentifierName();
                            var newChildIdentifier = $"{childIdentifierName} {childrenChanges.Count + 1}";
                            childChanges.SetIdentifiers(childIdentifierName, newChildIdentifier, newChildIdentifier);
                        }

                        childrenChanges.Add(childChanges);
                    }

                    foreach (var newChildObj in newChildren.Where(x => !oldChildrenKeys.Contains(x.Key)))
                    {
                        var key = newChildObj.Key;
                        var newChild = newChildObj.Object;
                        var childChanges = method.Invoke(changeTracker, new object[] { key, null, newChild, null }) as ExpandoObject;

                        if (child.UseSequenceNumberAsIdentifier)
                        {
                            var childIdentifierName = childChanges.GetIdentifierName();
                            var newChildIdentifier = $"{childIdentifierName} {childrenChanges.Count + 1}";
                            childChanges.SetIdentifiers(childIdentifierName, newChildIdentifier, newChildIdentifier);
                        }

                        childrenChanges.Add(childChanges);
                    }

                    changes.AddChildrenDiff(diffCtr++, child.Name, childrenChanges);
                }
            }

            return changes;
        }
    }
}
