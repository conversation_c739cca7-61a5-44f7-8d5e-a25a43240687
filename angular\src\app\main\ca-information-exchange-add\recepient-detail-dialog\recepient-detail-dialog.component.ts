import { Component, OnInit, Injector, Output, ViewChild, EventEmitter } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap';
import { FormControl, FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { NgFormValidationComponent } from '@shared/utils/validation/ng-form-validation.component'
import { RecipientDetailsDto, AddressIE, CountryDto, InformationExchangeServiceProxy, EntityPersonType } from '@shared/service-proxies/service-proxies'; 
import { UtilityComponent } from '@app/shared/common/general/utility'
import * as _ from 'lodash';
import * as uuid from 'uuid';
import { AllRequestStatusesInclAny, AllInformationExchangeStatus, Status, AllUBOStatus, AllUBOStatusInclAny, AllEntityPeronStatus } from "@app/shared/common/informationexchangestatus/informationexchange-status"

@Component({
  selector: 'recepientdialog',
  templateUrl: './recepient-detail-dialog.component.html',
  styleUrls: ['./recepient-detail-dialog.component.css']
})
export class RecepientDetailDialogComponent extends AppComponentBase implements OnInit
{

    @Output() submitted: EventEmitter<RecipientDetailsDto> = new EventEmitter<RecipientDetailsDto>();

    @ViewChild('recipientmodal', { static: true }) recipientmodal: ModalDirective;
    @ViewChild('recipientForm', { static: true }) recipientform: FormGroup;
    @ViewChild('recepientFormValidation', { static: true }) recipientFormValidation: NgFormValidationComponent;

    extraValidation: any[] = [];
    validationFields: any[] = [];

    boStatuses;
    selectedboStatus;
    entityStatus;
    selectedEntityStatus:Status;
    detail: RecipientDetailsDto = new RecipientDetailsDto();
    countries: any;
    constructor(injector: Injector,
        private _informationExchangeServiceProxy: InformationExchangeServiceProxy,
        private _utlity: UtilityComponent) {
        super(injector);

        this.detail = new RecipientDetailsDto();
        this.detail.address = new AddressIE();
        this.detail.address.country = new CountryDto();
        this.detail.jurisdictionResidence = new CountryDto();
        this.detail.tinIssuedCoutry = new CountryDto();
        
        this.entityStatus = AllEntityPeronStatus;
        this.boStatuses = AllUBOStatusInclAny;

        this.selectedEntityStatus = AllEntityPeronStatus.find(x => x.value === 0);
        _informationExchangeServiceProxy.getAllCountries(null).subscribe(x => {
            this.countries = this._utlity.sort_country(x);
        });

    }

    ngOnInit()
    {
        this.recipientFormValidation.formGroup = this.recipientform;
    }

    shown(): void { }

    show(item?: any)
    {
        this.extraValidation = [];
        this.validationFields = [];
        
        this.selectedEntityStatus = AllEntityPeronStatus.find(x => x.value === 0);
        this.selectedboStatus = AllUBOStatusInclAny.find(x => x.value === -1);
        this.detail = new RecipientDetailsDto();
        this.detail.address = new AddressIE();
        this.detail.address.country = new CountryDto();
        this.detail.jurisdictionResidence = new CountryDto();
        this.detail.tinIssuedCoutry = new CountryDto();


        if (item) {

            this.detail = _.cloneDeep(item);
            this.selectedEntityStatus = AllEntityPeronStatus.find(x => x.value === this.detail.typeOfEntityPerson);
        
            if (this.detail.typeOfEntityPerson === 0) {
                this.selectedboStatus = this.boStatuses.find(x => x.value === this.detail.uboType);
               
            }
            this.detail.jurisdictionResidence = this.countries.find(x=>x.countryCode == this.detail.jurisdictionResidence.countryCode)
            this.recipientmodal.show();
        }
            else {
                this.selectedEntityStatus = AllEntityPeronStatus.find(x => x.value === 0);
                this.recipientmodal.show();
            }
        
    }
    close(): void
    {
        this.recipientform.reset();
        this.recipientmodal.hide();
    }

    hasError(fieldName: string): boolean {

        return this.recipientFormValidation.fieldHasErrors(fieldName);
    }

    hasCustomError(fieldName: string)
    {
        return this.validationFields && this.validationFields.find(x => x === fieldName);
    }

    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }


    checkHaveUBO(): boolean{        
        this.extraValidation = [];
        this.validationFields = [];

        if (this.IsNullorUndefined(this.detail.address.country.countryCode) || this.detail.address.country.countryCode === "N/A")
        {
            this.extraValidation.push("Country is Required ");
            this.validationFields.push("Country");
        }

        if (this.detail.tin && this.IsNullorUndefined(this.detail.tinIssuedCoutry.countryCode) || this.detail.tinIssuedCoutry.countryCode === "N/A") {
            this.extraValidation.push("Issued Country is Required ");
            this.validationFields.push("IssuedCountry");
        }

        if (this.IsNullorUndefined(this.detail.jurisdictionResidence.countryCode) || this.detail.jurisdictionResidence.countryCode === "N/A") {
            this.extraValidation.push("Jurisdiction Country Required ");
            this.validationFields.push("Jurisdiction");
        }

        if (this.selectedEntityStatus.value === EntityPersonType.UltimateBO && this.selectedboStatus.value === -1) {
            this.extraValidation.push("UBO Type is Required ");
            this.validationFields.push("recipient");

        }

        if (this.extraValidation.length > 0) return false; else return true;
        }
    
   
    save(): void {
        
            if (!this.checkHaveUBO() ||!this.recipientFormValidation.isFormValid(this.extraValidation)) {
                return;
            }
            else {
                this.detail.typeOfEntityPerson = this.selectedEntityStatus.value;
                this.detail.uboType = this.selectedboStatus ? this.selectedboStatus.value : null;
                if (!this.detail.id) {
                    this.detail.id = uuid.v4();
                }
                this.submitted.emit(this.detail);
                this.recipientmodal.hide();
                this.close();
            }
        
    }
}
