import { Component, EventEmitter, Injector, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CtspListDto, CtspServiceProxy } from '@shared/service-proxies/service-proxies';
import { NgFormValidationComponent } from '@shared/utils/validation/ng-form-validation.component';
import { CountryISO, SearchCountryField, TooltipLabel } from 'ngx-intl-tel-input';


@Component({
  selector: 'app-edit-ctsp',
  templateUrl: './edit-ctsp.component.html',
  styleUrls: ['./edit-ctsp.component.css']
})
export class EditCtspComponent extends AppComponentBase {

    ctspDetail: CtspListDto = new CtspListDto();
    phoneNumber: any;
    itPhoneNumber: any;
    isEditMode = false;
    @Output() onSave: EventEmitter<any> = new EventEmitter<any>();
    originalCtspDetail: CtspListDto= new CtspListDto();
    saving = false;
    phoneNumberInput: any;
    @ViewChild('ctspFormValidation', { static: true }) ctspFormValidation: NgFormValidationComponent;
    @ViewChild('ctspForm', { static: true }) ctspForm: FormGroup;
    SearchCountryField = SearchCountryField;
    TooltipLabel = TooltipLabel;
    CountryISO = CountryISO;
    selectedCountryISO: string = CountryISO.BritishVirginIslands;
    preferredCountries: CountryISO[] = [CountryISO.BritishVirginIslands];
    ngOnInit() {
        this.ctspFormValidation.formGroup = this.ctspForm;
       
    }
    
    constructor(injector: Injector, private _ctspService: CtspServiceProxy)
    {
        super(injector);
    }

    get(ctspId?:number)
    {
        this._ctspService.getCtspDetailById(ctspId).subscribe(result =>
        {
            this.ctspDetail = result;
            Object.assign(this.originalCtspDetail, result);
            this.phoneNumber = result.phoneNumber;
            this.itPhoneNumber = result.itPhoneNumber;
        });
    }
    onEdit() {
        this.isEditMode = true;
    }
    
    hasError(fieldName: string): boolean {
        return this.ctspFormValidation.fieldHasErrors(fieldName);
    }

    onCancel() {
        Object.assign(this.ctspDetail, this.originalCtspDetail);
        this.phoneNumber = this.originalCtspDetail.phoneNumber;
        this.itPhoneNumber = this.originalCtspDetail.itPhoneNumber;
        this.isEditMode = false;
      }
    Save() {
        if (!this.ctspFormValidation.isFormValid()) {
            return;
        }
        if (this.phoneNumber && this.phoneNumber.internationalNumber) {
            this.ctspDetail.phoneNumber = this.phoneNumber.internationalNumber;
        }
        else{
            this.ctspDetail.phoneNumber = null;
        }
        
        if (this.itPhoneNumber && this.itPhoneNumber.internationalNumber) {
            this.ctspDetail.itPhoneNumber = this.itPhoneNumber.internationalNumber;
        }
        else{
            this.ctspDetail.itPhoneNumber = null;
        }
        this._ctspService.updateCtspInformation(this.ctspDetail).subscribe(result =>
            {
                this.onSave.emit();
                this.get(this.ctspDetail.id);
                this.notify.success(this.l('SavedSuccessfully'));
               
            });
        this.isEditMode = false;
    }
    
}
