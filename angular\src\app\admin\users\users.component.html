<div class="row-flex-space" [@routerTransition]>
    <div class="ess-um-grid col-flex">
        <div class="row-flex">
            <p class="ess-title">{{l("Users")}}</p>
            <div style="width:100%;margin-left:40px">
                <button id="addUser" pButton type="button" (click)="createUser()" label="{{l('Add User')}}">
                </button>
            </div>

            <div class="row-flex-align-center">
                <div class="ess-um-form-control">
                    <input id="addUserName" [(ngModel)]="filterText" name="filterText"
                           class="form-control" placeholder="Name" type="text">
                </div>
                <div class="ess-um-form-control">
                    <p-dropdown id="addUserStatus" [options]="statuses"
                                [(ngModel)]="status" name="Statuses">
                    </p-dropdown>
                </div>
                <div class="ess-um-form-item">
                    <button id="addUserApply" pButton type="button" (click)="getUsers()" label="Apply"></button>
                </div>
            </div>
        </div>
        <div class="ess-um-grid-container">
            <div class="primeng-datatable-container" [busyIf]="primengTableHelper.isLoading">
                <p-table #dataTable (onLazyLoad)="getUsers($event)" [value]="primengTableHelper.records"
                         [lazy]="true" [scrollable]="true" scrollHeight="calc(100vh - 175px)"
                         selectionMode="single" (onRowSelect)="onRowSelect($event)">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 40px" pSortableColumn="Name">
                                {{'FirstName' | localize}}
                                <p-sortIcon id="sortFirstName" field="Name"></p-sortIcon>
                            </th>
                            <th style="width: 40px" pSortableColumn="Surname">
                                {{'LastName' | localize}}
                                <p-sortIcon id="sortLastName" field="Surname"></p-sortIcon>
                            </th>
                            <th style="width: 75px" pSortableColumn="EmailAddress">
                                {{'Email' | localize}}
                                <p-sortIcon id="sortEmail" field="EmailAddress"></p-sortIcon>
                            </th>
                            <th style="width: 30px" pSortableColumn="Status">
                                {{'Status' | localize}}
                                <p-sortIcon id="sortStatus" field="Status"></p-sortIcon>
                            </th>
                            <th style="width: 60px" pSortableColumn="SortingRoleName">
                                {{'Role' | localize}}
                                <p-sortIcon id="sortRole" field="SortingRoleName"></p-sortIcon>
                            </th>
                            <th style="width: 50px">
                                {{'Phone' | localize}}
                            </th>
                            <th style="width: 40px">
                                {{'Actions' | localize}}
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-record="$implicit">
                        <tr [pSelectableRow]="record">
                            <td style="width: 40px">
                                {{record.firstName}}
                            </td>
                            <td style="width: 40px">
                                {{record.lastName}}
                            </td>
                            <td style="width: 75px">
                                {{record.emailAddress}}
                            </td>
                            <td style="width: 30px">
                                <span *ngIf="record.isActive && !record.isLocked">{{'Active' | localize}}</span>
                                <span *ngIf="record.isActive && record.isLocked">{{'Locked' | localize}}</span>
                                <span *ngIf="!record.isActive">{{'Inactive' | localize}}</span>
                            </td>
                            <td style="width: 60px">
                                {{record.role}}
                            </td>
                            <td style="width: 50px">
                                {{record.phoneNumber}}
                            </td>
                            <td style="width: 40px">
                                <div *ngIf="record.isActive">
                                    <i class="pi pi-trash ess-um-grid-button" title="Remove User" (click)="deleteUser(record); $event.stopPropagation()"></i>
                                    <i *ngIf="record.isLocked" class="pi pi-unlock ess-um-grid-button" title="Unlock User" (click)="unlockUser(record); $event.stopPropagation()"></i>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                    {{'NoData' | localize}}
                </div>
            </div>
        </div>
    </div>
    <createOrEditUser #createOrEditUser (onSave)="getUsers()"></createOrEditUser>
</div>
