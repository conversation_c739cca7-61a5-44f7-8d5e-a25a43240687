<div *ngIf="!hidden" class="col-flex-center">
    <div class="col-flex margin-top">
        <label>{{l("CTSP Unique Client #")}}</label>
        <div>
            <input type="text" name="entityNumber" class="form-control" pInputText
                [(ngModel)]="entitySearchInput.entityNumber">
        </div>
    </div>
    <div class="col-flex margin-top">
        <label>{{l("Company Number")}}</label>
        <div>
            <input type="text" name="companyNumber" class="form-control" pInputText
                [(ngModel)]="entitySearchInput.companyNumber">
        </div>
    </div>
    <div class="col-flex margin-top">
        <label>{{l("Entity Name")}}</label>
        <div>
            <input type="text" name="entityName" class="form-control" pInputText
                [(ngModel)]="entitySearchInput.entityName">
        </div>
    </div>
    <div class="col-flex margin-top">
        <label>{{l("Entity Status")}}</label>
        <div>
            <p-dropdown name="requireDeclaration" [options]="statuses"
                [(ngModel)]="entitySearchInput.requireDeclaration"></p-dropdown>
        </div>
    </div>
    <!-- Done in parent component?
    <div class="col-flex margin-top">
        <label>{{l("Entity Search Notes")}}</label>
    </div>
    -->
</div>