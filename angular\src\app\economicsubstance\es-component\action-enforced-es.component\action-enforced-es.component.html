<div class="col-flex">
    <!--Enforced Action-->
    <p-card [ngClass]="{'p-card-properties_form margin-top':!readOnlyMode,'p-card-properties_form_readonly margin-top':readOnlyMode}">
      
            <div class="row-flex-justified ">
                <label class="ac-pheader"> {{l('ActionEnforcedTitle')}}</label>
            </div>

            <div class="row-flex-justified">
                <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true"
                          [(ngModel)]="esassessment.actionEnforced"
                          style="background-color : #f0f0f0 !important" rows="3"></textarea>
            </div>

            <app-upload-files [Documents]="esassessment.esEnforcementDocuments"
                              [DocumentTypeName]="EnforcmentDocumentDoc"
                              [IsEconomicDocument]="false"
                              [readOnlyMode]="true"
                              [displayFromCa]="displayFromCa"
                              [ctspId]="ctspId">
            </app-upload-files>
              
    </p-card>
</div>
