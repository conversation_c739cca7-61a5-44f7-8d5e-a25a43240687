import { NgModule } from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { UsersComponent } from './users/users.component';
import { MyProfileComponent } from './my-profile/my-profile.component';
import { CtspsComponent } from './ctsps/ctsps.component';
import { RedFlagsComponent } from './red-flags/red-flags.component';
import { AuditComponent } from './audit/audit.component';
import { SecurityQuestionsComponent } from './security-questions/security-questions.component';
import { ChangePasswordComponent } from './change-password/change-password.component';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                children: [
                    { path: 'users', component: UsersComponent, data: { permissions: ['Ctsp.User', 'Ca.User'] } },
                    { path: 'myProfile', component: MyProfileComponent, data: { permissions: ['Ctsp', 'Ca'] } },
                    { path: 'securityQuestions', component: SecurityQuestionsComponent, data: { permissions: ['Ctsp', 'Ca'] } },
                    { path: 'changePassword', component: ChangePasswordComponent, data: { permissions: ['Ctsp', 'Ca'] } },
                    { path: 'ctsps', component: CtspsComponent, data: { permissions: ['Ca.Ctsp'] } },
                    { path: 'redFlags', component: RedFlagsComponent, data: { permissions: ['Ca.RedFlagSettings'] } },
                    { path: 'audit', component: AuditComponent, data: { permissions: ['Ctsp.Audit', 'Ca.Audit'] } },
                ]
            }
        ])
    ],
    exports: [
        RouterModule
    ]
})
export class AdminRoutingModule {

    constructor(
        private router: Router
    ) {
        router.events.subscribe((event) => {
            if (event instanceof NavigationEnd) {
                window.scroll(0, 0);
            }
        });
    }
}
