import { Component, OnInit, Output, Input, EventEmitter } from '@angular/core';
import { EconomicSubstanceServiceProxy, FileParameter, DocumentsDto, DocumentOutputDto} from '@shared/service-proxies/service-proxies';

@Component({
  selector: 'upload-document',
  templateUrl: './upload-doument.component.html',
  styleUrls: ['./upload-doument.component.css']
})
export class UploadDoumentComponent implements OnInit
{
    @Input() CtspId: string;
    @Output() itemUploaded: EventEmitter<DocumentOutputDto[]> = new EventEmitter<DocumentOutputDto[]>();
    constructor(private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy) { }
    uploadedFiles: DocumentOutputDto[] = [];
    nonUploadedFile: string[] = [];
    ngOnInit()
    {
       this.uploadedFiles = [];
    }
  

   

    myUploader(event) {

        this.uploadedFiles = [];
        this.nonUploadedFile = [];

        let Uploadedlength = event.files.length;

        
        if (this.CheckAllFile(event))
        {
           
            this.UploadCompleted(event, false);
        }

         else {
            for (let i = 0; i < event.files.length; i++)
            {
                if (event.files[i].size > 10485760)
                {
            
                    Uploadedlength = Uploadedlength - 1;
                }
                else {
                    let myObj = { data: event.files[i], fileName: event.files[i].name };

                    this._economicSubstanceServiceProxy.addDocument(myObj, this.CtspId)
                        .subscribe(
                            {
                                next: (data) => {
                                    this.uploadedFiles.push(data);

                                    // need to add it to step1 

                                },
                                error: (err: any) => {
                                    //  finallyCallback();
                                },
                                complete: () => {
                                    for (let j = 0; j < event.files.length; j++) {
                                        if (event.files[j].name == myObj.data.name) {
                                            event.files.splice(j, 1);
                                            break;
                                        }
                                    }

                                    if (this.uploadedFiles.length == Uploadedlength) {
                                        this.UploadCompleted(event,true);

                                    }
                                }
                            });
                }
            }
        }
    }

    
    CheckAllFile(event): boolean
    {
        let count = 0;
        for (let i = 0; i < event.files.length; i++)
        {
            if (event.files[i].size > 10485760)
            {
                this.nonUploadedFile.push(event.files[i].name);
                count = count + 1;
            }
        }

        return count === event.files.length;
    }

    UploadCompleted(event,completeUpload): void
    {
        if (completeUpload) this.itemUploaded.emit(this.uploadedFiles);

        if (this.nonUploadedFile.length > 0) {
            let names = this.nonUploadedFile.join(', ');
            abp.message.error("System can't upload the files: " + names + " file size is more than 10 MB");

            for (let i = 0; i < this.nonUploadedFile.length; i++) {
                for (let j = 0; j < event.files.length; j++) {
                    if (event.files[j].name == this.nonUploadedFile[i]) {
                        event.files.splice(j, 1);
                        break;
                    }
                }
            }
        }

    }

}
