<div class="row-flex-space" [@routerTransition]>
    <p-card class="p-card-properties_form">
        <p-header class="row-flex-space">
            <p>{{title}}</p>
            <p *ngIf="createdAt != null" class="updateTime">Generated at {{createdAt | date:'yyyy-MM-dd HH:mm'}}</p>
            <div class="row-flex-justified">
                <button pButton type="button" icon="pi pi-arrow-left" class="margin-left bigger-icons" iconPos="left"
                        (click)="goToRedFlagsDashboard($event)" tooltip="{{'Back' | localize}}"></button>
            </div>
        </p-header>
        <div class="margin-top">
            <div class="primeng-datatable-container" [busyIf]="primengTableHelper.isLoading">
                <p-table #dataTable (onLazyLoad)="getResults($event)" [value]="primengTableHelper.records"
                         rows="{{primengTableHelper.defaultRecordsCountPerPage}}" [paginator]="false"
                         [lazy]="true" [scrollable]="true" ScrollWidth="100%"
                         selectionMode="single" (onRowSelect)="onRowSelect($event)">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 50px" pSortableColumn="CorporateEntityName">
                                {{'Entity Name' | localize}}
                                <p-sortIcon field="CorporateEntityName"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="CorporateEntityNumber">
                                {{'Incorp./Formation #' | localize}}
                                <p-sortIcon field="CorporateEntityNumber"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="CtspNumber">
                                {{'CSP' | localize}}
                                <p-sortIcon field="CtspNumber"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="FiscalYear">
                                {{' Financial Period End' | localize}}
                                <p-sortIcon field="FiscalYear"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="SubmittedAt">
                                {{'Submitted' | localize}}
                                <p-sortIcon field="SubmittedAt"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="AssessmentStatus">
                                {{'Assessment Status' | localize}}
                                <p-sortIcon field="AssessmentStatus"></p-sortIcon>
                            </th>
                            <th style="width: 50px">
                                {{'Review' | localize}}
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-record="$implicit">
                        <tr [pSelectableRow]="record">
                            <td style="width: 50px">
                                {{record.corporateEntityName}}
                            </td>
                            <td style="width: 50px">
                                {{record.corporateEntityNumber}}
                            </td>
                            <td style="width: 50px">
                                {{record.ctspName}}
                            </td>
                            <td style="width: 50px">
                                {{record.fiscalYear}}
                            </td>
                            <td style="width: 50px">
                                {{record.submittedAt | date:'yyyy-MM-dd HH:mm'}}
                            </td>
                            <td style="width: 50px">
                                {{record.assessmentStatus}}
                            </td>
                            <td style="width: 50px">
                                <button pButton type="button" icon="pi pi-search"
                                        (click)="goToReview(record)" iconPos="left"></button>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                    {{'NoData' | localize}}
                </div>
                <div class="primeng-paging-container">
                    <app-custom-paginator #customPagination id="auditFormPaginator"
                            [totalRecordsCount]="primengTableHelper.totalRecordsCount"
                            [defaultRecordsCountPerPage]="primengTableHelper.defaultRecordsCountPerPage"
                            [predefinedRecordsCountPerPage]="primengTableHelper.predefinedRecordsCountPerPage"
                            (pageChange)="getResults($event)"
                            (pageSizeChange)="getResults($event)">
                    </app-custom-paginator>
                </div>
            </div>
        </div>
    </p-card>
</div>
