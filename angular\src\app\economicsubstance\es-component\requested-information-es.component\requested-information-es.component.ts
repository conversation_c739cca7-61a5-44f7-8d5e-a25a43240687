import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, DocumentsDto, EconomicSubstanceInformationRequiredDto } from '@shared/service-proxies/service-proxies';
import { EconomicDocumentsName } from '@app/economicsubstance/EconomicSubstance';
import { NgForm } from '@angular/forms';
import { EconomicSubstanceService} from '@app/economicsubstance/economicsubstance.service';

@Component({
  selector: 'app-requested-information-es',
  templateUrl: './requested-information-es.component.html',
  styleUrls: ['./requested-information-es.component.css']
})
export class RequestedInformationEsComponent extends AppComponentBase implements OnInit {

    @Input() public economicsubstance: EconomicSubstanceDeclarationDto;
    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() ctspId: any;
    InformationRequestedDocumentDoc: any;
    @Input() informationRequired: EconomicSubstanceInformationRequiredDto;
    @Input() informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[];

    @ViewChild('#requestedInfoForm', { static: false }) form: NgForm;
    constructor(injector: Injector,
        public _economicSubstanceService: EconomicSubstanceService
    ) { super(injector); }
    
 
    ngOnInit()
    {
        this.InformationRequestedDocumentDoc = EconomicDocumentsName.InformationRequestedDocumentDoc;        
    }

    returnDate(obj) {
        if (obj) return new Date(obj.format('YYYY-MM-DD HH:mm:ss') + ' UTC');
    }
}
