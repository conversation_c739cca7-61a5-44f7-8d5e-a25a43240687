import { Component, OnInit, ViewEncapsulation, ViewChild, AfterViewInit, Injector } from '@angular/core';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { Table } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { LazyLoadEvent } from 'primeng/api';
import { Router, ActivatedRoute, Params } from '@angular/router';
import * as moment from 'moment';
import { CASearchServiceServiceProxy, CaSearchNonResidentByCountryInput, DashboardSearchResultDto, PagedResultDtoOfDashboardSearchResultDto, CaSearchOverdueFilingsInput, CaSearchNotStartedFilingsInput, PagedResultDtoOfCaEntityListDto } from '@shared/service-proxies/service-proxies';
import { finalize } from 'rxjs/operators';
import { Observable, empty } from 'rxjs';

@Component({
  selector: 'app-ca-entity-search',
  templateUrl: './ca-entity-search.component.html',
  styleUrls: ['./ca-entity-search.component.css'],
  encapsulation: ViewEncapsulation.None,
  animations: [appModuleAnimation()]
})
export class CaEntitySearchComponent extends AppComponentBase implements AfterViewInit, OnInit {
  @ViewChild('dataTable', { static: true }) dataTable: Table;
  @ViewChild('paginator', { static: true }) paginator: Paginator;

  private params: Params
  get title(): string {
    if (this.params.type == "overdue") {
      if (this.params.ctspnumber) {
        return `Overdue in year ${this.params.year} for CSP ${this.params.ctspnumber}`
      }
      return `Overdue in year ${this.params.year}`
    }
    return null
  }

  constructor(
    injector: Injector,
    private router: Router,
    private route: ActivatedRoute,
    private searchService: CASearchServiceServiceProxy) {
    super(injector)

    route.queryParams.subscribe(x => this.params = x);
  }

  ngOnInit() {
  }

  ngAfterViewInit(): void {
    this.primengTableHelper.adjustScroll(this.dataTable);
  }

  getResults(event?: LazyLoadEvent) {
    if (this.primengTableHelper.shouldResetPaging(event)) {
      this.paginator.changePage(0);

      return;
    }

    this.primengTableHelper.showLoadingIndicator();

    let observable: Observable<PagedResultDtoOfCaEntityListDto> = empty()

    if (this.params.type == "overdue") {
      const input = new CaSearchOverdueFilingsInput({
        year: this.params.year,
        ctspNumber: this.params.ctspnumber,
        updateTime: moment(this.params.updatetime),
        maxResultCount: this.primengTableHelper.getMaxResultCount(this.paginator, event),
        skipCount: this.primengTableHelper.getSkipCount(this.paginator, event),
          sorting: this.primengTableHelper.getSorting(this.dataTable),
      })
      // observable = this.searchService.searchOverdueFilings(input)
      observable = this.searchService.searchOverdueFilings(input)
    }

    observable.pipe(
      finalize(() => this.primengTableHelper.hideLoadingIndicator())
    ).subscribe(result => {
      this.primengTableHelper.totalRecordsCount = result.totalCount;
      this.primengTableHelper.records = result.items;
      this.primengTableHelper.hideLoadingIndicator();
    })
  }

  reloadPage(): void {
    this.paginator.changePage(this.paginator.getPage());
  }

  onRowSelect(record: any) {
  }

  goToReview(record: DashboardSearchResultDto): void {
    this.router.navigate(['app/economicsubstance/careview/' + record.economicSubstanceId + '/' + + record.ctspNumber]);
  }

  goToNonResidentDashboard(e: any): void {
    this.router.navigate(['app/main/cadashboard']);
  }
}
