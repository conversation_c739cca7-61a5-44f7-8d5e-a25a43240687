import { Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CountryDto, EsEntityAdditionalDetailDto, ParentEntityType } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../shared/utils/validation/ng-form-validation.component';
import { NameLengthValidatorDirective } from '../../../../shared/utils/validation/name-length-validator.directive'; 


@Component({
    selector: 'app-entity-detail-dialog',
    templateUrl: './entity-detail-dialog.component.html',
    styleUrls: ['./entity-detail-dialog.component.css']
})
export class EntityDetailDialogComponent extends AppComponentBase implements OnInit {

    @Input() public listOfCountry: CountryDto[];
    @Input() entityTypeName: ParentEntityType;
    @Output() submitted: EventEmitter<EsEntityAdditionalDetailDto> = new EventEmitter<EsEntityAdditionalDetailDto>();
    details: EsEntityAdditionalDetailDto = new EsEntityAdditionalDetailDto();

    @ViewChild('entitymodal', { static: true }) modal: ModalDirective;
    @ViewChild('entityform', { static: true }) entityform: FormGroup;
    @ViewChild('entityFormValidation', { static: true }) entityFormValidation: NgFormValidationComponent;
    @ViewChild('NameLength', { static: true }) NameLength: NameLengthValidatorDirective; 
    isFormSubmitted = false;
    importOnlyMode: boolean;
    public entityType: string;
    constructor(injector: Injector) { super(injector); }

    ngOnInit() {
        this.isFormSubmitted = false;
        this.entityFormValidation.formGroup = this.entityform;
        this.entityType = ParentEntityType[this.entityTypeName];
    }
    
    get extraValidation() {
        const errors = [];
        if (this.isFormSubmitted && !this.details.jurisdictionId) {
            errors.push('Jurisdiction field is required');
        }
        return errors;
    }

    shown(): void { }

    show(item, isNew, importOnlyMode): void {

        this.importOnlyMode = importOnlyMode;

        this.details = new EsEntityAdditionalDetailDto();
        this.details.entityType = this.entityTypeName;
        if (!isNew) {
            this.details = _.cloneDeep(item);
        }
        this.modal.show();
    }

    close(): void {
        this.entityform.reset();
        this.isFormSubmitted = false;
        this.modal.hide();
    }

    save(): void {
        this.isFormSubmitted = true;
        if (!this.entityFormValidation.isFormValid() || this.extraValidation.length > 0) {
            return;
        }
        if (!this.details.id) { this.details.id = uuid.v4(); this.details.isNew = true; }
        this.submitted.emit(this.details);
        this.close();

    }

    public setJurisdictionId(item: any): void {
        this.details.jurisdictionId = item.value.id;
    }

}
