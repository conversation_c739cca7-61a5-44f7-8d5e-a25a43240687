import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, EsEntityDetailDto, CountryDto, CorporateEntityDto, ValidationResultMainDto, ValidationResultDto, ValidationType, ParentEntityType, EsEntityAdditionalDetailDto } from '@shared/service-proxies/service-proxies';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';

import { NgForm } from '@angular/forms';
import * as _ from 'lodash';

@Component({
    selector: 'app-es-entity-details',
    templateUrl: './es-entity-details.component.html',
    styleUrls: ['./es-entity-details.component.css']
})
export class ESEntityDetailsComponent extends AppComponentBase implements OnInit {

    @Input() public economicsubstance: EconomicSubstanceDeclarationDto; // this is typed as string, but you can use any type you want
    @Input() public esEntityDetail: EsEntityDetailDto;
    @Input() public listOfCountry: CountryDto[];
    @Input() public corporateEntity: CorporateEntityDto;
    @Input() public currency: any;
    @Input() public readOnlyMode: boolean;
    @Input() public displayFromCa: boolean;
    @Input() public importOnlyMode: boolean;
    @ViewChild('#entityDetails', { static: false }) form: NgForm;
    private showHideConditions: boolean[] = [];
    public ultimateParentEntityType: ParentEntityType;
    public immediateParentEntityType: ParentEntityType;
    
    constructor(injector: Injector, public _economicSubstanceService: EconomicSubstanceService) {
        super(injector);        
    }

    ngOnInit() {

        this.ultimateParentEntityType = ParentEntityType.Ultimate;
        this.immediateParentEntityType = ParentEntityType.Immediate;

        if (!this.readOnlyMode) {
            if (!this.esEntityDetail) this.esEntityDetail = new EsEntityDetailDto();
            if (!this.esEntityDetail.esEntityAdditionalDetails) this.esEntityDetail.esEntityAdditionalDetails = [];
        }       

        if (!this.isNullorUndefined(this.esEntityDetail.isSameAsRegisteredAddress) && this.esEntityDetail.isSameAsRegisteredAddress) {
            if (this.corporateEntity) {
                this.esEntityDetail.addressLine1 = this.corporateEntity.addressLine1;
                this.esEntityDetail.addressLine2 = this.corporateEntity.addressLine2;
                this.esEntityDetail.countryId = this.corporateEntity.countryId;
                if (this.listOfCountry) this.esEntityDetail.country = this.listOfCountry.find((value, i) => value.id === this.corporateEntity.countryId);
                if (this.readOnlyMode && this.importOnlyMode) this.esEntityDetail.country.countryName = this.corporateEntity.countryName;
            }
        }

        this._economicSubstanceService.validations$.subscribe((data: ValidationResultMainDto[]) => {
            if (data) {
                data.forEach((validation: ValidationResultMainDto) => {
                    if (validation.activityName == "EntityDetails") {
                        let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                        if (backendValidations != null) this.runNextValidations(backendValidations);
                    }
                });
            }

        });
    }

    private isNullorUndefined(input: any): boolean {
        return typeof input === "undefined" || input === null;
    }

    private runNextValidations(validations: ValidationResultDto[]): void {
        validations.forEach((validation: ValidationResultDto) => {
            if (!this._economicSubstanceService.entityDetailsError[validation.fieldName]) {
                this._economicSubstanceService.entityDetailsError[validation.fieldName] = true;
                if (validation.validationType == ValidationType.Error) {

                    this._economicSubstanceService.errorList.push(validation.validationString);
                }
                else {
                    this._economicSubstanceService.warningList.push(validation.validationString);

                }
            }
        })
    }

    public setBusinessAddress(item: any): void {
        if (!this.isNullorUndefined(this.esEntityDetail.isSameAsRegisteredAddress) && this.esEntityDetail.isSameAsRegisteredAddress) {
            this.esEntityDetail.addressLine1 = this.corporateEntity.addressLine1;
            this.esEntityDetail.addressLine2 = this.corporateEntity.addressLine2;
            this.esEntityDetail.countryId = this.corporateEntity.countryId;
            this.esEntityDetail.country = this.listOfCountry.find((value, i) => value.id === this.corporateEntity.countryId);
        }
        else {
            this.esEntityDetail.addressLine1 = null;
            this.esEntityDetail.addressLine2 = null;
            this.esEntityDetail.countryId = null;
            this.esEntityDetail.country = null;
        }
    }

    public setBusinessAddressId(item: any): void {
        this.esEntityDetail.countryId = item.value.id;
    }

    public showHideResult(index): boolean {
        this.showHideConditions[0] = !this.isNullorUndefined(this.esEntityDetail.doesEntityHaveUltimateParent) && this.esEntityDetail.doesEntityHaveUltimateParent;
        this.showHideConditions[1] = !this.isNullorUndefined(this.esEntityDetail.doesEntityHaveImmediateParent) && this.esEntityDetail.doesEntityHaveImmediateParent;
        return this.showHideConditions[index];
    }

    public setCurrencyId(item: any): void {
        this.economicsubstance.currencyId = item.value.id;
    }

    public getTotalInUSD(total: any): string {
        if (total) {
            if (this.economicsubstance.currencyExchangeRate === null || typeof (this.economicsubstance.currencyExchangeRate) === undefined) return "";
            let result = total * this.economicsubstance.currencyExchangeRate;
            return result.toFixed(2);
        }
    }
}
