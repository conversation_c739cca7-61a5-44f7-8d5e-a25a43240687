import { Component, OnInit, Injector, Input, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CtspListDto, CtspServiceProxy, CtspActivateInputDto } from '@shared/service-proxies/service-proxies';
import { CTSPListItem } from './ctsp-list-item';
import { GetCtspInformation } from '../shared/get-ctps-Inforamtion'

@Component({
  selector: 'app-ctsp-list',
  templateUrl: './ctsp-list.component.html',
  styleUrls: ['./ctsp-list.component.css']
})
export class CtspListComponent extends AppComponentBase implements OnInit
{
    @Input() ctspList: CTSPListItem[];
    @Input() isReadonly: boolean;
    @Output() onUpdateSelectedCtspList: EventEmitter<CTSPListItem[]> = new EventEmitter();
   // @Output() onViewRADetail: EventEmitter<string> = new EventEmitter();

    items: string[] = [];
    displayBasic: boolean;
    searchText: string;
    selectAll: boolean = false;
    selectNone: boolean = false;
    selectedCount: number = 0;

    constructor(injector: Injector, public _getCtspInformation: GetCtspInformation,public  _ctspServiceProxy: CtspServiceProxy)
    {
        super(injector);
    }

    ngOnInit() {
        
  }

ngOnChanges(changes: SimpleChanges) {
        //when raList gets set first
    if (changes.ctspList.currentValue) {
        this.selectedCount = changes.ctspList.currentValue.filter(x => { return !!x.isSelected }).length;

        if (this.selectedCount == changes.ctspList.currentValue.length) {
                this.selectAll = true;
            }
        }
    }

    onSelectAll() {
        if (this.isReadonly)
            event.stopPropagation();
        this.selectedCount = this.getFilteredCount();
        this.selectNone = false;

        this.updateRASelected(true);
        this.onUpdateSelectedCtspList.emit(this.getSelectedRAIdList());
    }

    onSelectNone() {
        if (this.isReadonly) return;
        this.selectedCount = 0;
        this.selectAll = false;

        this.updateRASelected(false);
        this.onUpdateSelectedCtspList.emit(this.getSelectedRAIdList());
    }

    onSelectRA(checked: any) {
        if (this.isReadonly) return;
        this.selectedCount += (checked ? 1 : -1);

        if (this.selectedCount == this.getFilteredCount()) {
            this.selectAll = true;
            this.selectNone = false;
        }
        else if (this.selectedCount == 0) {
            this.selectNone = true;
            this.selectAll = false;
        }
        else if (this.selectedCount > 0) {
            this.selectNone = false;
            this.selectAll = false;
        }

        this.onUpdateSelectedCtspList.emit(this.getSelectedRAIdList());
    }

    getFilteredCount() {
        return this.ctspList.filter(x => { return this.isMatchFilter(x) }).length;
    }

    updateRASelected(selectAll: boolean) {
        this.ctspList.forEach(x => {
            if (selectAll)
                x.isSelected = this.isMatchFilter(x);
            else
                x.isSelected = false;
        });
    }

    isMatchFilter(ra: CTSPListItem): boolean {
        if (!this.searchText)
            return true;

        var searchableText = ra.name + ' ' + ra.number;
        return searchableText.toLowerCase().indexOf(this.searchText.toLowerCase()) >= 0;
    }

    getSelectedRAIdList(): CTSPListItem[] {

        
        return  this.ctspList.filter(x => x.isSelected);
    }

   


    showBasicDialog(item: any)
    {
        this._ctspServiceProxy.getCtspDetailById(item).subscribe(result => {
            this.items = this._getCtspInformation.getCtspInformation(result);
            this.displayBasic = true;
        });
    }
}
