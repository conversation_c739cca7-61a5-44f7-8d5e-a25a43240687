import {
    Component,
    OnInit,
    Injector,
    ViewChild,
    EventEmitter,
    Output,
    Input,
} from "@angular/core";
import { AppComponentBase } from "@shared/common/app-component-base";
import { ModalDirective } from "ngx-bootstrap";
import {
    WrokflowServiceProxy,
    ESCAAssessmentDto,
    ESReviewStatusDto,
    EconomicSubstanceDeclarationDto,
    EsCaAssessmentCommentsDto,
    ESCAAssessmentInputDto,
    AssessmentActionType,
    AuditEventServiceProxy,
    CaEconomicSubstanceAuditDto,
    CASearchServiceServiceProxy,
    ESEnforcementDocumentsDto,
    RelativeActivitySelection,
    RelativeActivityStatusDto,
    EsCaCspInformationRequestDocumentDto,
} from "@shared/service-proxies/service-proxies";
import { ReviewChangeStatus } from "@app/economicsubstance/EconomicSubstance";
import { SharedComponent } from "@app/economicsubstance/sharedfunctions";
import { DialogService } from "primeng/api";
import * as moment from "moment";
import * as _ from "lodash";
import { forkJoin, Observable } from "rxjs";
import { FormGroup } from "@angular/forms";
import { NgFormValidationComponent } from "shared/utils/validation/ng-form-validation.component";
import {
    EconomicDocumentsName,
    RelevantDocumentsName,
} from "@app/economicsubstance/EconomicSubstance";

@Component({
    selector: "changestatusmodal",
    templateUrl: "./change-ca-reviewstatus.component.html",
    styleUrls: ["./change-ca-reviewstatus.component.css"],
})
export class ChangeCaReviewstatusComponent
    extends AppComponentBase
    implements OnInit {
    @ViewChild("changestatusmodal", { static: true }) modal: ModalDirective;

    @ViewChild("changestatusform", { static: true })
    changestatusform: FormGroup;
    @ViewChild("changestatusFormValidation", { static: true })
    changestatusFormValidation: NgFormValidationComponent;

    @Output() finishAssessment: EventEmitter<boolean> =
        new EventEmitter<boolean>();

    currentEconomicSubstance: EconomicSubstanceDeclarationDto =
        new EconomicSubstanceDeclarationDto();
    esassessment: ESCAAssessmentDto = new ESCAAssessmentDto();
    @Input() ctspId: any;
    @Input() esassessmentInput: ESCAAssessmentDto;

    isInforamtionRequired: boolean = false;
    isFail: boolean = false;
    InformationRequestedDocumentDoc: any;
    EnforcmentDocumentDoc: any;
    ESCAAssessmentDocumentsDoc: any;

    anyActivityFailed: RelativeActivityStatusDto;

    reviewOldStatus: ESReviewStatusDto;
    errorListdialog: string[] = [];
    commentRequired: boolean = false;

    esReviewStatus: ESReviewStatusDto[];
    escaassessmentInputDto: ESCAAssessmentInputDto =
        new ESCAAssessmentInputDto();
    currentComments: EsCaAssessmentCommentsDto =
        new EsCaAssessmentCommentsDto();

    esCspDoc: EsCaCspInformationRequestDocumentDto[] = [];
    esEnforcmentDoc: ESEnforcementDocumentsDto[] = [];
    minimumDate: Date;
    isFormSubmitted = false;

    constructor(
        injector: Injector,
        private _wrokflowServiceProxy: WrokflowServiceProxy,
        private _auditEventService: AuditEventServiceProxy
    ) {
        super(injector);

        this.minimumDate = new Date();
    }

    ngOnInit() {
        this.changestatusFormValidation.formGroup = this.changestatusform;
        this.EnforcmentDocumentDoc =
            EconomicDocumentsName.EnforcmentDocumentDoc;
        this.InformationRequestedDocumentDoc =
            EconomicDocumentsName.InformationRequestedDocumentDoc;
        this.ESCAAssessmentDocumentsDoc =
            EconomicDocumentsName.ESCAAssessmentDocumentsDoc;

        if (!this.currentComments.escaAssessmentDocuments)
            this.currentComments.escaAssessmentDocuments = [];

        if (!this.esEnforcmentDoc) this.esEnforcmentDoc = [];

        if (!this.esCspDoc) this.esCspDoc = [];
    }

    shown(): void {
        // I don't know what is supposed to go here.
        // Regards, Jeff
    }
    show(esassessment, currentEconomicSubstance): void {
        let self = this;
        this.changestatusform.reset();
        this.changestatusFormValidation.ClearExternalError();
        this.esassessment = _.cloneDeep(esassessment);
        this.currentEconomicSubstance = _.cloneDeep(currentEconomicSubstance);

        this.currentComments.escaAssessmentDocuments = [];

        this.esEnforcmentDoc = [];
        this.esCspDoc = [];

        this.isInforamtionRequired = false;
        this.isFail = false;

        this.esassessment.comments = "";
        this.isInforamtionRequired = false;

        forkJoin([
            this._wrokflowServiceProxy.getESRelevantActivityAssessmentStatus(
                this.ctspId,
                this.currentEconomicSubstance.id,
                undefined
            ),
            this._wrokflowServiceProxy.getESReviewStatusLevelBased(
                this.ctspId,
                this.esassessment.assessmentLevel,
                esassessment.esReviewStatus.code
            ),
        ]).subscribe((responseList) => {
            // response 0
            this.esassessment.relativeActivityStatus = responseList[0];
            // response 1
            responseList[1] = responseList[1].filter(
                (x) => x.code !== this.esassessment.esReviewStatus.code
            );
            let selectReviewStatus = new ESReviewStatusDto();
            selectReviewStatus.name = this.l("Select");
            selectReviewStatus.code = 0;
            responseList[1].unshift(selectReviewStatus);

            this.esReviewStatus = responseList[1];
            this.esassessment.esReviewStatus = selectReviewStatus;

            if (this.esassessment.relativeActivityStatus && this.esassessment.relativeActivityStatus.relativeActivityList) {
                let relevantActivities = this.currentEconomicSubstance.relevantActivities;
                let existingRelativeActivityList = this.esassessment.relativeActivityStatus.relativeActivityList.filter(function (o1) {
                    return relevantActivities.some(function (o2) {
                        return o1.id == o2.id;
                    });
                }).map(function (o) {
                    return o;
                });

                if (existingRelativeActivityList)
                    this.anyActivityFailed =
                        existingRelativeActivityList.find(
                            (x) => x.status === RelativeActivitySelection.Fail
                        );
            }
        });

        self.modal.show();
    }
    close(): void {
        this.isFormSubmitted = false;
        this.modal.hide();
    }

    ChangeAssessment(event: any): void {
        if (!this.escaassessmentInputDto)
            this.escaassessmentInputDto = new ESCAAssessmentInputDto();
        this.escaassessmentInputDto.action = AssessmentActionType.ChangeStatus;
        this.escaassessmentInputDto.esReviewStatus = event.value;

        this.isInforamtionRequired = event.value.code === 4 ? true : false;
        this.isFail = event.value.code === 8 ? true : false;
    }

    complete(): void {
        let self = this;
        self.isFormSubmitted = true;
        self.changestatusFormValidation.ClearExternalError();   
        // need one more validation for pass and fail
        // Pass validation
        if (self.esassessment.esReviewStatus.code === 0) {
            self.changestatusFormValidation.AddExternalError(
                "Select the assessment status"
            );
        }

        if (self.esassessment.esReviewStatus.code === 7) {
            if (self.anyActivityFailed)
                self.changestatusFormValidation.AddExternalError(
                    "No relevant activities on the declaration should be marked as ‘Fail’"
                );
        }
        // faild validation
        if (self.esassessment.esReviewStatus.code === 8) {
            if (!self.anyActivityFailed)
                self.changestatusFormValidation.AddExternalError(
                    "At least one of the relevant activities on the declaration has  been marked ‘Fail’ "
                );
        }
        // if no selection was made

        if (
            !self.changestatusFormValidation.isFormValid() ||
            !self.changestatusFormValidation.isFormHaveCustomErrors() || 
            self.extraValidation.length > 0
        ) {
            return;
        }
        self.SaveAssessment();
    }

    SaveAssessment() {
        let self = this;
        self.escaassessmentInputDto.action = AssessmentActionType.ChangeStatus;
        self.escaassessmentInputDto.id = self.esassessment.id;
        self.escaassessmentInputDto.economicSubstanceId =
            self.currentEconomicSubstance.id;
        self.escaassessmentInputDto.esReviewStatus =
            self.esassessment.esReviewStatus;
        self.escaassessmentInputDto.esReviewStatusGuid =
            self.esassessment.esReviewStatusGuid;
        self.escaassessmentInputDto.assessmentLevel =
            self.esassessment.assessmentLevel;
        self.escaassessmentInputDto.prTreatmentSubmissionDateString =
            self.esassessment.prTreatmentDueDateString;
        self.escaassessmentInputDto.informationRequestedCommentsCA =
            self.esassessment.informationRequestedCommentsCA;
        self.escaassessmentInputDto.inRequestedDueDateString =
            self.esassessment.inRequestedDueDateString;
        self.escaassessmentInputDto.comments = self.esassessment.comments;
        self.escaassessmentInputDto.ctspId = self.ctspId;
        self.escaassessmentInputDto.penaltyApplied =
            self.esassessment.penaltyApplied;

        self.escaassessmentInputDto.isInformationRequired =
            self.esassessment.esReviewStatus.code === 4;

        self.escaassessmentInputDto.actionEnforced =
            self.esassessment.actionEnforced;
        self.escaassessmentInputDto.esEnforcementDocuments =
            self.esEnforcmentDoc;
        self.escaassessmentInputDto.esCaCspInformationRequestDocuments =
            self.esCspDoc;

        if (self.currentComments.escaAssessmentDocuments)
            self.escaassessmentInputDto.escaAssessmentDocuments =
                self.currentComments.escaAssessmentDocuments;

        self._wrokflowServiceProxy
            .updateESReviewStatus(self.escaassessmentInputDto)
            .subscribe((result) => {
                let auditInput = new CaEconomicSubstanceAuditDto();
                auditInput.ctspNumber = self.ctspId;
                auditInput.id = self.currentEconomicSubstance.id;
                self._auditEventService
                    .auditCaPerformAssesmentDeclarationEvent(auditInput)
                    .subscribe();

                self.finishAssessment.emit(true);
                self.close();
            });
    }

    get extraValidation() {
        const errors = [];
        if (this.isFormSubmitted && this.esassessment.esReviewStatus.code === 4 && !this.esassessment.inRequestedDueDateString) {
            errors.push('EvidenceDueDate field is required');
        }
        return errors;
    }
}
