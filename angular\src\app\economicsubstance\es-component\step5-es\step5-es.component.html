<div class="col-flex">
    <!--Step 5-->
    <p-card [ngClass]="{'p-card-properties_form margin-top':!readOnlyMode,'p-card-properties_form_readonly margin-top':readOnlyMode}">
        <form #step5="ngForm">
            <div class="row-flex-justified ">
                <label class="ac-header"> {{l('ESComments')}}</label>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('ESCommentsa')}}</label>
            </div>
            <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.step5Error.SupportingComments }">
                <textarea *ngIf="!readOnlyMode" pInputTextarea rows="10" maxlength ="510" [(ngModel)]="economicsubstance.supportingComments" id="supportingCommentsid" name="supportingCommentsname"></textarea>

                <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.supportingComments" style="background-color : #f0f0f0 !important" rows="5" id="supportingCommentsredid" name="supportingCommentsredname"></textarea>

            </div>

            <div class="row-flex-justified-rows">
                <div class="row-flex-justified ">
                    <label class="ac-header"> {{l('EsSupportingDocument')}}</label>
                </div>
                <div class="row-flex-justified-largefont">
                    <label>{{l('EsSupportingDocumenta')}}</label>
                </div>
                <div class="row-flex-justified">
                    <app-upload-files
                    [Documents]="economicsubstance.supportingDocument"
                    [DocumentTypeName]="SupportingDoc"
                    [IsEconomicDocument]="true"
                    [readOnlyMode]="readOnlyMode"
                    [displayFromCa] ="displayFromCa || historyDisplay"
                    [ctspId]="ctspId">
                    </app-upload-files>

                </div>
            </div>

        </form>
    </p-card>
</div>
