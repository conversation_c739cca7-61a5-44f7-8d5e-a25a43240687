import { Component, OnInit, ViewEncapsulation, ViewChild, AfterViewInit, Injector } from '@angular/core';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { Table } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { LazyLoadEvent } from 'primeng/api';
import { Router, ActivatedRoute, Params } from '@angular/router';
import * as moment from 'moment';
import { CASearchServiceServiceProxy, CaSearchNonResidentByCountryInput, DashboardSearchResultDto, PagedResultDtoOfDashboardSearchResultDto, CaSearchOverdueFilingsInput, CaSearchNotStartedFilingsInput } from '@shared/service-proxies/service-proxies';
import { finalize } from 'rxjs/operators';
import { Observable, empty } from 'rxjs';

@Component({
  selector: 'app-ca-ess-search',
  templateUrl: './ca-ess-search.component.html',
  styleUrls: ['./ca-ess-search.component.css'],
  encapsulation: ViewEncapsulation.None,
  animations: [appModuleAnimation()]
})
export class CaEssSearchComponent extends AppComponentBase implements AfterViewInit, OnInit {
  @ViewChild('dataTable', { static: true }) dataTable: Table;
  @ViewChild('paginator', { static: true }) paginator: Paginator;

  private params: Params
  get title(): string {
    if (this.params.type == "nonresident") {
      const start = moment(this.params.startDate).format("YYYY-MM-DD")
      const end = moment(this.params.endDate).add(-1, "days").format("YYYY-MM-DD")
      return `Non Resident Report (Start : ${start}, End : ${end}, Country : ${this.params.country})`
    }
    return null
  }

  constructor(
    injector: Injector,
    private router: Router,
    private route: ActivatedRoute,
    private searchService: CASearchServiceServiceProxy) {
    super(injector)

    route.queryParams.subscribe(x => this.params = x);
  }

  ngOnInit() {
  }

  ngAfterViewInit(): void {
    this.primengTableHelper.adjustScroll(this.dataTable);
  }

  getResults(event?: LazyLoadEvent) {
    if (this.primengTableHelper.shouldResetPaging(event)) {
      this.paginator.changePage(0);

      return;
    }

    this.primengTableHelper.showLoadingIndicator();

    let observable: Observable<PagedResultDtoOfDashboardSearchResultDto> = empty()

    if (this.params.type == "nonresident") {
      const input = new CaSearchNonResidentByCountryInput(
        {
          startDate: moment(this.params.startDate),
          endDate: moment(this.params.endDate),
          countryCode: this.params.country,
          maxResultCount: this.primengTableHelper.getMaxResultCount(this.paginator, event),
          skipCount: this.primengTableHelper.getSkipCount(this.paginator, event),
              sorting: this.primengTableHelper.getSorting(this.dataTable),
        }
      );

      observable = this.searchService.searchNonResidentEssByCountry(input)
    } else if (this.params.type == "notstarted") {
      const input = new CaSearchNotStartedFilingsInput({
        year: this.params.year,
        ctspNumber: this.params.ctspnumber,
        maxResultCount: this.primengTableHelper.getMaxResultCount(this.paginator, event),
        skipCount: this.primengTableHelper.getSkipCount(this.paginator, event),
          sorting: this.primengTableHelper.getSorting(this.dataTable),
      })
      observable = this.searchService.searchNotStartedFilings(input)
    }

    observable.pipe(
      finalize(() => this.primengTableHelper.hideLoadingIndicator())
    ).subscribe(result => {
      this.primengTableHelper.totalRecordsCount = result.totalCount;
      this.primengTableHelper.records = result.items;
      this.primengTableHelper.hideLoadingIndicator();
    })
  }

  reloadPage(): void {
    this.paginator.changePage(this.paginator.getPage());
  }

  onRowSelect(record: any) {
  }

  goToReview(record: DashboardSearchResultDto): void {
    this.router.navigate(['app/economicsubstance/careview/' + record.economicSubstanceId + '/' + + record.ctspNumber]);
  }

  goToNonResidentDashboard(e: any): void {
    if (this.params.type == "notstarted") {
      this.router.navigate(['app/main/cadashboard']);
    } else {
      this.router.navigate([`app/main/cadashboard/nonresident`], {
        queryParams: {
          year: +this.params.year,
          week: moment(this.params.startDate).format("YYYY-MM-DD")
        }
      });
    }
  }
}
