    public class ServiceProvidersDto: EntityDtoBase
    {
      
      
        public string Name { get; set; }
        //*******

        public string AddressLine1 { get; set; }
       
        public decimal? NumberOfStaffEmployed { get; set; }
        public string NumberOfStaffEmployedString { get; set; }
        //Outsourcing: Hours per month   each person employed for this relevant activity
        public decimal? HoursPerMonthForEmployee { get; set; }
        //Outsourcing: Is the entity able  to monitor and control carrying out of the outsourced activity? 
        public string HoursPerMonthForEmployeeString { get; set; }
        public bool? IsEntityAbleToMonitor { get; set; }

        public string IsEntityAbleToMonitorString { get; set; }
        public bool IsNew { get; set; } = false;
    }

