<div class="ca-entitysearch-container ui-g">
    <div class="ca-entitysearch-search-section ui-g-9">
        <div class="ca-entitysearch-title ca-title">
            <p class="ess-title" [hidden]="isReport">ECONOMIC SUBSTANCE SEARCH</p>
            <p class="ess-title" [hidden]="!isReport">ECONOMIC SUBSTANCE REPORT</p>
        </div>


        <form #searchForm="ngForm" (ngSubmit)="onSubmit(searchForm)" class="ca-entitysearch-form">

            <div [hidden]="isReport">
                <div class="col-flex-center basicSearch">
                    <div class="col-flex margin-top" [hidden]="isAdvancedSearch || isReport">
                        <label>{{l("Entity Name (or Alternative Name):")}}</label>
                        <div>
                            <input id="searchEntityName" type="text" name="entityName" [(ngModel)]="viewModel.essSearch.entityName"
                                class="form-control" pInputText>
                        </div>
                    </div>

                    <div class="col-flex margin-top" [hidden]="isAdvancedSearch || isReport">
                        <div class="row-flex-justified-width_300">
                            <label for="IsActive">{{l("Only Include Entities Requiring Declaration:")}} </label>
                            <input id="IsActive" type="checkbox" name="includeDeclaration" style="padding-left:5px"
                                [(ngModel)]="viewModel.essSearch.includeDeclaration">
                        </div>
                    </div>

                    <div class="col-flex margin-top" [hidden]="isAdvancedSearch || isReport">
                        <label>{{l("Economic Substance Assessment Status:")}}</label>
                        <div>
                            <p-dropdown id="ESSStatus" name="essStatus" [options]="viewModel.essStatuses"
                                [(ngModel)]="viewModel.essSearch.reviewStatus"></p-dropdown>
                        </div>
                    </div>
                    <div class="col-flex margin-top" [hidden]="isAdvancedSearch || isReport">
                        <label>{{l("Financial Period End Year")}}</label>
                        <div>
                            <p-dropdown id="ESSSearchYear" name="year" [options]="viewModel.years" [(ngModel)]="viewModel.essSearch.year">
                            </p-dropdown>
                        </div>
                    </div>
                </div>
                <app-entity-search-adv [hidden]="!isAdvancedSearch" (onAdvSearch)="onAdvSearch($event)"
                    [isForCASearch]=true [supportRandomSampling]=true></app-entity-search-adv>

                <!-- <app-ca-home-report [hidden]="!isReport"></app-ca-home-report> -->

                <div class="button-section margin-top" [hidden]="isAdvancedSearch || isReport">
                    <button id="search" pButton type="submit" label="Search" [disabled]="!viewModel.isSearchValid"></button>
                </div>
            </div>

            <div [hidden]="!isReport">
                <div class="form-group row-flex-right">
                    <div>
                        <div class="btn btn-primary ca-report-button-section"  (click)="generateESReport();">
                            <i class="fas fa-download"></i>
                            Generate Report
                        </div>
                        <div class="btn btn-primary ca-report-button-section" (click)="resetform();">
                            <i class="fas fa-plus"></i>
                            New Report
                        </div>
                        <div class="btn btn-primary ca-report-button-section" (click)="isReport=false;">
                            <i class="fas fa-undo"></i>
                            Back Home
                        </div>
                    </div>
                </div>
                <div class="col-flex-center basicSearch">


                    <div class="col-flex margin-top">
                        <label for="CSPNumber">CSP Number</label>
                        <div>
                            <input type="text" name="ReportCSPNumber" class="form-control" placeholder="Enter CSP Number"
                                [(ngModel)]="reportModel.CSPNumber">
                        </div>
                    </div>

                    <div class="col-flex margin-top">
                        <label for="EsName">Entity Name</label>
                        <div>
                            <input type="text" name="ReportEsName" class="form-control" placeholder="Enter Entity Name"
                                [(ngModel)]="reportModel.EsName">
                        </div>
                    </div>

                    <div class="col-flex margin-top">
                        <label for="FinancialPeriodDate">Financial Period End
                            Date</label>
                        <div>
                            <input type="date" name="FinancialPeriodDate" class="form-control" placeholder="Enter End Date"
                                [(ngModel)]="reportModel.FinancialPeriodDate">
                        </div>
                    </div>

                    <div class="col-flex margin-top">
                        <label for="AssessmentStatus">Assessment Status</label>
                        <div>
                            <p-dropdown name="assessmentStatuses" [options]="reportModel.AssessmentStatuses"
                                [(ngModel)]="reportModel.AssessmentStatus"></p-dropdown>
                        </div>
                    </div>

                    <div class="col-flex margin-top">
                        <label for="NonResidenceDeclaration">Non-residence declaration</label>
                        <div>
                            <p-dropdown name="nonResidenceDeclaration" [options]="reportModel.ResidenceDeclarations"
                                [(ngModel)]="reportModel.NonResidenceDeclaration"></p-dropdown>
                        </div>
                    </div>
                    <div class="col-flex margin-top">
                        <label for="RelevantActivity">Relevant Activity</label>
                        <div>
                             <ng-multiselect-dropdown name="RelevantActivity" class="form-control"
                                placeholder="Select"
                                [settings]="dropdownSettings"
                                [data]="reportModel.RelevantActivities"
                                [(ngModel)]="reportModel.SelectedRelevantActivity"
                                (onSelect)="onItemSelect($event)"
                                (onSelectAll)="onSelectAll($event)"
                                >
                                </ng-multiselect-dropdown>
                        </div>
                    </div>


                </div>
            </div>
        </form>

        <div class="button-section multibuttons searchtypelinks">
            <a id="advancedSearch" *ngIf="!isAdvancedSearch && supportAdvancedSearch && !isReport"
                (click)="isAdvancedSearch=true;">{{l("Advanced Search")}}</a>
            <a id="basicSearch" *ngIf="isAdvancedSearch && !isReport" (click)="isAdvancedSearch=false;">{{l("Basic Search")}}</a>

            <!-- <a id="reportSearch" *ngIf="!isReport" (click)="isReport=true;">{{l("Report")}}</a> -->
        </div>

        <div class="button-section searchtypelinks">
            
        </div>
    </div>
    <div class="ca-entitysearch-ralist-section ui-g-3">
        <app-ctsp-list [ctspList]="ctspItems" (onUpdateSelectedCtspList)="setSelectedRAs($event)"></app-ctsp-list>
    </div>

    <p-dialog header="Report Generation" [(visible)]="displayModal" [style]="{width: '30vw'}" [baseZIndex]="10000" styleClass="customDialog">
        <p>The report is being generated and you will get a notification when the file will be available to download..</p>
        <div class="button-section searchtypelinks">
            <p-button icon="pi pi-check" (click)="displayModal=false" label="OK" styleClass="p-button-text"></p-button>
        </div>
    </p-dialog>
</div>

