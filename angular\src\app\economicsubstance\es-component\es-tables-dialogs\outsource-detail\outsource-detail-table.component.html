<div class="row-flex-justified-largefont">
    <label>  9{{headingSeq}}.{{sequenceNo}}b. {{l("OutsourcingC")}} <span class="required">*</span> </label>
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="model.show(null,country,true,importOnlyMode)">Add row</a>
    </div>
</div>

<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="details">
    <ng-template pTemplate="header">
        <tr>
            <th style="width:15%" class="table-text-header">Name of entity to whom outsourced</th>
            <th style="width:25%" class="table-text-header">Details of resources deployed by the entity in carrying out the activity on their behalf</th>
            <th style="width:15%" class="table-text-header">Number of staff employed in carrying out CIGA for the entity</th>
            <th style="width:15%" class="table-text-header">Hours per month each person employed for this relevant activity</th>
            <th style="width:10%" class="table-text-header">Is the entity able to monitor and control carrying out of the outsourced activity?</th>
            <th *ngIf="!readOnlyMode" style="width:10%" class="table-text-header">Action</th>

        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
            <td style="width:15%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping"> {{source.name}}</label>
            </td>
            <td style="width:25%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping"> {{source.addressLine1}}</label>
            </td>
            <td style="width:15%" *ngIf="!importOnlyMode">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.numberOfStaffEmployed}}</label>
            </td>
            <td style="width:15%" *ngIf="importOnlyMode">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.numberOfStaffEmployedString}}</label>
            </td>
            <td style="width:15%" *ngIf="!importOnlyMode">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.hoursPerMonthForEmployee}}</label>
            </td>
            <td style="width:15%" *ngIf="importOnlyMode">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.hoursPerMonthForEmployeeString}}</label>
            </td>
            <td style="width:10%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{returnyesno(source.isEntityAbleToMonitor)}}</label>
            </td>
            <td style="width:10%" *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="model.show(source,country,false,importOnlyMode)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-outsource-detail-dialog #model (submitted)="updateSource($event)" ></app-outsource-detail-dialog>

