import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, DocumentsDto, ValidationResultDto, ValidationResultMainDto } from '@shared/service-proxies/service-proxies';
import { EconomicDocumentsName } from '@app/economicsubstance/EconomicSubstance';
import { NgForm } from '@angular/forms';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';

@Component({
  selector: 'app-step5-es',
  templateUrl: './step5-es.component.html',
  styleUrls: ['./step5-es.component.css']
})
export class Step5EsComponent extends AppComponentBase implements OnInit {
  @Input() public economicsubstance: EconomicSubstanceDeclarationDto;
  @Input() readOnlyMode: boolean;
  @Input() displayFromCa: boolean;

  @Input() historyDisplay: boolean;

  @Input() ctspId: any;

  @ViewChild('#step5', { static: false }) form: NgForm;
  SupportingDoc: any;
  constructor(injector: Injector, public _economicSubstanceService: EconomicSubstanceService) { super(injector); }

  ngOnInit() {
    this.SupportingDoc = EconomicDocumentsName.SupportingDocument;

    this._economicSubstanceService.validations$.subscribe((data: ValidationResultMainDto[]) => {
      if (data) {
        data.forEach((validation: ValidationResultMainDto) => {
          if (validation.activityName == "Step5") {
            let backendValidations: ValidationResultDto[] = validation.validationResultDto;
            if (backendValidations != null) this.runNextValidations(backendValidations);
          }
        });
      }
    });
  }


  runNextValidations(validations: ValidationResultDto[]) {
    validations.forEach((validation: ValidationResultDto) => {
      if (!this._economicSubstanceService.step5Error[validation.fieldName]) {
        this._economicSubstanceService.step5Error[validation.fieldName] = true;
        this._economicSubstanceService.errorList.push(validation.validationString);
      }
    });
  }

}
