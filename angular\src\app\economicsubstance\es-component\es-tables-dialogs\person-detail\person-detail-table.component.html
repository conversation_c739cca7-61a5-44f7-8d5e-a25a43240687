<div class="row-flex-justified-largefont">
    <label> 9{{headingSeq}}.{{sequenceNo}}b. {{l("DirectionManagementB")}} <span class="required">*</span></label>
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="personalmodal.show(null,true)">Add row</a>
    </div>
</div>

<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="personDetails">
    <ng-template pTemplate="header">
        <tr>
            <th style="width:25%" class="table-text-header">Name</th>
            <th style="width:25%" class="table-text-header">Resident in the Virgin Islands?</th>
            <th style="width:25%" class="table-text-header">Relation to the entity</th>
            <th *ngIf="!readOnlyMode" style="width:25%" class="table-text-header">Action</th>

        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
            
            <td style="width:25%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"  class="word-wrapping" >{{source.name}} </label>
            </td>
            <td style="width:25%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{returnyesno(source.isResidentInBVI)}} </label>
            </td>
            <td style="width:25%"  >
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping" > {{source.relationToEntity}}  </label>
            </td>
            <td style="width:25%" *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="personalmodal.show(source,false)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-person-detail-dialog #personalmodal (submitted)="updateSource($event)" ></app-person-detail-dialog>
