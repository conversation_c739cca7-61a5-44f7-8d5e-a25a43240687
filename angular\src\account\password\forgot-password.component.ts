import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { accountModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AccountServiceProxy, SendPasswordResetCodeInput, SingleFieldInputOfString } from '@shared/service-proxies/service-proxies';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs/operators';

@Component({
    templateUrl: './forgot-password.component.html',
    animations: [accountModuleAnimation()]
})
export class ForgotPasswordComponent extends AppComponentBase {
    model: SendPasswordResetCodeInput = new SendPasswordResetCodeInput();

    saving = false;

    emailMethod: string = 'Email';
    securityQuestionsMethod: string = 'SecurityQuestions';
    methods: SelectItem[];
    selectedMethod: string = null;

    constructor(
        injector: Injector,
        private _accountService: AccountServiceProxy,
        private _router: Router
    ) {
        super(injector);
    }

    ngOnInit() {
        this.methods = [
            { label: "Email", value: this.emailMethod },
            { label: "Security Questions", value: this.securityQuestionsMethod },

        ];

        this.selectedMethod = this.methods[0].value;
    }

    save(): void {
        this.saving = true;

        if (this.selectedMethod == this.emailMethod) {
            this._accountService.sendPasswordResetCode(this.model)
                .pipe(finalize(() => { this.saving = false; }))
                .subscribe(() => {
                    this.message.success(this.l('PasswordResetMailSentMessage'), this.l('MailSent')).then(() => {
                        this._router.navigate(['account/login']);
                    });
                });
        } else if (this.selectedMethod == this.securityQuestionsMethod) {
            var input = new SingleFieldInputOfString();
            input.value = this.model.userName;
            this._accountService.checkUser(input)
                .pipe(finalize(() => { this.saving = false; }))
                .subscribe(() => {
                    this._router.navigate(['account/security-questions/' + this.model.userName]);
                });
        } else {
            this.saving = false;
        }
    }
}
