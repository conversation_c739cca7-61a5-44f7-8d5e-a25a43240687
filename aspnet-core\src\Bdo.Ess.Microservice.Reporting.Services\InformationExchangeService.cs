using Abp.Application.Services.Dto;
using AutoMapper;
using Bdo.Ess.Common;
using Bdo.Ess.Common.Audit;
using Bdo.Ess.Common.EconomicSubstance;
using Bdo.Ess.Common.Extensions;
using Bdo.Ess.Common.Helpers;
using Bdo.Ess.Common.InformationExchange;
using Bdo.Ess.Core;
using Bdo.Ess.Dtos.Common;
using Bdo.Ess.Dtos.InformationExchange;
using Bdo.Ess.Dtos.LookUp;
using Bdo.Ess.Dtos.MasterData;
using Bdo.Ess.Dtos.NJT;
using Bdo.Ess.Lookup;
using Bdo.Ess.Microservice.Core.EntityFramework.Ca;
using Bdo.Ess.Microservice.Core.Services;
using Bdo.Ess.Microservice.Core.Services.Audit;
using Bdo.Ess.Microservice.Core.Utilities;
using Bdo.Ess.Microservice.Reporting.Services.InfoExchangeWorkFlow;
using Bdo.Ess.Services.Audit;
using Bdo.Ess.Services.InformationExchange;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace Bdo.Ess.Microservice.Reporting.Services
{


    public class InformationExchangeService : ServiceBase<CaDbContext, IInformationExchangeMicroservice>, IInformationExchangeMicroservice
    {

        private EssSession _essSession;
        private IConfiguration _configuration;
        public static string DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
        public static string BVI_ISO_CODE = "VG";
        private readonly MicroserviceClient<ICaAuditMicroservice> _caAuditMicroservice;
        private const string NOTIN = "NOTIN";
        private static readonly XmlSerializerNamespaces NAMESPACE = new XmlSerializerNamespaces(new XmlQualifiedName[] {
            new XmlQualifiedName("ntj", "urn:oecd:ties:ntj:v1"),
            new XmlQualifiedName("stf", "urn:oecd:ties:ntjstf:v1"),
            new XmlQualifiedName("xsi", "urn:oecd:ties:isontjtypes:v1"),
        });

        private static readonly string IN_FreeType = "FreeText";

        private static IMapper Mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Country, CountryDto>();
        }).CreateMapper();

        private readonly ILoadExchangeStatus _loadExchangeStatus;

        private readonly int[] EconomicStatusIncluded = { (int)EconomicSubstanceStatus.Submitted, (int)EconomicSubstanceStatus.ReSubmitted };


        public InformationExchangeService(UnitOfWork unitOfWork,
                                            EssSession essSession,
                                            IConfiguration configuration,
                                            ILoadExchangeStatus loadExchangeStatus,
                                            MicroserviceClient<ICaAuditMicroservice> caAuditMicroservice,
              ILogger<InformationExchangeService> logger) : base(unitOfWork, essSession, logger)
        {
            _essSession = essSession;
            _configuration = configuration;
            _caAuditMicroservice = caAuditMicroservice;
            _loadExchangeStatus = loadExchangeStatus;
        }

        private static Func<AddressIE, string> AddressString = x =>
        {
            return !string.IsNullOrEmpty(x.AddressLine2) ?
                        $"{x.AddressLine1} , {x.AddressLine2} , {x.Country.CountryName}" :
                        $"{x.AddressLine1} ,  {x.Country.CountryName}";
        };


        private async Task GetAddressFromBoss(InformationExchangeDto input)
        {
            Logger.LogDebug("In GetAddressFromBoss");

            try
            {
                var ctspNumber = GetSessionValue<string>(EssSessionKeys.CtspNumber);

                Logger.LogDebug($"In GetAddressFromBoss--ctspNumber {ctspNumber}");

                var repoCtsp = GetRepository<Bdo.Ess.Authorization.Users.Ctsp>();

                var ctspinfo = await repoCtsp.Query(x => x.Number == ctspNumber).FirstOrDefaultAsync();

                var entityIntegrationGetAddressUrl = _configuration["Bosss:EntityIntegrationGetAddress"];

                Logger.LogDebug($"In GetAddressFromBoss--entityIntegrationGetAddressUrl {entityIntegrationGetAddressUrl}");

                Logger.LogDebug($"ctspinfo--ctspinfo.BOSSsDataManagerBaseUrl {ctspinfo.BOSSsDataManagerBaseUrl}");
                if (string.IsNullOrWhiteSpace(ctspinfo.BOSSsDataManagerBaseUrl))
                {
                    Logger.LogDebug($"Exception does not have a boss url");
                    throw new Exception($"ctsp {ctspinfo.Number} does not have a boss url");
                }
                else if (string.IsNullOrWhiteSpace(entityIntegrationGetAddressUrl))
                {
                    Logger.LogDebug($"Exception does not have a entityIntegrationGetAddressUrl");
                    throw new Exception($"EntityIntegrationGetAddress not configured");
                }

                var result = Common.Routing.RoutingHelper.UrlCombine(new[] { ctspinfo.BOSSsDataManagerBaseUrl, entityIntegrationGetAddressUrl });


                Logger.LogDebug($"Boss URL and the coperationEnityID {result} - {input.ExternalReferenceId} ");


                var resultAdd = Common.Helpers.RestHelper.Post<Bdo.Ess.Dtos.Integration.AddressDto>(Common.Routing.RoutingHelper.UrlCombine(new[]
                { ctspinfo.BOSSsDataManagerBaseUrl, entityIntegrationGetAddressUrl }), input.ExternalReferenceId).Result;

                Logger.LogDebug($"Get the address of Entity {input.ExternalReferenceId} for Entity Name {input.EntityName}");
                if (resultAdd != null)
                {
                    Logger.LogDebug("Address is null");
                    input.InformationExchangeDetail.RegisteredAddress.AddressLine1 = resultAdd.AddressLine1;
                    input.InformationExchangeDetail.RegisteredAddress.AddressLine2 = resultAdd.AddressLine2;
                }


                input.InformationExchangeDetail.RegisteredAddressString = AddressString(input.InformationExchangeDetail.RegisteredAddress);

                Logger.LogDebug($"RegisteredAddressString is {input.InformationExchangeDetail.RegisteredAddressString}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Can't get the Boss entity address");
            }

        }


        /// <summary>
        /// Update the status of the declartion to reopen so we don't include it in the dashboard.
        /// </summary>
        /// <param name="EsId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateESInformationDclartionStatus(Guid EsId)
        {
            var repo = GetRepository<EssInformationExchange>();
            var result = await repo.Query(x => x.EconomicSubstanceId == EsId && !x.IsDeleted).FirstOrDefaultAsync();

            if (result != null)
            {
                result.EconomicSubstanceStatus = EconomicSubstanceStatus.ReOpen;
                repo.Update(result);
                await CommitAsync();
            }
            return true;
        }
        /// <summary>
        /// If the record is already exists and change have be done in the status
        /// Need to create a new history 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<bool> AddCaInformationExchange(InformationExchangeDto input)
        {
            Logger.LogInformation("AddCaInformationExchange Starting");
            var repo = GetRepository<EssInformationExchange>();
            var repoHistory = GetRepository<EssInformationExchangeHistory>();
            var historyObject = new EssInformationExchangeHistory();           

            await GetAddressFromBoss(input);
            var informationExchange = input.MapToInformationExchange();
            var result = await repo.Query(x => x.EconomicSubstanceId == input.EconomicSubstanceId &&
                                                !x.IsDeleted).FirstOrDefaultAsync();

            // Only allow the migration to run once for a declaration
            // Do not overwrite migrations.
            if (input.IsMigration && result != null)
            {
                return false;
            }

            var user = _essSession.GetData<string>(EssSessionKeys.UserName) ?? "Unknown User";
            bool isChanged = false;

            if (result == null && !input.IsPassDeclaration)
            {
                informationExchange.CreatedAt = DateTime.UtcNow;
                informationExchange.CreatedBy = user;
                repo.Add(informationExchange);
                isChanged = true;
            }
            else
            {
                if (result != null)
                {
                    historyObject.InformationExchangeId = result.Id;
                    historyObject.EssInformationExchangeObject = result.ConvertObjectToCompressedJson();
                    historyObject.InformationExchangeUpdateTime = result.UpdatedAt ?? result.CreatedAt;
                    historyObject.CreatedAt = DateTime.UtcNow;
                    historyObject.UpdatedBy = user;
                    informationExchange.ExchangeReason = input.IsPassDeclaration ? result.ExchangeReason : informationExchange.ExchangeReason;
                    informationExchange.CreatedAt = result.CreatedAt;
                    informationExchange.CreatedBy = result.CreatedBy;
                    informationExchange.UpdatedAt = DateTime.UtcNow;
                    informationExchange.UpdatedBy = user;
                    informationExchange.Id = result.Id;
                    informationExchange.InformationExchangeStatus = InformationExchangeStatus.WaitingReview;

                    repo.Update(informationExchange);
                    repoHistory.Add(historyObject);
                    isChanged = true;
                }

            }
            await CommitAsync();

            if (isChanged)
                await _caAuditMicroservice.CaAudit(AuditType.OnlyNew, AuditEntity.CA_AddInformationExchange, user, null, input,
                    entityName: input.EntityName, fiscalYear: input.FiscalEndDate.Year);

            Logger.LogInformation("AddCaInformationExchange Completed");

            return true;
        }


        public async Task<List<InformationExchangeHistoryDto>> GetInformationHistoryList(SingleFieldInput<Guid> input)
        {
            var repoHistory = GetRepository<EssInformationExchangeHistory>();
            var historyList = await repoHistory.Query(x => x.InformationExchangeId.Equals(input.Value)).ToListAsync();

            var result = historyList?.Select(x => new InformationExchangeHistoryDto()
            {
                Id = x.Id,
                InformationExchage = x?.EssInformationExchangeObject?.ConvertCompressedJsonToObject<EssInformationExchange>().MapToInformationExchangeDto(),
                CreatedAt = x.CreatedAt
            }
            ).ToList();

            return result ?? new List<InformationExchangeHistoryDto>();
        }

        public async Task<InformationExchangeDto> GetInformationExchangeHistoryDetail(SingleFieldInput<Guid> input)
        {
            var repoHistory = GetRepository<EssInformationExchangeHistory>();
            var result = await repoHistory.Query(x => x.Id.Equals(input.Value)).FirstOrDefaultAsync();
            var resultDto = result?.EssInformationExchangeObject?.ConvertCompressedJsonToObject<EssInformationExchange>()?.MapToInformationExchangeDto();

            return resultDto ?? new InformationExchangeDto();
        }

        public async Task<PagedResultDto<InformationExchangeDto>> GetAllInformationExchange(GetInformationExchangeInput input)
        {
            var repo = GetRepository<EssInformationExchange>();
            var result = await repo.Query(x => EconomicStatusIncluded.Contains((int)x.EconomicSubstanceStatus)).ToListAsync();
            var mappedDto = result.MapToInformationExchangeDto();

            return new PagedResultDto<InformationExchangeDto>(mappedDto.Count, mappedDto.Skip(input.SkipCount).Take(input.MaxResultCount).ToArray());
        }



        public async Task<PagedResultDto<InformationExchangeDto>> GetInformationExchangeByFilter(InformationExchangeFilterInput input)
        {
            var repo = GetRepository<EssInformationExchange>();
            var result = new List<EssInformationExchange>();
            var yearint = Int32.Parse(input.Year);

            if (input.Status != InformationExchangeStatus.None)

                result = await repo.Query(x => x.FiscalEndDate.Year.Equals(yearint) &&
                                               x.InformationExchangeStatus.Equals(input.Status) &&
                                               EconomicStatusIncluded.Contains((int)x.EconomicSubstanceStatus)).Include(x => x.EssInformationExchangeHistory).OrderBy(input.Sorting ?? "EntityName ASC")
                                               .ToListAsync();
            else
                result = await repo.Query(x => x.FiscalEndDate.Year.Equals(yearint) &&
                                           EconomicStatusIncluded.Contains((int)x.EconomicSubstanceStatus)).Include(x => x.EssInformationExchangeHistory).OrderBy(input.Sorting ?? "EntityName ASC").ToListAsync();

            var mappedDto = result.MapToInformationExchangeDto();

            return new PagedResultDto<InformationExchangeDto>(mappedDto.Count, mappedDto.Skip(input.SkipCount).Take(input.MaxResultCount).ToArray());

        }



        public async Task<List<InformationExchangeTypeOutput>> GetnformationForByType(InformationExchangeTypeInput input)
        {
            var repo = GetRepository<EssInformationExchange>();
            var result = new List<EssInformationExchange>();
            var yearint = Int32.Parse(input.Year);


            try
            {
                result = await repo.Query(
                                            x => x.FiscalEndDate.Year.Equals(yearint)
                                            && x.ExchangeReason.Equals(input.ExchangeReason)
                                             && x.InformationExchangeStatus.Equals(InformationExchangeStatus.ReadyForExchange)
                                            && EconomicStatusIncluded.Contains((int)x.EconomicSubstanceStatus)
                                            ).ToListAsync();

                if (!result.Any())
                {
                    Logger.LogDebug("No Item found to report.");
                    return new List<InformationExchangeTypeOutput>();
                }
            }
            catch (Exception ex)
            {
                Logger.LogDebug(ex, "Unable to query EssInformationExchange");
                throw ex;
            }

            // can generate the XML base on the input.ExchangeReason pass to the client

            var mappedDto = result.Select(x => x.MapToInformationExchangeDto()).ToList();
            var tempDirectory = String.Format("{0}-{1}_{2}", input.Year, input.ExchangeReason.ToString(), DateTime.Now.ToString("yyyy-MM-ddThh-mm-ss"));


            try
            {
                await MapDetailsToXml(mappedDto, tempDirectory);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Unable to Map Details to XML");
                throw;
            }


            // need to change the status of the information exchange

            result?.ForEach(x =>
            {
                x.InformationExchangeStatus = InformationExchangeStatus.InformationExchange;
                repo.Update(x);
            });

            await CommitAsync();


            var content = CompressExchangeXML(tempDirectory);
            return content;
        }


        public async Task<InformationExchangeSummary> GetSummaryByYear(SingleFieldInput<string> input)
        {
            InformationExchangeSummary summary = new InformationExchangeSummary();
            var repo = GetRepository<EssInformationExchange>();
            var yearint = Int32.Parse(input.Value);
            var result = await repo.Query(x => x.FiscalEndDate.Year.Equals(yearint) &&
                                          EconomicStatusIncluded.Contains((int)x.EconomicSubstanceStatus)).ToListAsync();

            summary.TotalNoofReports = result?.Count() ?? 0;
            summary.TotalNoofExchangedReports = result?.Where(x => x.InformationExchangeStatus.Equals(InformationExchangeStatus.InformationExchange)).Count() ?? 0;
            summary.TotalNoofReadyExchangedReports = result?.Where(x => x.InformationExchangeStatus.Equals(InformationExchangeStatus.ReadyForExchange)).Count() ?? 0;

            summary.TotalNoofReviewReports = result?.Where(x => x.InformationExchangeStatus.Equals(InformationExchangeStatus.NotStarted) ||
                                                                x.InformationExchangeStatus.Equals(InformationExchangeStatus.WaitingForAppeal) ||
                                                                x.InformationExchangeStatus.Equals(InformationExchangeStatus.WaitingReview)).Count() ?? 0;


            summary.TotalNotSentReports = result?.Where(x => x.InformationExchangeStatus.Equals(InformationExchangeStatus.NotRequired)).Count() ?? 0;

            return summary;


        }



        public async Task<InformationExchangeDto> GetInformationExchange(SingleFieldInput<Guid> input)
        {
            var repo = GetRepository<EssInformationExchange>();
            var result = await repo.Query(x => x.Id == input.Value).FirstOrDefaultAsync();
            var mappedDto = result.MapToInformationExchangeDto();
            return mappedDto;
        }

        public async Task<bool> UpdateCaInformationExchange(InformationExchangeDto input)
        {
            var oldObject = await this.GetInformationExchange(new SingleFieldInput<Guid>() { Value = input.Id });

            var repo = GetRepository<EssInformationExchange>();
            var informationExchange = input.MapToInformationExchange();
            var user = _essSession.GetData<string>(EssSessionKeys.UserName) ?? "Unknown User";

            informationExchange.UpdatedAt = DateTime.UtcNow;
            informationExchange.UpdatedBy = user;
            informationExchange.CreatedAt = informationExchange.CreatedAt;
            informationExchange.CreatedBy = informationExchange.CreatedBy;

            repo.Update(informationExchange);
            await CommitAsync();

            await _caAuditMicroservice.CaAudit(AuditType.Full, AuditEntity.CA_UpdateInformationExchange, user, oldObject, input,
                entityName: input.EntityName, fiscalYear: input.FiscalEndDate.Year);
            return true;
        }


        public async Task<List<CountryDto>> GetAllCountries(NoInput input)
        {
            var repo = GetRepository<Country>();

            var result = await repo.Query().ToListAsync();

            return Mapper.Map<List<CountryDto>>(result);
        }


        public async Task<List<ExchangeStatus>> GetExchangeStatus(InformationExchangeStatusInput input)
        {
            var result = _loadExchangeStatus.GetExchangeStatus();
            var filteredList = result.Where(x => x.CurrentStatus == input.InformationExchangeStatus && x.ExchangeReason == input.ExchangeReason).ToList();
            return filteredList;
        }

        private List<InformationExchangeTypeOutput> CompressExchangeXML(string directory)
        {
            string dirPath = String.Format(@"{0}/{1}", Directory.GetCurrentDirectory(), directory);
            string zipPath = String.Format(@"{0}/{1}.zip", Directory.GetCurrentDirectory(), directory);

            if (!Directory.EnumerateFileSystemEntries(dirPath).Any()) return null;

            var response = new List<InformationExchangeTypeOutput>();

            ZipFile.CreateFromDirectory(dirPath, zipPath);
            FileStream fs = new FileStream(zipPath, FileMode.Open, FileAccess.Read);
            byte[] data = new byte[fs.Length];
            fs.Read(data, 0, Convert.ToInt32(fs.Length));

            fs.Close();
            Directory.Delete(dirPath, true);
            File.Delete(zipPath);

            response.Add(new InformationExchangeTypeOutput()
            {
                Content = data,
                FileName = String.Format("{0}.zip", directory)
            });

            return response;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="exchanges"></param>
        /// <param name="directory"></param>
        /// <returns></returns>
        /// <remarks>
        /// •	One file is generated per country mentioned in recipient jurisdiction of residence plus entity’s jurisdiction of residence for non-residence case.
        /// •	Exchange Nexus section refers to which related person gave rise to the exchange and Related Persons would include none or some other recipients based on the logic below.
        /// •	Out of all the recipients for the reportable entity (check in this order to determine who goes under Exchange Nexus):
        ///    o Scenario 1: If the exchange reason is “Non Residence” and the entity is resident in the country then the entity itself goes under Exchange Nexus and any recipients go under Related Persons section.
        ///    o Scenario 2: If there is one “Ultimate Beneficial Owner” resident in the country then that recipient goes into the Exchange Nexus section, any other recipients go into the Related Persons section regardless of their jurisdiction (if there are multiple “Ultimate Beneficial Owner” resident in the country then randomly pick one to go into the Exchange Nexus section, any other recipients go into the Related Persons section)
        ///    o Scenario 3: If there is “Ultimate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Immediate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        ///    o Scenario 4: If there is “Immediate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Ultimate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        /// •	Under both Exchange Nexus and Related Person, each recipient should be under the NTJ Person object. If UBO then the “UBO” element should be populated with a “PersonParty_Type” format and if Ultimate/Immediate Parent then the “Entity” element should be populated with a “OrganisationParty_Type” format.
        /// </remarks>
        private async Task<List<EssInformationExchangeXML>> MapDetailsToXml(List<InformationExchangeDto> exchanges, string directory)
        {
            string path = String.Format(@"{0}/{1}", Directory.GetCurrentDirectory(), directory);
            Directory.CreateDirectory(path);

            var countriesMap = GetRepository<Country>().Query(c => c.CountryCode2 != null).ToDictionary(c => c.CountryCode, c => c);
            var transmittingCountry = countriesMap.FirstOrDefault(c => c.Value.CountryCode2 == BVI_ISO_CODE).Value;
            var oECDESSIdMap = new Dictionary<NTJ_OECD, List<Guid>>();
            var initalizeResultTuple = InitialAllOECD(exchanges, exchanges.FirstOrDefault().FiscalEndDate.Year, transmittingCountry, countriesMap);
            var resultOECDs = initalizeResultTuple.Item1;
            var sequenceNumberMap = initalizeResultTuple.Item2;

            var bodies = new Dictionary<string, List<CorrectableNtjBody_Type>>();
            foreach (InformationExchangeDto exchange in exchanges)
            {
                try
                {
                    var recipientGroups = exchange.InformationExchangeDetail.RecipientDetails.GroupBy(r => r.JurisdictionResidence.CountryCode).ToDictionary(g => g.Key, g => g.ToList());
                    if (exchange.ExchangeReason == ExchangeReason.NonResidence)
                    {
                        AppendEntityNexusToCountryOECD(exchange, transmittingCountry, resultOECDs, bodies, oECDESSIdMap, sequenceNumberMap, countriesMap);
                    }

                    foreach (var recipients in recipientGroups)
                    {
                        AppendToCountryOECD(exchange, recipients, resultOECDs, bodies, oECDESSIdMap, sequenceNumberMap);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error when export exchange [{EntityName}](ID: {ExchangeId} )", exchange.EntityName, exchange.Id);
                }
            }

            var xmlExchangeList = new List<EssInformationExchangeXML>();

            foreach (var oecd in resultOECDs)
            {
                // assign bodies to each OECD
                oecd.Value.NtjBody = bodies[oecd.Key].ToArray();

                // serial oecd object to xml then save to file
                var xml = GetXMLString(oecd.Value, NAMESPACE);

                var fileName = String.Format(@"{0}/{1}/{2}-{3}-{4}.xml", Directory.GetCurrentDirectory(), directory, DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss"), BVI_ISO_CODE, oecd.Key);
                SaveXmlToFile(fileName, xml);

                // add EssInformationExchangeXML will save to DB
                xmlExchangeList.Add(GetEssInformationExchangeXMLRecord(oecd, transmittingCountry, countriesMap, sequenceNumberMap, oECDESSIdMap, xml));
            }

            // save all record to DB
            var repo = GetRepository<EssInformationExchangeXML>();
            xmlExchangeList.ForEach(v => repo.Add(v));
            await CommitAsync();

            return xmlExchangeList;
        }

        private EssInformationExchangeXML GetEssInformationExchangeXMLRecord(KeyValuePair<string, NTJ_OECD> oecd, Country transmittingCountry, Dictionary<string, Country> countriesMap, Dictionary<string, int> sequenceNumberMap, Dictionary<NTJ_OECD, List<Guid>> oECDESSIdMap, string xml)
        {
            return new EssInformationExchangeXML()
            {
                CreatedAt = DateTime.Now,
                EssIds = String.Join(",", oECDESSIdMap[oecd.Value].Distinct()),
                TransmittingCountryId = transmittingCountry.Id,
                ReceivingCountryId = countriesMap[oecd.Key].Id,
                SequenceNumber = sequenceNumberMap[oecd.Key],
                XMLString = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(xml))
            };
        }

        private string GetXMLString(NTJ_OECD oecd, XmlSerializerNamespaces ns)
        {
            XmlSerializer xsSubmit = new XmlSerializer(typeof(NTJ_OECD));
            var xml = string.Empty;
            using (var sww = new Utf8StringWriter())
            {
                using (XmlWriter writer = XmlWriter.Create(sww))
                {
                    xsSubmit.Serialize(writer, oecd, ns);
                    xml = sww.ToString();
                }
            }

            return xml;
        }

        private void SaveXmlToFile(string fileName, string xml)
        {
            using (var fileStream = new FileStream(fileName, FileMode.Create))
            {
                using (var fsw = new StreamWriter(fileStream))
                {
                    fsw.Write(xml);
                }
            }
        }

        /// <summary>
        /// Append to OECD Body bay country
        /// Handle cases
        ///    o Scenario 2: If there is one “Ultimate Beneficial Owner” resident in the country then that recipient goes into the Exchange Nexus section, any other recipients go into the Related Persons section regardless of their jurisdiction (if there are multiple “Ultimate Beneficial Owner” resident in the country then randomly pick one to go into the Exchange Nexus section, any other recipients go into the Related Persons section)
        ///    o Scenario 3: If there is “Ultimate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Immediate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        ///    o Scenario 4: If there is “Immediate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Ultimate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        /// </summary>
        /// <param name="exchange"></param>
        /// <param name="recipients"></param>
        /// <param name="country_OECD"></param>
        /// <param name="country_bodyList"></param>
        /// <param name="oECDESSIdMap"></param>
        /// <param name="sequenceNumberMap"></param>
        private void AppendToCountryOECD(InformationExchangeDto exchange, KeyValuePair<string, List<RecipientDetailsDto>> recipients, Dictionary<string, NTJ_OECD> country_OECD, Dictionary<string, List<CorrectableNtjBody_Type>> country_bodyList, Dictionary<NTJ_OECD, List<Guid>> oECDESSIdMap, Dictionary<string, int> sequenceNumberMap)
        {
            var oecd = country_OECD[recipients.Key];
            // OECD map to ESS id need for our record
            if (!oECDESSIdMap.ContainsKey(oecd))
            {
                oECDESSIdMap.Add(oecd, new List<Guid>());
            }

            // body List by recipient country code
            if (!country_bodyList.ContainsKey(recipients.Key))
            {
                country_bodyList.Add(recipients.Key, new List<CorrectableNtjBody_Type>());
            }
            var bodies = country_bodyList[recipients.Key];
            var nexusPerson = GetNexusPersonFromRecipients(recipients.Value);
            if (nexusPerson != null)
            {
                if (!oECDESSIdMap[oecd].Contains(exchange.Id))
                {
                    oECDESSIdMap[oecd].Add(exchange.Id);

                    // related persons means BOs in the same exchange (entity) but not *person* self.
                    var releatedPersons = exchange.InformationExchangeDetail.RecipientDetails.Where(p =>
                        // not the nexus itself
                        p.id != nexusPerson.id
                        // If nexus is not UBO, filter out all UBOs
                        && (nexusPerson.TypeOfEntityPerson == EntityPersonType.UltimateBO || p.TypeOfEntityPerson != EntityPersonType.UltimateBO))
                        .ToList();

                    var correctableNtjBody = new CorrectableNtjBody_Type()
                    {
                        ExchangeNexus = GetNTJPerson(exchange, nexusPerson),
                        ReportableEntity = GetReportableEntity(exchange),
                        RelatedPerson = GetNTJPersons(exchange, releatedPersons),
                        // not support DocSpec?
                        DocSpec = new DocSpec_Type()
                        {
                            DocTypeIndic = OECDDocTypeIndic_EnumType.OECD1,
                            DocRefId = $"{BVI_ISO_CODE}{sequenceNumberMap[recipients.Key]}"
                        }
                    };

                    bodies.Add(correctableNtjBody);
                }
            }
        }

        /// <summary>
        /// Get one of the Recipient as Nexus for
        ///    o Scenario 2: If there is one “Ultimate Beneficial Owner” resident in the country then that recipient goes into the Exchange Nexus section, any other recipients go into the Related Persons section regardless of their jurisdiction (if there are multiple “Ultimate Beneficial Owner” resident in the country then randomly pick one to go into the Exchange Nexus section, any other recipients go into the Related Persons section)
        ///    o Scenario 3: If there is “Ultimate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Immediate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        ///    o Scenario 4: If there is “Immediate Parent” resident in the country then that recipient goes into the Exchange Nexus section, any “Ultimate Parent” recipients go into the Related Persons section regardless of their jurisdiction – UBOs don’t need to be included(if there are multiple then pick one for Exchange Nexus and rest into Related Persons)
        /// </summary>
        /// <param name="recipients"></param>
        /// <returns></returns>
        private RecipientDetailsDto GetNexusPersonFromRecipients(List<RecipientDetailsDto> recipients)
        {
            return recipients.FirstOrDefault(r => r.TypeOfEntityPerson == EntityPersonType.UltimateBO) ??
                    recipients.FirstOrDefault(r => r.TypeOfEntityPerson == EntityPersonType.UltimateParent) ??
                    recipients.FirstOrDefault(r => r.TypeOfEntityPerson == EntityPersonType.ImmediateParent) ??
                    recipients.FirstOrDefault(r => r.TypeOfEntityPerson == EntityPersonType.ReportableEntity);
        }

        /// <summary>
        /// Get Nexus from Exchange Entity
        ///    o Scenario 1: If the exchange reason is “Non Residence” and the entity is resident in the country then the entity itself goes under Exchange Nexus and any recipients go under Related Persons section.
        /// </summary>
        /// <param name="exchange"></param>
        /// <param name="transmittingCountry"></param>
        /// <param name="country_OECD"></param>
        /// <param name="country_bodyList"></param>
        /// <param name="oECDESSIdMap"></param>
        /// <param name="sequenceNumberMap"></param>
        /// <param name="countriesMap"></param>
        private void AppendEntityNexusToCountryOECD(InformationExchangeDto exchange, Country transmittingCountry, Dictionary<string, NTJ_OECD> country_OECD, Dictionary<string, List<CorrectableNtjBody_Type>> country_bodyList, Dictionary<NTJ_OECD, List<Guid>> oECDESSIdMap, Dictionary<string, int> sequenceNumberMap, Dictionary<string, Country> countriesMap)
        {
            // if we use the Entity as ExchangeNexus, we should create report to the Jurisdiction Tax ResidentCode (Question 8b in declaration!!)
            var entityCountryCode = exchange.InformationExchangeDetail?.JurisdictionTaxResidentCode
                    // if Tax Identification Country is not set, we use InformationExchangeDetail?.RegisteredAddress (the it shouldn't happened for non-resident)
                    ?? exchange.InformationExchangeDetail?.RegisteredAddress?.Country?.CountryCode;

            if (!country_OECD.ContainsKey(entityCountryCode))
            {
                country_OECD.Add(entityCountryCode, GetOECD(entityCountryCode, transmittingCountry.Id, countriesMap[entityCountryCode].Id, exchange.FiscalEndDate.Year, sequenceNumberMap));
            }

            var oecd = country_OECD[entityCountryCode];
            // OECD map to ESS id need for our record
            if (!oECDESSIdMap.ContainsKey(oecd))
            {
                oECDESSIdMap.Add(oecd, new List<Guid>());
            }

            if (!oECDESSIdMap[oecd].Contains(exchange.Id))
            {
                oECDESSIdMap[oecd].Add(exchange.Id);
                // body List by recipient country code
                if (!country_bodyList.ContainsKey(entityCountryCode))
                {
                    country_bodyList.Add(entityCountryCode, new List<CorrectableNtjBody_Type>());
                }


                var bodies = country_bodyList[entityCountryCode];
                // related persons means BOs in the same exchange (entity) but not *person* self.
                var releatedPersons = exchange.InformationExchangeDetail.RecipientDetails.ToList();

                var correctableNtjBody = new CorrectableNtjBody_Type()
                {
                    ExchangeNexus = GetNTJNexusFromExchange(exchange),
                    ReportableEntity = GetReportableEntity(exchange),
                    RelatedPerson = GetNTJPersons(exchange, releatedPersons),
                    // not support DocSpec?
                    DocSpec = new DocSpec_Type()
                    {
                        DocTypeIndic = OECDDocTypeIndic_EnumType.OECD1,
                        DocRefId = $"{BVI_ISO_CODE}{sequenceNumberMap[entityCountryCode]}"
                    }
                };

                bodies.Add(correctableNtjBody);

            }
        }

        private NtjPerson_Type GetNTJNexusFromExchange(InformationExchangeDto exchange)
        {
            var result = new NtjPerson_Type()
            {
                Nexus = GetNexusCode(EntityPersonType.ReportableEntity, true),
                ID = new NtjPerson_TypeID()
                {
                    Item = GetEntity(exchange)
                }
            };

            return result;
        }

        private NtjPerson_Type[] GetNTJPersons(InformationExchangeDto exchange, List<RecipientDetailsDto> value)
        {
            return value.Select(p => GetNTJPerson(exchange, p)).ToArray();
        }

        private NtjPerson_Type GetNTJPerson(InformationExchangeDto exchange, RecipientDetailsDto recipient)
        {
            return new NtjPerson_Type()
            {

                Nexus = GetNexusCode(recipient.TypeOfEntityPerson, exchange.InformationExchangeDetail.RegisteredAddress.Country.CountryCode == recipient.JurisdictionResidence.CountryCode),
                ID = GetNtjPersonId(recipient)
            };
        }

        private NtjPerson_TypeID GetNtjPersonId(RecipientDetailsDto recipient)
        {
            object item = null;
            if (recipient.TypeOfEntityPerson == EntityPersonType.UltimateBO)
            {
                item = GetUBOPersonId(recipient);
            }
            else
            {
                item = GetEntityPersionId(recipient);
            }


            return new NtjPerson_TypeID()
            {
                Item = item
            };
        }

        private object GetEntityPersionId(RecipientDetailsDto recipient)
        {
            var regCountryCode = GetCountryType(recipient.JurisdictionResidence?.CountryCode);
            var organisationParty_Type = new OrganisationParty_Type()
            {
                Name = new NameOrganisation_Type[] { new NameOrganisation_Type() { Value = recipient.NameEntityPerson } },
                ResCountryCode = (new CountryCode_Type?[] { regCountryCode }).Where(v => v.HasValue).Select(v => v.Value).ToArray(),
                Address = new Address_Type[] { FormatAddress(recipient.Address) }
            };

            var tinCountry = GetCountryType(recipient.TINIssuedCoutry?.CountryCode);
            organisationParty_Type.TIN = new TIN_Type()
            {
                Value = GetStringValueIfEmptyDefault(recipient.TIN, NOTIN),
            };
            if (!string.IsNullOrEmpty(recipient.OtherIdentification))
            {
                organisationParty_Type.IN = new OrganisationIN_Type[]
                {
                    new OrganisationIN_Type()
                    {
                         Value = recipient.OtherIdentification,
                         INType = IN_FreeType
                    }
                };
            }

            return organisationParty_Type;
        }

        private object GetUBOPersonId(RecipientDetailsDto recipient)
        {
            var regCountryCode = GetCountryType(recipient.JurisdictionResidence?.CountryCode);
            var personParty_Type = new PersonParty_Type()
            {
                Address = new Address_Type[] { FormatAddress(recipient.Address) },
                Name = new NamePerson_Type[] { GetPersonName(recipient) },
                ResCountryCode = (new CountryCode_Type?[] { regCountryCode }).Where(v => v.HasValue).Select(v => v.Value).ToArray(),
                UboType = GetUBOTypeEnum(recipient)
            };

            // populate TIN if exists
            personParty_Type.TIN = new TIN_Type[] {
                GetTin_Type(recipient.TIN, recipient.TINIssuedCoutry)
            };

            return personParty_Type;
        }

        /// <summary>
        /// Get TIN Type
        /// </summary>
        /// <param name="tIN"></param>
        /// <param name="tINIssuedCoutry"></param>
        /// <returns></returns>
        private TIN_Type GetTin_Type(string tIN, CountryDto tINIssuedCoutry)
        {
            var result = new TIN_Type()
            {
                Value = GetStringValueIfEmptyDefault(tIN, NOTIN)
            };

            var tinCountry = GetCountryType(tINIssuedCoutry?.CountryCode);
            if (tinCountry.HasValue)
            {
                result.issuedBy = tinCountry.Value;
                result.issuedBySpecified = true;
            }

            return result;
        }

        private UboType_EnumType[] GetUBOTypeEnum(RecipientDetailsDto recipient)
        {
            var results = new List<UboType_EnumType>();
            if (recipient.UBOType.HasValue)
            {
                var typeString = GetUBOType(recipient.UBOType.Value);
                try
                {
                    results.Add(EnumHelper.ToEnumValue<UboType_EnumType>(typeString, false));
                }
                catch
                {
                    Logger.LogDebug($"{typeString} not found from {nameof(UboType_EnumType)}.");
                }
            }

            return results.ToArray();
        }

        private NamePerson_Type GetPersonName(RecipientDetailsDto recipient)
        {
            string[] parts = recipient.NameEntityPerson.Split(' ');
            string firstName = null, lastName = null;
            if (parts.Count() == 1)
            {
                lastName = recipient.NameEntityPerson;
                firstName = "NFN";
            }
            else
            {
                firstName = parts.First();
                lastName = recipient.NameEntityPerson.Substring(firstName.Length).Trim();
            }


            return new NamePerson_Type()
            {
                FirstName = new NamePerson_TypeFirstName() { Value = firstName },
                LastName = new NamePerson_TypeLastName() { Value = lastName }
            };
        }

        private ReportableEntity_Type GetReportableEntity(InformationExchangeDto exchange)
        {
            var entity = GetEntity(exchange);

            var report = new ReportableEntity_Type()
            {
                Entity = entity,
                ReportingReason = GetReportingReason(exchange.ExchangeReason),
                Period = new ReportableEntity_TypePeriod() { EndDate = exchange.InformationExchangeDetail.FiscalEndDate, StartDate = exchange.InformationExchangeDetail.FiscalStartDate },
                Activities = GetActivities(exchange),
                Summary = (new StringMin1Max4000WithLang_Type[] { GetSummary(exchange) }).Where(v => v != null).ToArray(),
                NameGroup = !string.IsNullOrEmpty(exchange.InformationExchangeDetail?.MNEGroupName) ? exchange.InformationExchangeDetail?.MNEGroupName : null
            };

            return report;
        }

        private OrganisationParty_Type GetEntity(InformationExchangeDto exchange)
        {
            var address = new List<Address_Type>();
            var regCuntry = GetCountryType(exchange.InformationExchangeDetail?.RegisteredAddress?.Country?.CountryCode);

            var entity = new OrganisationParty_Type()
            {
                Name = new NameOrganisation_Type[] { new NameOrganisation_Type() { Value = exchange.EntityName } },
                ResCountryCode = new CountryCode_Type[] { },
                TIN = GetTin_Type(exchange?.InformationExchangeDetail?.TaxIdentificationNo, exchange?.InformationExchangeDetail?.TaxIdentificationCountry)
            };

            // Populate ResCountryCode if exists
            if (regCuntry.HasValue)
            {
                entity.ResCountryCode = new CountryCode_Type[] { regCuntry.Value };
            }

            // Populate Address
            address.Add(FormatAddress(exchange.InformationExchangeDetail.RegisteredAddress, OECDLegalAddressType_EnumType.OECD304));

            // Populate IN if exists
            if (!string.IsNullOrEmpty(exchange.InformationExchangeDetail.OtherIdentificationNo))
            {
                var orgIn = GetIN_Type(exchange.InformationExchangeDetail);
                entity.IN = new OrganisationIN_Type[] { orgIn };
            }

            // Populate other addresses
            if (exchange?.InformationExchangeDetail?.BussinesPrimisess?.Any() ?? false)
            {
                address.AddRange(GetAddressFromBusinessPrimisess(exchange.InformationExchangeDetail.BussinesPrimisess));
            }

            entity.Address = address.ToArray();

            return entity;
        }

        /// <summary>
        /// Get IN from informationExchangeDetail
        /// </summary>
        /// <param name="informationExchangeDetail"></param>
        /// <returns></returns>
        private OrganisationIN_Type GetIN_Type(InformationExchangeDetailDto informationExchangeDetail)
        {
            var orgIn = new OrganisationIN_Type()
            {
                Value = informationExchangeDetail.OtherIdentificationNo
            };
            if ((informationExchangeDetail?.OtherIdentificationCountry?.Id ?? Guid.Empty) != Guid.Empty)
            {
                var issueBy = GetCountryType(informationExchangeDetail?.OtherIdentificationCountry?.CountryCode);
                orgIn.issuedBySpecified = issueBy.HasValue;
                if (issueBy.HasValue)
                {
                    orgIn.issuedBy = issueBy.Value;
                }
            }
            if (!string.IsNullOrEmpty(informationExchangeDetail.OtherIdentificationTIN))
            {
                orgIn.INType = informationExchangeDetail.OtherIdentificationTIN;
            }

            return orgIn;
        }

        /// <summary>
        /// Get Summary
        /// </summary>
        /// <param name="exchange"></param>
        /// <returns></returns>
        private StringMin1Max4000WithLang_Type GetSummary(InformationExchangeDto exchange)
        {
            if (!string.IsNullOrEmpty(exchange.InformationExchangeDetail.Summary))
            {
                //string isoLanguageCode = GetISOLanguageCode(exchange.InformationExchangeDetail.RegisteredAddress?.Country?.CountryCode);
                var result = new StringMin1Max4000WithLang_Type()
                {
                    Value = exchange.InformationExchangeDetail.Summary,

                    // language is hardcodded!!!!
                    language = LanguageCode_Type.EN,
                    languageSpecified = true
                };

                return result;
            }

            return null;
        }

        /// <summary>
        /// Get Activities
        /// </summary>
        /// <param name="exchange"></param>
        /// <returns></returns>
        /// <remarks>
        /// From Ticket SysAid # 41116: Basically we don't create Activities tag if there is no KeyData from Declaration activities
        /// 
        /// We are still trying to exchange information with our peers using the xml schema this has proven to be more difficult than we initially expected. In any regards further information has come to our attention about exchanging information on those entities that claim to be non-resident.
        /// It was explained to me that the NTJ Body consists of four main elements:
        /// 1. Reportable Entity: this contains the details on the entity that conducts the relevant activity.
        /// 2. Exchange Nexus: this contains the details on the person giving rise to the exchange with the receiving jurisdiction.
        /// 3. Related Person: this contains the details on all persons in the ownership chain below the person identified under the Exchange Nexus element(i.e.Immediate Parents and Ultimate Parent where applicable).
        /// 4. DocSpec: this contains information on the type of data provided, as well as technical identifying information.
        /// Elements 1, 2 and 4 must always be provided, while element 3 may be omitted.The jurisdiction outlined the following issues that arose when adopting the NTJ XML Schema for claims of non-residence:
        /// Reportable Entity:
        /// * There is no suitable value for the Reporting Reason value.There has been some discussion on this with other jurisdictions and the best way forward seemed to be to use value NTJ805 (Standard – Other Entities).
        /// * Most data for the Activities element is not collected in the case of non-resident entities.While the NTJ XML Schema User Guide indicates that the Activities element is a validation element, in the actual NTJ Schema as available on the OECD website (the xsd file) the Activities element can be omitted.Omitting the Activities element has proven to pass the xml validation, and is consistent with the communication from the FHTP Secretariat that only basic identifying information on the Reportable Entity needs to be exchanged (see attached document).
        /// * The Summary element should not be provided in circumstances other than described in the NTJ XML Schema User Guide.Exchanges with nexus value NTJ704-NTJ707 are not covered by this description, which means that the Summary element should be omitted.
        /// Exchange Nexus:
        /// 
        /// * The Nexus element has specific values for the different situations that can arise in case of non-resident entities: NTJ704-NTJ707.
        /// * When exchanging the information with the jurisdiction of tax residence, the person giving rise to the exchange with the receiving jurisdiction is the same as the Reportable Entity.This means that the relevant data from the Reportable Entity element should be duplicated in the ID element in that case (that case being when the Nexus value is NTJ704).
        /// Following on from this jurisdictions expect these exchanges to occur via the cts using the xml schema.We therefore need to ensure that this happens.
        /// In addition, I know that for all searches it is limited to 100 random results, this cannot work for when we search for applications made for non-resident entities. We have to be able to view all the requests or download them.Now that we are going to have to put these into the xml, there has to be process for us to choose which ones will go into the xml schema and which ones won’t.There are some persons that make the claim and the evidence is not appropriate and it therefore cannot be exchanged. We therefore need to sift those out, maybe different status’ need to be applied.
        /// </remarks>
        private Activities_Type GetActivities(InformationExchangeDto exchange)
        {
            var isoCurrCode = GetCurrencyType(exchange.InformationExchangeDetail.Currency?.CurrencyCode);
            var keydata = GetKeyData(exchange, isoCurrCode);

            // create Activities tab only if there is some activities
            if (keydata != null && keydata.Any())
            {
                return new Activities_Type()
                {
                    // Do not include these two fields for Non-resident Entities
                    AnnualIncome = exchange.ExchangeReason == ExchangeReason.NonResidence ? null : GetMonAmnt_Type(exchange.InformationExchangeDetail.TotalAnnualIncome, isoCurrCode),
                    NetBookValue = exchange.ExchangeReason == ExchangeReason.NonResidence ? null : GetMonAmnt_Type(exchange.InformationExchangeDetail.NetBookValue, isoCurrCode),
                    KeyData = new Activities_TypeKeyData()
                    {
                        Items = keydata
                    }
                };

            }

            return null;
        }

        /// <summary>
        /// Covert RelevantActivityInformationDetail to KeyData
        /// </summary>
        /// <param name="exchange"></param>
        /// <param name="isoCurrCode"></param>
        /// <returns></returns>
        /// <remarks>
        /// If the declare has no activities,
        /// It will add an KeyDataOther with Other activties type and Employee, Expenses and GrossIncome = 00
        /// </remarks>
        private object[] GetKeyData(InformationExchangeDto exchange, currCode_Type isoCurrCode)
        {
            var keyData = new List<object>();

            // if there is any activities is not RelevantActivity.IntelBA, it is not an pure IP company
            var isPureIpActivities = !exchange.InformationExchangeDetail.RelevantActivityInformationDetail.Any(v => v.ReleventActivityValue != RelevantActivity.IntelBA);

            foreach (var activity in exchange.InformationExchangeDetail.RelevantActivityInformationDetail)
            {
                if (activity.ReleventActivityValue == RelevantActivity.IntelBA && isPureIpActivities && exchange.FiscalEndDate.IsLessThan(EssConstants.DeclarationChanges_2_0_Date))
                {
                    keyData.Add(new ActivitiesKeyDataIP_Type()
                    {
                        TypeIncome = ActivitiesKeyDataIP_EnumType.NTJ603, // we only support 603!!
                        Employees = GetRoundDecimalValueString(activity.TotalFullTimeEmployeeInBVI),
                        Expenses = new Expenses_Type()
                        {
                            Direct = GetMonAmnt_Type(activity.TotalExpeditureInBVI, isoCurrCode),
                            Outsourcing = GetMonAmnt_Type(activity.TotalExpenditureIncurredInBVI, isoCurrCode)
                        },
                        GrossIncome = GetMonAmnt_Type(activity.TotalTurnover, isoCurrCode)
                    });

                }
                else if (activity.ReleventActivityValue == RelevantActivity.IntelBA && isPureIpActivities && activity.IsLegalEntityHighRisk && exchange.FiscalEndDate.IsGreaterThanOrEqualTo(EssConstants.DeclarationChanges_2_0_Date))
                {
                    keyData.Add(new ActivitiesKeyDataIP_Type()
                    {
                        TypeIncome = ActivitiesKeyDataIP_EnumType.NTJ601,  // Royalties
                        Employees = null,
                        Expenses = null,
                        GrossIncome = GetMonAmnt_Type(activity.GrossIncomeRoyalities, isoCurrCode)
                    });

                    keyData.Add(new ActivitiesKeyDataIP_Type()
                    {
                        TypeIncome = ActivitiesKeyDataIP_EnumType.NTJ602, // Sale of IP
                        Employees = null,
                        Expenses = null,
                        GrossIncome = GetMonAmnt_Type(activity.GrossIncomeGains, isoCurrCode)
                    });

                    keyData.Add(new ActivitiesKeyDataIP_Type()
                    {
                        TypeIncome = ActivitiesKeyDataIP_EnumType.NTJ603, // Others
                        Employees = null,
                        Expenses = null,
                        GrossIncome = GetMonAmnt_Type(activity.GrossIncomeOthers, isoCurrCode)
                    });
                }
                else
                {
                    keyData.Add(new ActivitiesKeyDataOther_Type()
                    {
                        TypeIncome = GetTypeIncomeEnum(activity.ReleventActivityValue),
                        Employees = GetRoundDecimalValueString(activity.TotalFullTimeEmployeeInBVI),
                        Expenses = new Expenses_Type()
                        {
                            Direct = GetMonAmnt_Type(activity.TotalExpeditureInBVI, isoCurrCode),
                            Outsourcing = GetMonAmnt_Type(activity.TotalExpenditureIncurredInBVI, isoCurrCode)
                        },
                        GrossIncome = GetMonAmnt_Type(activity.TotalTurnover, isoCurrCode)
                    });
                }
            }

            return keyData.ToArray();
        }

        /// <summary>
        /// Round deciaml to nearest dollar and convert to string
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string GetRoundDecimalValueString(decimal? value)
        {
            return $"{Convert.ToInt64(Math.Round(value ?? 0, MidpointRounding.AwayFromZero)):d}";
        }

        /// <summary>
        /// Convert decimal value to MonAmnt
        /// </summary>
        /// <param name="value"></param>
        /// <param name="isoCurrCode"></param>
        /// <returns></returns>
        private MonAmnt_Type GetMonAmnt_Type(decimal? value, currCode_Type isoCurrCode)
        {
            return new MonAmnt_Type()
            {
                currCode = isoCurrCode,
                Value = GetRoundDecimalValueString(value)
            };
        }

        /// <summary>
        /// Convert currency code to <seealso cref="currCode_Type"/>
        /// </summary>
        /// <param name="currencyCode"></param>
        /// <returns></returns>
        private currCode_Type GetCurrencyType(string currencyCode)
        {
            try
            {
                return EnumHelper.ToEnumValue<currCode_Type>(currencyCode, false);
            }
            catch
            {
                Logger.LogDebug($"Currency {currencyCode} not found.");
            }

            // if not know assume it is USD
            return currCode_Type.USD;
        }

        /// <summary>
        /// Convert <seealso cref="ExchangeReason"/> to <seealso cref="ReportingReason_EnumType"/>
        /// </summary>
        /// <param name="exchangeReason"></param>
        /// <returns></returns>
        private ReportingReason_EnumType GetReportingReason(ExchangeReason exchangeReason)
        {
            switch (exchangeReason)
            {
                case ExchangeReason.HighRisk:
                    return ReportingReason_EnumType.NTJ804;
                case ExchangeReason.NonResidence:
                    return ReportingReason_EnumType.NTJ805;
                case ExchangeReason.NonCompliance:
                    return ReportingReason_EnumType.NTJ801;
            }

            // should never reach here!!!
            return ReportingReason_EnumType.NTJ801;
        }

        /// <summary>
        /// Convert to <seealso cref="Address_Type"/> from Business Primisess
        /// </summary>
        /// <param name="bussinesPrimisess"></param>
        /// <returns></returns>
        private IEnumerable<Address_Type> GetAddressFromBusinessPrimisess(string[] bussinesPrimisess)
        {
            var results = new List<Address_Type>();
            foreach (string businessPremise in bussinesPrimisess)
            {
                var separators = businessPremise.Split(',').Reverse().ToList();

                // Some countries have a comma in the name, must check both
                var countryName1 = separators.Count >= 1 ? separators[0] : null;
                var countryName2 = separators.Count >= 2 ? String.Join(',', separators.Take(2).Reverse().ToList()) : null;

                string countryCode = GetShortCountryCodeByCountryName(countryName1) ?? GetShortCountryCodeByCountryName(countryName2);

                var countryCode_Type = GetCountryType(countryCode);
                var address_type = new Address_Type()
                {
                    legalAddressType = OECDLegalAddressType_EnumType.OECD303,
                    legalAddressTypeSpecified = true,
                    Items = new object[] { businessPremise ?? string.Empty }

                };

                if (countryCode_Type.HasValue)
                {
                    address_type.CountryCode = countryCode_Type.Value;
                }
                results.Add(address_type);
            }

            return results;
        }

        /// <summary>
        /// Return string is not empty otherwise return default Value
        /// </summary>
        /// <param name="value"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        private string GetStringValueIfEmptyDefault(string value, string defaultValue = "")
        {
            return string.IsNullOrEmpty(value) ? defaultValue : value;
        }

        /// <summary>
        /// Conver <seealso cref="Address_Type"/> from <seealso cref="AddressIE"/>
        /// </summary>
        /// <param name="address"></param>
        /// <param name="legalAddressType"></param>
        /// <returns></returns>
        private Address_Type FormatAddress(AddressIE address, OECDLegalAddressType_EnumType? legalAddressType = null)
        {
            try
            {
                var freeAdressItemString = string.Join(", ", (new string[] { address.AddressLine1, address.AddressLine2 }).Where(l => !string.IsNullOrEmpty(l)));
                var address_type =
                    new Address_Type()
                    {
                        Items = new object[] { freeAdressItemString },
                        legalAddressType = legalAddressType ?? OECDLegalAddressType_EnumType.OECD305, // default unspecified
                        legalAddressTypeSpecified = true
                    };

                // populate country if specified
                var countryCode_type = GetCountryType(address?.Country.CountryCode);
                if (countryCode_type.HasValue)
                {
                    address_type.CountryCode = countryCode_type.Value;
                }

                return address_type;
            }
            catch
            {
                throw new NullReferenceException($"Address is missing");
            }

        }

        /// <summary>
        /// Initial OECD reports by country
        /// </summary>
        /// <param name="exchanges"></param>
        /// <param name="targetFy"></param>
        /// <param name="transmittingCountry"></param>
        /// <param name="countries"></param>
        /// <returns></returns>
        private Tuple<Dictionary<string, NTJ_OECD>, Dictionary<string, int>> InitialAllOECD(List<InformationExchangeDto> exchanges, int targetFy, Country transmittingCountry, Dictionary<string, Country> countries)
        {

            var seqNumberMap = new Dictionary<string, int>();
            var result = exchanges.Where(e => e.InformationExchangeDetail != null)
                .SelectMany(e => e.InformationExchangeDetail.RecipientDetails)
                .Where(d => d.JurisdictionResidence != null)
                .GroupBy(d => d.JurisdictionResidence?.CountryCode)
                .ToDictionary(g => g.Key, g => GetOECD(g.Key, transmittingCountry.Id, countries[g.Key].Id, targetFy, seqNumberMap)
                );
            return new Tuple<Dictionary<string, NTJ_OECD>, Dictionary<string, int>>(result, seqNumberMap);
        }

        private NTJ_OECD GetOECD(string countryKey, Guid transmittingCountryId, Guid countriesId, int targetFy, Dictionary<string, int> seqNumberMap)
        {
            int seqNumber;  
            if (seqNumberMap.Count()>0)
            {
                seqNumber = seqNumberMap.OrderByDescending(x => x.Value).FirstOrDefault().Value + 1;
            }
            else
            {
                seqNumber = GetSequenceNumber();
            }
            seqNumberMap.Add(countryKey, seqNumber);
            return new NTJ_OECD()
            {
                MessageSpec = new MessageSpec_Type()
                {
                    ReceivingCountry = GetCountryType(countryKey).Value,
                    TransmittingCountry = CountryCode_Type.VG,
                    Timestamp = DateTime.UtcNow,
                    MessageType = MessageType_EnumType.NTJ,
                    MessageRefId = $"{BVI_ISO_CODE}{GetCountryType(countryKey).Value}{seqNumber}",
                    MessageTypeIndic = MessageTypeIndic_EnumType.NTJ401,
                    ReportingPeriod = (new DateTime(targetFy, 12, 31))
                },
                NtjBody = new CorrectableNtjBody_Type[0],
                version = "1.0"
            };
        }

        /// <summary>
        /// Convert <seealso cref="CountryCode_Type"/> 
        /// from 2 or 3 digits ISO 3166 Country code <seealso cref="https://www.iso.org/iso-3166-country-codes.html"/> or
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        private CountryCode_Type? GetCountryType(string countryCode)
        {
            // try long code
            try
            {
                var shortCountryCode = GetShortCountryCodeByLongCode(countryCode) ?? countryCode;

                return EnumHelper.ToEnumValue<CountryCode_Type>(shortCountryCode, false);
            }
            catch (Exception ex)
            {
                Logger.LogDebug($"Country code {countryCode} not found");
            }

            return null; // default country code
        }

        /// <summary>
        /// Convert from <seealso cref="RelevantActivity"/> to <seealso cref="ActivitiesKeyDataOther_EnumType"/>
        /// </summary>
        /// <param name="activity"></param>
        /// <returns></returns>
        private ActivitiesKeyDataOther_EnumType GetTypeIncomeEnum(RelevantActivity ReleventActivityValue)
        {
            switch (ReleventActivityValue)
            {
                case RelevantActivity.HeadBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ501;
                case RelevantActivity.FinaBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ502;
                case RelevantActivity.BankBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ503;
                case RelevantActivity.InsuBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ504;
                case RelevantActivity.DisrBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ505;
                case RelevantActivity.ShipBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ506;
                case RelevantActivity.HoldBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ507;
                case RelevantActivity.FundBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ508;
                case RelevantActivity.IntelBA:
                    return ActivitiesKeyDataOther_EnumType.NTJ509;
                default:
                    return ActivitiesKeyDataOther_EnumType.NTJ510;
            }
        }

        private string GetUBOType(UBOType uboType)
        {
            switch (uboType)
            {
                case UBOType.LegalPerson:
                    return "NTJ901";
                case UBOType.LegalPersonOther:
                    return "NTJ902";
                case UBOType.LegalPersonSenior:
                    return "NTJ903";
                case UBOType.LegalSettlor:
                    return "NTJ904";
                case UBOType.LegalTrustee:
                    return "NTJ905";
                case UBOType.LegalProtector:
                    return "NTJ906";
                case UBOType.LegalBeneficiary:
                    return "NTJ907";
                case UBOType.LegalOther:
                    return "NTJ908";
                case UBOType.LegalSettlorEquivalent:
                    return "NTJ909";
                case UBOType.LegalTrusteeEquivalent:
                    return "NTJ910";
                case UBOType.LegalProtectorEquivalent:
                    return "NTJ911";
                case UBOType.LegalBeneficiaryEquivalent:
                    return "NTJ912";
                case UBOType.LegalOtherEquivalent:
                    return "NTJ912";
                default:
                    return "";
            }
        }

        /// <summary>
        /// Convert <seealso cref="EntityPersonType"/> to <seealso cref="Nexus_EnumType"/>
        /// </summary>
        /// <param name="nexusType"></param>
        /// <param name="isSameJurisdiction"></param>
        /// <returns></returns>
        private Nexus_EnumType GetNexusCode(EntityPersonType nexusType, bool isSameJurisdiction = true)
        {
            switch (nexusType)
            {
                case EntityPersonType.UltimateParent:
                    return isSameJurisdiction ? Nexus_EnumType.NTJ701 : Nexus_EnumType.NTJ705;
                case EntityPersonType.ImmediateParent:
                    return isSameJurisdiction ? Nexus_EnumType.NTJ702 : Nexus_EnumType.NTJ706;
                case EntityPersonType.UltimateBO:
                    return isSameJurisdiction ? Nexus_EnumType.NTJ703 : Nexus_EnumType.NTJ707;
                default:
                    return Nexus_EnumType.NTJ704;
            }
        }

        private string GetISOLanguageCode(string countryCode)
        {
            // For now, the only supported language is English
            return "EN";
        }

        private string GetShortCountryCodeByLongCode(string longCountryCode)
        {
            if (longCountryCode == null) return null;

            var country = GetRepository<Country>().Query().Where(x => x.CountryCode == longCountryCode).FirstOrDefault();
            return country?.CountryCode2;
        }

        private string GetShortCountryCodeByCountryName(string countryName)
        {
            if (countryName == null) return null;

            var country = GetRepository<Country>().Query().Where(x => x.CountryName == countryName.Trim()).FirstOrDefault();
            return country?.CountryCode2;
        }

        public int GetSequenceNumber()
        {
                var exchanges = GetRepository<EssInformationExchangeXML>().Query()
                    .OrderByDescending(x => x.SequenceNumber)
                    .ToList();

                if (exchanges.Count == 0)
                    return 1;

                return exchanges[0].SequenceNumber + 1;
        }


        /// <summary>
        /// Get Entity Address From Boss
        /// </summary>
        /// <param name="GetEntityInput"></param>
        /// <returns></returns>
        public async Task<AddressDto> GetEntityAddressFromBoss(GetEntityInput input)
        {
            using (Logger.BeginScope(new Dictionary<string, object>
                {
                    { "CspNumber", GetSessionValue<string>(EssSessionKeys.CtspNumber) },
                    { "CorporateEntityId", input.Id }
                }))
            {
                Logger.LogInformation("In GetEntityAddressFromBoss");

                try
                {
                    AddressDto address = new AddressDto();

                    var ctspNumber = GetSessionValue<string>(EssSessionKeys.CtspNumber);

                    Logger.LogInformation($"In GetEntityAddressFromBoss--ctspNumber {ctspNumber}");

                    var repoCtsp = GetRepository<Bdo.Ess.Authorization.Users.Ctsp>();

                    var ctspinfo = await repoCtsp.Query(x => x.Number == ctspNumber).FirstOrDefaultAsync();

                    var entityIntegrationGetAddressUrl = _configuration["Bosss:EntityIntegrationGetAddress"];

                    Logger.LogInformation($"In GetEntityAddressFromBoss--entityIntegrationGetAddressUrl {entityIntegrationGetAddressUrl}");

                    Logger.LogInformation($"ctspinfo--ctspinfo.BOSSsDataManagerBaseUrl {ctspinfo.BOSSsDataManagerBaseUrl}");
                    if (string.IsNullOrWhiteSpace(ctspinfo.BOSSsDataManagerBaseUrl))
                    {
                        Logger.LogInformation($"Exception does not have a boss url");
                        throw new Exception($"ctsp {ctspinfo.Number} does not have a boss url");
                    }
                    else if (string.IsNullOrWhiteSpace(entityIntegrationGetAddressUrl))
                    {
                        Logger.LogInformation($"Exception does not have a entityIntegrationGetAddressUrl");
                        throw new Exception($"EntityIntegrationGetAddress not configured");
                    }

                    var result = Common.Routing.RoutingHelper.UrlCombine(new[] { ctspinfo.BOSSsDataManagerBaseUrl, entityIntegrationGetAddressUrl });

                    Logger.LogInformation($"Boss URL and the coperationEnityID {result} - {input.Id} ");

                    var resultAdd = Common.Helpers.RestHelper.Post<Bdo.Ess.Dtos.Integration.AddressDto>(Common.Routing.RoutingHelper.UrlCombine(new[]
                    { ctspinfo.BOSSsDataManagerBaseUrl, entityIntegrationGetAddressUrl }), input.Id).Result;

                    if (resultAdd != null)
                    {
                        address.AddressLine1 = resultAdd?.AddressLine1;
                        address.AddressLine2 = resultAdd?.AddressLine2;
                    }

                    return address;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Can't get the Boss entity address");
                    throw;
                }
            }
        }
    }

    public class Utf8StringWriter : StringWriter
    {
        public override Encoding Encoding { get { return Encoding.UTF8; } }
    }
}
