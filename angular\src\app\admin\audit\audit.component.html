<div class="row-flex-space" [@routerTransition]>
    <div class="ess-audit-search col-flex">
        <div class="row-flex-space">
            <p class="ess-title">{{l("Audit Trail")}}</p>

            <div class="row-flex">
                <button id="auditSearchFormClear" pButton type="button" (click)="auditSearchForm.reset()" label="Clear"></button>
                <button id="auditFormSearch" pButton type="button" (click)="search()" label="Search"></button>
            </div>
        </div>

        <hr />

        <ngFormValidation #auditSearchFormValidation displayMode="2"></ngFormValidation>
        <form #auditSearchForm="ngForm" class="col-flex">
            <div class="row-flex-space">
                <div class="col-flex">
                    <div class="ess-audit-form-control col-flex">
                        <label for="AuditStartDate">{{"AuditStartDate" | localize}}</label>
                        <div [ngClass]="{ 'ess-audit-form-error': hasError('AuditStartDate') }">
                            <p-calendar #auditStartDateInput="ngModel" id="AuditStartDate" name="AuditStartDate" [(ngModel)]="dateFrom"
                                        class="p-calender-properties" placeholder="DD/MM/YYYY" dateFormat="{{dateFormatString}}"
                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()" [showIcon]="true"
                                        (onClose)="validateDateRange()">
                            </p-calendar>
                        </div>
                    </div>

                    <div class="ess-audit-form-control col-flex">
                        <label for="AuditEndDate">{{"AuditEndDate" | localize}}</label>
                        <div [ngClass]="{ 'ess-audit-form-error': hasError('AuditEndDate') }">
                            <p-calendar #auditEndDateInput="ngModel" id="AuditEndDate" name="AuditEndDate" [(ngModel)]="dateTo"
                                        class="p-calender-properties" placeholder="DD/MM/YYYY" dateFormat="{{dateFormatString}}"
                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()" [showIcon]="true"
                                        (onClose)="validateDateRange()">
                            </p-calendar>
                        </div>
                    </div>
                </div>

                <div class="col-flex">
                    <div class="ess-audit-form-control col-flex">
                        <label for="entityName">{{"Entity Name" | localize}}</label>
                        <input #entityNameInput="ngModel" id="entityName" name="entityName"
                               class="form-control" type="text" [(ngModel)]="entityName" />
                    </div>

                    <div class="ess-audit-form-control col-flex">
                        <label for="fiscalYear">{{"ES Period End" | localize}}</label>
                        <p-dropdown #fiscalYearInput="ngModel" id="fiscalYear" name="fiscalYear"
                                    [(ngModel)]="fiscalYear" [options]="fiscalYears">
                        </p-dropdown>
                    </div>
                </div>
            </div>

            <hr />

            <div class="row-flex-space">
                <div *ngIf="hasFullAccess()" class="ess-audit-form-control col-flex">
                    <label for="Users">{{"Users" | localize}}</label>
                    <p-listbox #usersInput="ngModel" id="Users" name="Users" [(ngModel)]="selectedUsers"
                               [options]="users" optionLabel="displayName" multiple="multiple" filter="filter" checkbox="checkbox"
                               [listStyle]="{'height':'calc(100vh - 395px)'}">
                    </p-listbox>
                </div>

                <div class="ess-audit-form-control col-flex">
                    <label for="Actions">{{"Actions" | localize}}</label>
                    <p-listbox #actionsInput="ngModel" id="Actions" name="Actions" [(ngModel)]="selectedActions"
                               [options]="actions" optionLabel="displayName" multiple="multiple" filter="filter" checkbox="checkbox"
                               [listStyle]="{'height':'calc(100vh - 395px)'}">
                    </p-listbox>
                </div>
            </div>
        </form>
    </div>

    <div class="ess-audit-grid col-flex">
        <p class="ess-title">{{l("Audit Trail Summary")}}</p>

        <hr />

        <div class="ess-audit-grid-body">
            <p-table #dataTable [value]="primengTableHelper.records"
                     rows="50" [paginator]="false" [lazy]="true" [lazyLoadOnInit]="false" (onLazyLoad)="getRecords($event)"
                     [scrollable]="true" scrollHeight="calc(100vh - 215px)" selectionMode="single"
                     (onRowSelect)="onRowSelect($event)">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width:30%">
                            {{'Audit ID' | localize}}
                        </th>
                        <th style="width:15%" pSortableColumn="UserName">
                            {{'User' | localize}}
                            <p-sortIcon id="auditFormSearchSortUserName" field="UserName"></p-sortIcon>
                        </th>
                        <th style="width:20%" pSortableColumn="DateTimeUtc">
                            {{'Date/Time' | localize}}
                            <p-sortIcon id="auditFormSearchSortDate" field="DateTimeUtc"></p-sortIcon>
                        </th>
                        <th style="width:20%" pSortableColumn="EntityName">
                            {{'Entity Name' | localize}}
                            <p-sortIcon id="auditFormSearchSortEntityName" field="EntityName"></p-sortIcon>
                        </th>
                        <th style="width:20%" pSortableColumn="FiscalYear">
                            {{'ES Period End' | localize}}
                            <p-sortIcon id="auditFormSearchSortFiscalYear" field="FiscalYear"></p-sortIcon>
                        </th>
                        <th style="width:30%">
                            {{'Description' | localize}}
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-record="$implicit">
                    <tr [pSelectableRow]="record">
                        <td style="width:30%">
                            {{record.id}}
                        </td>
                        <td style="width:15%">
                            {{record.userName}}
                        </td>
                        <td style="width:20%">
                            {{record.dateTime | date: 'dd/MM/yyyy HH:mm'}}
                        </td>
                        <td style="width:20%">
                            {{record.entityName}}
                        </td>
                        <td style="width:20%">
                            {{record.fiscalYear}}
                        </td>
                        <td style="width:30%">
                            {{record.identifierName}}: {{record.identifierValue}}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                {{'NoData' | localize}}
            </div>
            <div class="primeng-paging-container">
                <app-custom-paginator #customPagination id="auditFormPaginator"
                        [totalRecordsCount]="primengTableHelper.totalRecordsCount"
                        [defaultRecordsCountPerPage]="50"
                        [predefinedRecordsCountPerPage]="primengTableHelper.predefinedRecordsCountPerPage"
                        (pageChange)="getRecords($event)"
                        (pageSizeChange)="getRecords($event)">
                </app-custom-paginator>
                </div>
        </div>
    </div>

    <auditRecordModal #auditRecordModal></auditRecordModal>
</div>
