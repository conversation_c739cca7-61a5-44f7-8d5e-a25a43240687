<div class="row-flex-justified-largefont">    
    6{{headingSeq}}.{{sequenceNo}}. {{entityType}} {{l("ESParent")}} 
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="model.show(null,true,importOnlyMode)">Add row</a>
    </div>
</div>
<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="additionalDetails">
    <ng-template pTemplate="header">
        <tr>
            <th class="table-text-header">Name</th>
            <th class="table-text-header">Alternative Name</th>
            <th class="table-text-header">Incorporation Number</th>
            <th class="table-text-header">Identification Number</th>
            <th class="table-text-header">Jurisdiction</th>            
            <th *ngIf="!readOnlyMode" class="table-text-header">Action</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
            <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping"> {{source.name}} </label>
            </td>
            <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.alternativeName}} </label>
            </td>
            <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.incorporationNumber}} </label>
           </td>
           <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.identificationNumber}} </label>
           </td>
           <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.jurisdiction?.countryName}} </label>
           </td>                 
            <td *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="model.show(source,false,importOnlyMode)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-entity-detail-dialog [listOfCountry]="listOfCountry" [entityTypeName]="entityTypeName" #model (submitted)="updateSource($event)"></app-entity-detail-dialog>
