using System;
using System.Collections.Generic;
using System.Text;

namespace Bdo.Ess.Dtos.Audit
{
    public enum FormatType
    {
        String,

        YesNo,
        YesNoText,
        IfTrueText,
        IfFalseText,

        Int,
        Long,
        Decimal,
        Double,

        DateTime,
        Date,
        MonthDate,
        Year,
        Class,
        Currency,
    }

    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class AuditableProperty : System.Attribute
    {
        public string AuditName { get; set; }

        public bool ForInfo { get; set; }

        public string FormatString { get; set; }

        public FormatType FormatType { get; set; }
        public bool IsKey { get; set; }
        public bool IgnoreAudit { get; set; }

        public AuditableProperty(string name, bool forInfo = false, FormatType formatType = FormatType.String, string formatString = null)
        {
            AuditName = name;
            ForInfo = forInfo;
            FormatString = formatString;
            FormatType = formatType;
        }
    }
}
