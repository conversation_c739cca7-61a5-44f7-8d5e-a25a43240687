import {
    Component,
    OnInit,
    Injector,
    ViewEncapsulation,
    ViewChild,
} from "@angular/core";
import { AppComponentBase } from "@shared/common/app-component-base";
import {
    InformationExchangeDto,
    RecipientDetailsDto,
    InformationExchangeDetailDto,
    InformationExchangeServiceProxy,
    ExchangeReason,
} from "@shared/service-proxies/service-proxies";
import { ActivatedRoute, Router } from "@angular/router";
import { Location } from "@angular/common";
import * as _ from "lodash";
import { UtilityComponent } from "@app/shared/common/general/utility";
import { AppConsts } from "../../../shared/AppConsts";
import { forkJoin, Observable } from "rxjs";
import {
    AllRequestStatusesInclAny,
    AllInformationExchangeStatus,
    Status,
    AllUBOStatus,
    AllUBOStatusInclAny,
    AllEntityPeronStatus,
} from "@app/shared/common/informationexchangestatus/informationexchange-status";
import { FormControl, FormGroup } from "@angular/forms";
import { NgFormValidationComponent } from "@shared/utils/validation/ng-form-validation.component";
import * as moment from 'moment';
import { ValueSelectionService } from "@shared/services/ValueSelectionService";

@Component({
    selector: "ca-information-exchange-add",
    templateUrl: "./ca-information-exchange-add.component.html",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["./ca-information-exchange-add.component.css"],
})
export class CaInformationExchangeAddComponent
    extends AppComponentBase
    implements OnInit
{
    @ViewChild("addExchangeForm", { static: true }) addExchangeForm: FormGroup;

    errors: any[] = [];
    validationFields: any[] = [];

    StatusSelect = { value: -1, description: "Select" };

    informationEchange: InformationExchangeDto = new InformationExchangeDto();

    heading: number = 2;
    sub_heading: string = "a";
    headings: number[] = [];
    sub_headings: string[] = [];

    informationStatuses;
    selectedInformationStatus;
    countries: any;
    resultFilters: any;
    disableDropDown: any;
    isReady: boolean = false;
    id: any;
    action: any;
    readonly: boolean;    
    constructor(
        injector: Injector,
        private route: ActivatedRoute,
        private _informationExchangeServiceProxy: InformationExchangeServiceProxy,
        private _utlity: UtilityComponent,
        private location: Location,
        private valueSelectionService: ValueSelectionService,
        private router: Router
    ) {
        super(injector);
        this.informationEchange.informationExchangeDetail =
            new InformationExchangeDetailDto();
        this.informationEchange.informationExchangeDetail.recipientDetails = [];
        
        this.errors = [];
        this.validationFields = [];
        this.route.params.subscribe((x) => {
            this.id = x.id;
            this.action = x.action;
            if (this.action === "1" || this.action === "3") {
                forkJoin([
                    // difference
                    this._informationExchangeServiceProxy.getInformationExchange(
                        this.id
                    ),
                    this._informationExchangeServiceProxy.getAllCountries(null),
                ]).subscribe((responseList) => {
                    this.informationEchange = Object.assign(
                        new InformationExchangeDto(),
                        responseList[0]
                    );
                    // need to get the proper list
                    this._informationExchangeServiceProxy
                        .getExchangeStatus(
                            this.informationEchange.informationExchangeStatus,
                            this.informationEchange.exchangeReason
                        )
                        .subscribe((result) => {
                            this.resultFilters = result;
                            this.informationStatuses = result.map((v) => {
                                return AllInformationExchangeStatus.find(
                                    (x) => x.value == v.nextStatus
                                );
                            });

                            if (this.action === "1") {
                                this.informationStatuses.unshift(
                                    this.StatusSelect
                                );
                                this.selectedInformationStatus =
                                    this.informationStatuses[0];
                                this.readonly = this.resultFilters.filter(
                                    (x) =>
                                        x.currentStatus ==
                                        this.informationEchange
                                            .informationExchangeStatus
                                )[0].readOnlyMode;
                                this.disableDropDown =
                                    this.resultFilters.filter(
                                        (x) =>
                                            x.currentStatus ==
                                            this.informationEchange
                                                .informationExchangeStatus
                                    )[0].isFinalState;
                            }
                            if (this.action === "3") {
                                this.readonly = true;
                                this.disableDropDown = true;
                            }
                        });

                    this.countries = this._utlity.sort_country(responseList[1]);
                    this.isReady = true;
                });
            } else {
                forkJoin([
                    // difference
                    this._informationExchangeServiceProxy.getInformationExchangeHistoryDetail(
                        this.id
                    ),
                    this._informationExchangeServiceProxy.getAllCountries(null),
                ]).subscribe((responseList) => {
                    this.informationEchange = Object.assign(
                        new InformationExchangeDto(),
                        responseList[0]
                    );
                    this.readonly = true;
                    this.disableDropDown = true;
                    this.countries = this._utlity.sort_country(responseList[1]);
                    this.isReady = true;
                });
            }
        });
    }

    changeReadOnly() {
        if (this.action === "1")
            this.readonly = this.resultFilters.filter(
                (x) => x.nextStatus === this.selectedInformationStatus.value
            )[0].readOnlyMode;
        else this.readonly = true;
    }

    ngOnInit() {
        for (let i = 0; i < 10; i++) {
            this.headings[i] = this.heading + i;
        }

        this.sub_headings[0] = this.sub_heading;
        for (let i = 1; i < 10; i++) {
            this.sub_headings[i] = this.getNextLetter(this.sub_headings[i - 1]);
        }
    }

    hasError(fieldName: string): boolean {
        return (
            this.validationFields &&
            this.validationFields.find((x) => x === fieldName)
        );
    }

    hasErrorField(fieldName: string, index: string): boolean {
        let newFieldName = fieldName + index;
        return (
            this.validationFields &&
            this.validationFields.find((x) => x === newFieldName)
        );
    }

    public getNextLetter(char: string): string {
        let code = char.charCodeAt(0);
        code++;
        return String.fromCharCode(code);
    }

    removeDetail(data: any) {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            "Are you sure you want to delete it?",
            function (isConfirmed) {
                if (isConfirmed) {
                    let index =
                        self.informationEchange.informationExchangeDetail.recipientDetails.findIndex(
                            (item) => item.id === data.id
                        );
                    if (index > -1)
                        self.informationEchange.informationExchangeDetail.recipientDetails.splice(
                            index,
                            1
                        );
                }
            }
        );
    }

    //update table
    updateSource(sp: RecipientDetailsDto) {
        let entitydetail = _.cloneDeep(sp);

        if (
            !this.informationEchange.informationExchangeDetail.recipientDetails
        ) {
            this.informationEchange.informationExchangeDetail.recipientDetails =
                [];
        }
        let index =
            this.informationEchange.informationExchangeDetail.recipientDetails.findIndex(
                (x) => x.id == sp.id
            );
        if (index == -1)
            this.informationEchange.informationExchangeDetail.recipientDetails.push(
                entitydetail
            );
        else
            this.informationEchange.informationExchangeDetail.recipientDetails[
                index
            ] = entitydetail;
    }

    formatAddress(address: any): string {
        return this._utlity.formatAddress3(address);
    }

    onExit() {
        this.valueSelectionService.isPreserveSelectedValues=true;
        this.router.navigate(['app/main/cainfomainexchange']);
        //this.location.back();
    }

    entityName(item?) {
        let findItem = AllEntityPeronStatus.find((x) => x.value === item);
        if (findItem) return findItem.description;
    }

    boName(item?) {
        if (item !== -1) {
            let findItem = AllUBOStatus.find((x) => x.value === item);
            if (findItem) return findItem.description;
        }
    }

    validate(item) {
        this.errors = [];
        this.validationFields = [];
        if (item === -1) return true;
        if (item === 2 || item === 3) {
            if (
                this.informationEchange.informationExchangeDetail
                    .recipientDetails &&
                this.informationEchange.informationExchangeDetail
                    .recipientDetails.length === 0
            ) {
                this.errors.push("At least one Recepient Detail is Required ");
                this.validationFields.push("recipient");
            }
            else{
                if(this.showDeclarationChanges_2_0()){
                    this.validateAddressTIN();
                }
            }

            if (
                this.informationEchange.exchangeReason ==
                    ExchangeReason.NonCompliance &&
                !this.informationEchange.informationExchangeDetail.summary
            ) {
                this.errors.push("Summary is Required");
                this.validationFields.push("Summary");
            }

            if (
                this.informationEchange.informationExchangeDetail
                    .taxIdentificationNo &&
                (this.informationEchange.informationExchangeDetail
                    .taxIdentificationCountry.countryCode == null ||
                    this.informationEchange.informationExchangeDetail
                        .taxIdentificationCountry.countryCode == "N/A")
            ) {
                this.errors.push("Issued By is Required");
                this.validationFields.push("IssuedBy");
            }

            if (
                this.informationEchange.exchangeReason !=
                    ExchangeReason.NonResidence &&
                !this.informationEchange.informationExchangeDetail
                    .totalAnnualIncome
            ) {
                this.errors.push("Total Gross Income is Required");
                this.validationFields.push("GrossTotal");
            }

            if (
                this.informationEchange.informationExchangeDetail
                    .relevantActivityInformationDetail
            ) {
                for (
                    let i = 0;
                    i <
                    this.informationEchange.informationExchangeDetail
                        .relevantActivityInformationDetail.length;
                    i++
                ) {
                    if (
                        !this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalTurnover &&
                        this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalTurnover !== 0
                    ) {
                        this.errors.push("Gross Income is Required");
                        let tempFiledName = "turn-" + i;
                        this.validationFields.push(tempFiledName);
                    }

                    if (
                        !this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalExpeditureInBVI &&
                        this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalExpeditureInBVI !== 0
                    ) {
                        this.errors.push(
                            "Expenses directly incurred is Required"
                        );
                        let tempFiledName = "expd-" + i;
                        this.validationFields.push(tempFiledName);
                    }

                    if (
                        !this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalExpenditureIncurredInBVI &&
                        this.informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail[i]
                            .totalExpenditureIncurredInBVI !== 0
                    ) {
                        this.errors.push("Outsourcing expenditure is Required");
                        let tempFiledName = "expbvi-" + i;
                        this.validationFields.push(tempFiledName);
                    }
                }
            }
            return this.errors.length == 0;
        } else return true;
    }

    onSave() {
        this.errors = [];
        this.validationFields = [];
        if (!this.validate(this.selectedInformationStatus.value)) return;
        else {
            this.informationEchange.informationExchangeStatus =
                this.selectedInformationStatus.value !== -1
                    ? this.selectedInformationStatus.value
                    : this.informationEchange.informationExchangeStatus;

            if (this.informationEchange.fiscalEndDate)
            {
                var fiscalPeriodEndDateWithNoTime = moment(this.informationEchange.fiscalEndDate);
                this.informationEchange.fiscalEndDateString = fiscalPeriodEndDateWithNoTime.format("YYYY-MM-DD");
            }

            this._informationExchangeServiceProxy
                .updateCaInformationExchange(this.informationEchange)
                .subscribe((x) => {
                    this._informationExchangeServiceProxy
                        .getExchangeStatus(
                            this.informationEchange.informationExchangeStatus,
                            this.informationEchange.exchangeReason
                        )
                        .subscribe((result) => {
                            this.resultFilters = result;
                            this.informationStatuses = result.map((v) => {
                                return AllInformationExchangeStatus.find(
                                    (x) => x.value == v.nextStatus
                                );
                            });
                            this.informationStatuses.unshift(this.StatusSelect);
                            this.selectedInformationStatus =
                                this.informationStatuses[0];
                            this.readonly = this.resultFilters.filter(
                                (x) =>
                                    x.currentStatus ==
                                    this.informationEchange
                                        .informationExchangeStatus
                            )[0].readOnlyMode;
                            this.disableDropDown = this.resultFilters.filter(
                                (x) =>
                                    x.currentStatus ==
                                    this.informationEchange
                                        .informationExchangeStatus
                            )[0].isFinalState;
                        });
                });
        }
    }

    showDeclarationChanges_2_0() {
        return moment(this.informationEchange.informationExchangeDetail.fiscalEndDate).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day');
    }

    validateAddressTIN() {
        for (let i = 0; i < this.informationEchange.informationExchangeDetail.recipientDetails.length; i++) {  

            // TIN 
            if(this.informationEchange.informationExchangeDetail.recipientDetails[i].tin) {   
                if (this.informationEchange.informationExchangeDetail.recipientDetails[i].tinIssuedCoutry) {
                    if (this.informationEchange.informationExchangeDetail.recipientDetails[i].tinIssuedCoutry.countryCode == null || this.informationEchange.informationExchangeDetail.recipientDetails[i].tinIssuedCoutry.countryCode == "N/A") {
                        this.errors.push("TIN Issued Country is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                        this.validationFields.push("recipient");
                    }                          
                }  
                else{
                    this.errors.push("TIN Issued Country is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                    this.validationFields.push("recipient");
                }
            }

            // Address
            if(this.informationEchange.informationExchangeDetail.recipientDetails[i].address) {
                if (!this.informationEchange.informationExchangeDetail.recipientDetails[i].address.addressLine1) {
                    this.errors.push("AddressLine1 is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                    this.validationFields.push("recipient");
                }  
                
                if (this.informationEchange.informationExchangeDetail.recipientDetails[i].address.country) {
                    if (this.informationEchange.informationExchangeDetail.recipientDetails[i].address.country.countryCode == null || this.informationEchange.informationExchangeDetail.recipientDetails[i].address.country.countryCode == "N/A") {
                        this.errors.push("Country is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                        this.validationFields.push("recipient");
                    }                          
                }  
                else{
                    this.errors.push("Country is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                    this.validationFields.push("recipient");
                }  
            }
            else{
                this.errors.push("Address is Required for : " + this.informationEchange.informationExchangeDetail.recipientDetails[i].nameEntityPerson);                        
                this.validationFields.push("recipient");
            }                   
        }
    }

}
