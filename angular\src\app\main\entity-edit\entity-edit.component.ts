import { Component, OnInit, Input, Output, EventEmitter, Injector, ViewChild, OnDestroy } from '@angular/core';
import { NgForm } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { LookUpMainServiceProxy, CorporateEntityServiceProxy, CheckEntityInput } from '@shared/service-proxies/service-proxies';
import * as moment from 'moment';
import { SearchCountryField, TooltipLabel, CountryISO } from 'ngx-intl-tel-input';

@Component({
  selector: 'app-entity-edit',
  templateUrl: './entity-edit.component.html',
  styleUrls: ['./entity-edit.component.css']
})
export class EntityEditComponent extends AppComponentBase implements OnInit, OnDestroy {

  @Output() onSubmitted: EventEmitter<any> = new EventEmitter<any>();
  _isNew: boolean;
  @Input() set isNew(isNew) {
    this._isNew = isNew;

  }
  none: boolean = true;
  _entity: any


  @Input() set entity(entity) {

    this._entity = entity

    for (const fieldGroup of this.fields) {
      for (const field of fieldGroup) {
        if (field.type == "address") {
          field.value[0] = entity[field.id + 1]
          field.value[1] = entity[field.id + 2]
          field.country.value = entity[field.country.id]
        } else if (field.type == "options") {
          // if the item is newly created one the non should be nul;
          this.none = !this._isNew;
          for (const item of field.items) {
            item.value = entity[item.id]
            if (item.value) this.none = false;
          }

          let fieldItem = field.items.find(x => x.id === "licenseNone");
          fieldItem.value = this.none;


        } else if (field.type == "date" || field.type == "periodEnd") {
          if (entity[field.id]) {
            const val = entity[field.id]
            if (moment.isMoment(val)) {
              field.value = val.toDate()
            } else {
              field.value = val
            }
          }
        } else {
          field.value = entity[field.id]
        }
      }
    }

    if (this.licenseActOtherTextField.value)
      this.licenseActOtherTextField.disabled = false;

    this.isDeletedField.disabled = this._entity.hasSubmittedDeclarations
  }
  @ViewChild('f', { static: false }) form: NgForm;

  fields: any
  errors: string[] = []
  warnings: string[] = []
  SearchCountryField = SearchCountryField;
  TooltipLabel = TooltipLabel;
  CountryISO = CountryISO;
  selectedCountryISO: string = CountryISO.BritishVirginIslands;
  preferredCountries: CountryISO[] = [CountryISO.BritishVirginIslands];
  phoneNumber: any = { 'phone': null, 'cell': null, 'alternativePhone': null };

  private uniqueIdField
  private companyNumberField
  private fiscalPeriodField
  private taxNumberField
  private licenseActField
  private licenseActOtherTextField
  private entityStatusField
  private addressField
  private nameField
  private statusIdField
  private isDeletedField

  constructor(
    injector: Injector,
    _lookupService: LookUpMainServiceProxy,
    private _entityService: CorporateEntityServiceProxy) {
    super(injector)

    // const now = new Date()
    _lookupService.getEntityStatuses({}).subscribe(x => {
      this.entityStatusField.items = x.map(y => ({ value: y.id, label: y.name }));
      if (!this.entityStatusField.value) {
        this.entityStatusField.value = this.entityStatusField.items[0].value;
      }
    })

    _lookupService.getAllCountries(null, null).subscribe(x => {
      const items = x.map(y => ({ value: y.id, label: y.countryName })).slice().sort((a, b) => {
        if (a.label == b.label) return 0;
        if (a.label < b.label) return -1;
        return 1;
      })
      let baraCountry = items.find(x => x.label === 'Barbados');
      let filtereditemsa = items.filter(x => x.value !== 'Barbados');
      this.addressField.country.items = [{ value: null, label: this.l("Select country") }, baraCountry, ...filtereditemsa];
    })

    this.fields =
      [
        [
          {
            type: "string",
            id: "clientNumber",
            name: "CTSP Unique Client #",
            value: '',
            validation: {
              isRequired: true
            },
            maxLength: 50,
          },
          {
            type: "string",
            id: "name",
            name: "Name",
            value: '',
            validation: {
              isRequired: true
            },
            maxLength: 100,
          },
          {
            type: "string",
            id: "companyNumber",
            name: "Company Number",
            value: '',
            validation: {
              isRequired: true
            },
            maxLength: 25
          },
          {
            type: "string",
            id: "alternativeName",
            name: "Alternative Name",
            value: '',
            maxLength: 100,
          },
          {
            type: "address",
            id: "addressLine",
            name: "Registered Office Address",
            value: ['', null],
            country: {
              id: "countryId",
              items: []
            },
            maxLength: 255,
          },
          {
            type: "date",
            id: "incorporationDate",
            name: "Date of Incorporation",
            // value: now,
          },
          {
            type: "string",
            id: "corporateTaxNumber",
            name: "Corporate Tax Number",
            validation: {
              isRecommended: true
            },
            maxLength: 25,
          },
          {
            type: "notes",
            id: "comments",
            name: "Comments",
            rows: 4,
            maxLength: 255,
          }
        ],
        [
          {
            type: "select",
            id: "statusId",
            name: "Status",
            validation: {
              isRequired: true
            },
            items:
              [],
          },
          {
            type: "date",
            id: "statusChangeDate",
            name: "Status Change Date",
            // value: now,
          },
          {
            type: "options",
            id: "licenseAct",
            name: "Licensed under act",
            items:
              [
                {
                  id: "licenseInsuranceAct",
                  text: "Insurance Act",
                  value: false,
                  disabled: false,
                },
                {
                  id: "licenseForeignCurrencyPermit",
                  text: "Foreign Currency Permit",
                  value: false,
                  disabled: false,
                },
                {
                  id: "licenseFinancialInstitutionAct",
                  text: "Financial Institution Act",
                  value: false,
                  disabled: false,
                },
                {
                  id: "licenseNone",
                  text: "None",
                  value: false,
                  disabled: false,
                },
                {
                  id: "licenseActOther",
                  text: "Other(Please Specify)",
                  value: false,
                  disabled: false,
                  change: checked => {
                    if (checked) { this.licenseActOtherTextField.disabled = false; }
                    else { this.licenseActOtherTextField.disabled = true; }
                  }
                }
              ],
            validation:
            {
              hasErrors: () => {
                if (!this.licenseActField["items"][0].value &&
                  !this.licenseActField["items"][1].value && !this.licenseActField["items"][2].value &&
                  !this.licenseActField["items"][3].value && !this.licenseActField["items"][4].value
                ) {
                  return true
                }

                if (this.licenseActField["items"][3].value &&
                  (this.licenseActField["items"][0].value || this.licenseActField["items"][1].value ||
                    this.licenseActField["items"][2].value || this.licenseActField["items"][4].value)
                ) {
                  return true
                }

              },
              validate: () => {

                if (!this.licenseActField["items"][0].value &&
                  !this.licenseActField["items"][1].value && !this.licenseActField["items"][2].value &&
                  !this.licenseActField["items"][3].value && !this.licenseActField["items"][4].value
                ) {
                  return [this.l('Must select one or more license acts.')]
                }


                if (this.licenseActField["items"][3].value &&
                  (this.licenseActField["items"][0].value || this.licenseActField["items"][1].value ||
                    this.licenseActField["items"][2].value || this.licenseActField["items"][4].value)
                ) {
                  return [this.l('Cannot select multiple options if None is selected.')]
                }

                return []
              }
            }
          },
          //{
          //  type: 'bool',
          //  id: "licenseActOther",
          //  text: "Other (Please Specify)",
          //  value: false,
          //  disabled: false,
          //  change: checked => {
          //    if (checked) {
          //      // this.licenseActField["items"][0].value = false;
          //      // this.licenseActField["items"][1].value = false;
          //      // this.licenseActField["items"][2].value = false;
          //      this.licenseActField["items"][0].disabled = true;
          //      this.licenseActField["items"][1].disabled = true;
          //      this.licenseActField["items"][2].disabled = true;
          //      this.licenseActOtherTextField.disabled = false;
          //    } else {
          //      this.licenseActField["items"][0].disabled = false;
          //      this.licenseActField["items"][1].disabled = false;
          //      this.licenseActField["items"][2].disabled = false;
          //      this.licenseActOtherTextField.disabled = true;
          //    }
          //  },
          //},
          {
            type: "string",
            id: "licenseActOtherName",
            placeholder: "Please specify if Other is selected",
            disabled: true,
            maxLength: 255,
            validation: {
              hasErrors: () => {
                return this.licenseActField["items"][4].value && !this.licenseActOtherTextField.value;
              },
              validate: () => {
                if (this.licenseActField["items"][4].value && !this.licenseActOtherTextField.value) {
                  return [this.l('Name must be specified for other license act')]
                }
                return []
              }
            }
          },
          {
            type: "date",
            id: "initialLicenseDate",
            name: "Initial License Date",
            // value: now,
          },
          {
            type: "periodEnd",
            id: "fiscalPeriodEnd",
            name: "Fiscal Period End",
            get isValid() {
              var regex = /^\d{1,2}\-\d{1,2}$/;
              if (!regex.test(this.textValue)) return false;
              const parts = this._textValue_.split('-')
              const month = parseInt(parts[0])
              const day = parseInt(parts[1])
              const d = new Date(2020, month - 1, day)
              return d.getDate() == day
            },
            get value() {
              return this._value_
            },
            set value(d) {
              this._value_ = d
              this._textValue_ = ("0" + (d.getMonth() + 1)).slice(-2) + "-" + ("0" + d.getDate()).slice(-2)
            },
            get textValue() {
              return this._textValue_
            },
            set textValue(val) {
              this._textValue_ = val
              if (this.isValid) {
                const parts = this._textValue_.split('-')
                const month = parseInt(parts[0])
                const day = parseInt(parts[1])
                const d = new Date(2020, month - 1, day)
                if (d.getDate() == day) {
                  // valid
                  this._value_ = d
                } else {
                  this.value_ = null
                }
              } else {
                this._value_ = null
              }
            }
          },
          {
            type: "tel",
            id: "phone",
            name: "Contact Phone #",
            validation: {
              isRecommended: true
            },
            maxLength: 20,
          },
          {
            type: "tel",
            id: "cell",
            name: "Contact Cell #",
            maxLength: 20,
            validation: {}
          },
          {
            type: "tel",
            id: "alternativePhone",
            name: "Alternative Phone #",
            maxLength: 20,
            validation: {}
          },
          {
            type: "email",
            id: "email",
            name: "Contact Email",
            validation: {
              isRequired: true,
              validate: () => {
                if (!this.form.controls["email"].valid) {
                  return ["Contact Email is invalid"]
                }
                return []
              }
            },
            maxLength: 100,
          },
          {
            type: "bool",
            id: "isDeleted",
            name: "Deleted",
            value: false,
            disabled: false,
          }
        ]
      ]

    this.uniqueIdField = this.findField('clientNumber')
    this.companyNumberField = this.findField('companyNumber')
    this.fiscalPeriodField = this.findField('fiscalPeriodEnd')
    this.taxNumberField = this.findField('corporateTaxNumber')
    this.licenseActField = this.findField('licenseAct')
    this.licenseActOtherTextField = this.findField('licenseActOtherName')
    this.entityStatusField = this.findField('statusId')
    this.addressField = this.findField('addressLine')
    this.nameField = this.findField('name')

    this.statusIdField = this.findField('statusId')
    this.isDeletedField = this.findField('isDeleted')
  }

  ngOnInit() {
  }

  ngOnDestroy() {
  }

  getErrorsWarnings(fields): [string[], string[]] {
    let errors: string[] = []
    let warnings: string[] = []

    const StringFormat = (str: string, ...args: string[]) =>
      str.replace(/{(\d+)}/g, (match, index) => args[index] || '')
    const requiredMessage = this.l("{0} is required")
    const recommendedMessage = this.l("{0} is recommended");

    for (const fieldGroup of fields) {
      for (const field of fieldGroup) {
        if (field.type == "string" || field.type == "tel" || field.type == "email" || field.type == "select") {
          if (field.validation) {
            if (field.validation.isRequired && !field.value) {
              errors.push(StringFormat(requiredMessage, field.name))
            } else if (field.validation.notCorrectFormat) {
              const invalidValueMessage = this.l("{0} value is invalid")
              errors.push(StringFormat(invalidValueMessage, field.name))
            } else if (field.validation.isRecommended && !field.value) {
              warnings.push(StringFormat(recommendedMessage, field.name))
            }
            if (field.validation.validate) {
              errors.push(...field.validation.validate())
            }
          }

        } else if (field.type == "address") {
          if (!field.value[0]) {
            const addrLine1RequiredMessage = this.l("{0} line 1 is required")
            errors.push(StringFormat(addrLine1RequiredMessage, field.name))
          }
          if (!field.country.value) {
            const addrCountryRequiredMessage = this.l("{0} country is required")
            errors.push(StringFormat(addrCountryRequiredMessage, field.name))
          }
        } else if (field.type == "periodEnd") {
          if (!field.textValue) {
            errors.push(StringFormat(requiredMessage, field.name))
          } else if (!field.isValid) {
            const invalidValueMessage = this.l("{0} value is invalid")
            errors.push(StringFormat(invalidValueMessage, field.name))
          }
        } else if (field.type == "options") {
          if (field.validation) {
            if (field.validation.validate) {
              errors.push(...field.validation.validate())
            }
          }
        }
      }
    }

    return [errors, warnings]
  }

  static stringArrayChanged(a1: string[], a2: string[]): boolean {
    return !(a1.length == a2.length && a1.every((value, index) => value == a2[index]))
  }

  onSubmit(f: NgForm) {

    this._entityService.checkEntity(new CheckEntityInput({ id: this._entity.id, name: this.nameField.value, clientNumber: this.uniqueIdField.value })).subscribe(output => {
      const errorsWarnings = this.getErrorsWarnings(this.fields)

      const newErrors = errorsWarnings[0]
      const newWarnings = errorsWarnings[1]

      if (output.duplicateClientNumber) {
        newErrors.push(this.l('CTSP Client # is not unique'))
      }
      if (output.duplicateName) {
        newErrors.push(this.l('CTSP client name is not unique'))
      }

      const errorsChanged = EntityEditComponent.stringArrayChanged(this.errors, newErrors);
      const warningsChanged = EntityEditComponent.stringArrayChanged(this.warnings, newWarnings)

      this.errors = errorsWarnings[0]
      this.warnings = errorsWarnings[1]

      if (this.errors.length > 0) {
        return;
      }

      if (warningsChanged || errorsChanged) {
        return
      }

      var selectedCountryId = this.addressField.country.value
      var countryItem = this.addressField.country.items.find(x => {
        return x.value == selectedCountryId
      })
      f.value.countryName = countryItem ? countryItem.label : null

      var selectedStatusId = this.statusIdField.value
      var statusItem = this.statusIdField.items.find(x => {
        return x.value == selectedStatusId
      })
      f.value.statusText = statusItem ? statusItem.label : null

      // set phone numbers to international numbers
      if (f.value.phone) f.value.phone = f.value.phone.internationalNumber;
      if (f.value.cell) f.value.cell = f.value.cell.internationalNumber;
      if (f.value.alternativePhone) f.value.alternativePhone = f.value.alternativePhone.internationalNumber;

      this.onSubmitted.emit(f.value)
    })

    // if (!this._entityId) {
    //   const input = Object.assign(new AddEntityInput(), f.value)

    //   this._entityService.addEntity(input).subscribe(x => {
    //     this.onReview.emit(x)
    //   })
    // } else {
    //   // rename to updateInput due to chrome debugging
    //   const updateInput = Object.assign(new UpdateEntityInput(), f.value)
    //   updateInput.id = this._entityId
    //   this._entityService.updateEntity(updateInput).subscribe(_ => {
    //     this.onReview.emit(updateInput.id)
    //   })
    // }
  }

  submit() {
    this.validatePhoneNumbers();
    this.form.ngSubmit.emit();
  }

  validatePhoneNumbers() {
    let phoneIds = ['phone', 'cell', 'alternativePhone'];
    phoneIds.forEach(id => {
      if (this.form.value[id]) {
        let notCorrectFormat = true;
        if (this.form.controls[id].valid) notCorrectFormat = false;
        this.findField(id).validation.notCorrectFormat = notCorrectFormat;
      } else {
        this.findField(id).validation.notCorrectFormat = false;
      }
    })
  }

  private findField(id: string): any {
    for (const fieldGroup of this.fields) {
      for (const field of fieldGroup) {
        if (field.id == id) {
          return field
        }
      }
    }
  }
}
