import { SelectItem } from "primeng/api";

export class ReportModel
{
    CSPNumber : number;
    EsName : string;
    FinancialPeriodDate: string;
    AssessmentStatus: number;
    NonResidenceDeclaration: string;
    RelevantActivity: any [];
    SelectedRelevantActivity: any [];
    RelevantActivities: any [];

    ResidenceDeclarations: SelectItem[];
    AssessmentStatuses: SelectItem[];

    constructor() {
        this.ResidenceDeclarations = [
            { label: "Select", value: null },
            { label: "Yes", value: 1 },
            { label: "No", value: 0 }
        ];

        this.AssessmentStatuses = [
            { label: "Any", value: 0 },
            { label: 'Not Started', value: 1 },
            { label: 'Closed', value: 2 },
            { label: 'In Progress', value: 3 },
            { label: 'Information Required', value: 4 },
            { label: 'Information Received', value: 5 },
            { label: 'Provisional Fail', value: 6 },
            { label: 'Pass', value: 7 },
            { label: 'Fail', value: 8 },
            { label: 'Review Evidence', value: 9 },
            { label: 'Awaiting Evidence', value: 10 }
        ];


        this.RelevantActivities = [
            { item_id: 0, item_text: 'None' },
            { item_id: 1, item_text: 'Banking business' },
            { item_id: 2, item_text: 'Distribution and service centre business' },
            { item_id: 3, item_text: 'Finance and leasing business' },
            { item_id: 4, item_text: 'Fund management business' },
            { item_id: 5, item_text: 'Headquarters business' },
            { item_id: 6, item_text: 'Insurance business' },
            { item_id: 7, item_text: 'Shipping business' },
            { item_id: 8, item_text: 'Holding company business' },
            { item_id: 9, item_text: 'Intellectual property business' }
        ];

    }

}