import { Component, OnInit, Injector, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { DeclarationHistoryItem } from './declaration-history-item'
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceServiceProxy, EconomicSubstanceDeclarationDto, AuditEntity, EconomicSubstanceStatus } from '@shared/service-proxies/service-proxies';
import { ActivatedRoute, Router } from '@angular/router'

import { ESActionStatus, SubmittedLocation } from '@app/economicsubstance/EconomicSubstance'
import { AppConsts } from '@shared/AppConsts';
import * as moment from 'moment';
import { EntityChangeHistoryModalComponent } from 'app/shared/common/audit/entity-change-history-modal.component';
import { ESHistoryComponent } from '@app/shared/common/economicSubstanceHistory/eshistory.component'



@Component({
  selector: 'app-declaration-history',
  templateUrl: './declaration-history.component.html',
  styleUrls: ['./declaration-history.component.css']
})

export class DeclarationHistoryComponent extends AppComponentBase implements OnInit {
    @ViewChild('entityChangeHistoryModal', { static: true }) entityChangeHistoryModal: EntityChangeHistoryModalComponent;

    @ViewChild('esHistoryModal', { static: true }) esHistoryModal: ESHistoryComponent;


  entityid: string;
  @Output() onDraftDiscarded: EventEmitter<void> = new EventEmitter<void>()

  @Input() set entity(val) {

    if (!val) return

    this.entityid = val.id;
    this.populatedata(this.entityid);

  }


  economicSubstanceDeclarationDto: EconomicSubstanceDeclarationDto[];
  historyItems: DeclarationHistoryItem[]

  get sortedDeclarations(): EconomicSubstanceDeclarationDto[] {
    if (!this.economicSubstanceDeclarationDto) {
      return []
    }
    return this.economicSubstanceDeclarationDto.sort((a, b) => b.fiscalEndDate.year() - a.fiscalEndDate.year())
  }

  constructor(injector: Injector,
    private route: ActivatedRoute,
    private router: Router,
      private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy) {
    super(injector)


  }

  ngOnInit() {

  }

  populatedata(id: string) {

    this.economicSubstanceDeclarationDto = [];
    this._economicSubstanceServiceProxy.getEconomicSubstanceByEntityID(id,undefined).subscribe(x => {
      x.forEach(obj => {
        obj["submissionDateLocal"] = new Date(obj.submissionDate.format('YYYY-MM-DD HH:mm:ss') + ' UTC')
        this.economicSubstanceDeclarationDto.push(obj);
      })



    });
  }

    getstatus(status: number): string {
        switch (status) {
            case EconomicSubstanceStatus.Draft: return "Draft";
            case EconomicSubstanceStatus.Submitted: return "Submitted";
            case EconomicSubstanceStatus.ReOpen: return "Reopen";
            case EconomicSubstanceStatus.ReSubmitted: return "Resubmitted";
            default: return "";
        }
    }


    isDraft(status: number): boolean
    {
        return (status === EconomicSubstanceStatus.Draft || status === EconomicSubstanceStatus.ReOpen);
    }

    isSubmitted(status: number): boolean {
        return (status === EconomicSubstanceStatus.Submitted || status === EconomicSubstanceStatus.ReSubmitted);
    }


  viewSubmission(item: DeclarationHistoryItem) {

      this.router.navigate(['app/economicsubstance/display/' + item.id + '/' + SubmittedLocation.FromDisplay])
  }


  isESStatusCompleted(item: EconomicSubstanceDeclarationDto): boolean {
      if (item.status == EconomicSubstanceStatus.Submitted || item.status == EconomicSubstanceStatus.ReSubmitted) return true;
    else return false;
  }

  editSubmission(item: EconomicSubstanceDeclarationDto) {

    this.router.navigate(['app/economicsubstance/edit/' + item.id + '/' + ESActionStatus.Edit])

  }

  discardSubmission(item: DeclarationHistoryItem) {
    let self = this;
    // ask for confirmation before discarding
    abp.message.confirm(
      AppConsts.messageList.EsDiscardConfirmation,
      'Are you sure you want to Discard the draft?',
      function (isConfirmed) {
        if (isConfirmed) {
          //abp.ui.setBusy();
          self._economicSubstanceServiceProxy.removeDraftEconomicSubstance(item.id, undefined).
            subscribe((data: any) => {
              //abp.ui.clearBusy();
              if (data) {
                self.economicSubstanceDeclarationDto.splice(self.economicSubstanceDeclarationDto.findIndex(x => x.id == item.id), 1);
                self.onDraftDiscarded.emit()
              }
            });
        }
      }
    );
    // re-populate
  }


  changeHistory(item: EconomicSubstanceDeclarationDto): void {
    this.entityChangeHistoryModal.show(AuditEntity.EconomicSubstance, item.id);
    }


    showESSHistory(item: EconomicSubstanceDeclarationDto): void
    {
        this.esHistoryModal.show(item.id);
    }
}
