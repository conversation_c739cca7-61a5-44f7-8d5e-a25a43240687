import { Component, OnInit, Injector, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, RelevantActivityDetailDto, ValidationResultMainDto, ValidationResultDto } from '@shared/service-proxies/service-proxies';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';

@Component({
    selector: 'app-step2-es',
    templateUrl: './step2-es.component.html',
    styleUrls: ['./step2-es.component.css']
})
export class Step2ESComponent extends AppComponentBase implements OnInit {

    @Input() public economicsubstance: EconomicSubstanceDeclarationDto;
    @Input() readOnlyMode: boolean;
    @Input() importOnlyMode: boolean;
    @Input() selectedActivity: RelevantActivityDetailDto[];
    @Input() displayFromCa: boolean;
    field = new Array(10);
    // TODO: need to use localized values
    cols = [
        { field: 'activity', header: '7a. Relevant activity that was carried on during the financial period' },
        { field: 'iscarriedforfanicialperiod', header: '7b. Carried on for only part of the financial period?' },
        { field: 'startdate', header: '7c. Start Date' },
        { field: 'enddate', header: '7d. End Date' },
    ];
    constructor(injector: Injector, public _economicSubstanceService: EconomicSubstanceService) { super(injector); }

    onUnselection(event) {

        if (event && event.data) {
            event.isChecked = false;
            event.data.isCarriedForPartFinancialPeriod = null;
            event.data.startDate = undefined;
            event.data.endDate = undefined;
        }
        let object = this.economicsubstance.relevantActivities.find(x => x.relevantActivityName === event.data.relevantActivityName);

        if (object) {
            object.isChecked = false;
        }


    }
    checkvalue() {
        return this.selectedActivity.find(x => x.relevantActivityName === 'None')

    }
    onHeaderCheckboxToggle(event) {


        if (!event.checked) {
            // loop over all activities and set to default values
            this.economicsubstance.relevantActivities.forEach((activity: RelevantActivityDetailDto) => {
                event.isChecked = false;
                activity.isCarriedForPartFinancialPeriod = false;
                activity.startDate = undefined;
                activity.endDate = undefined;

                activity.startDateString = undefined;
                activity.endDateString= undefined;

            })
        }
    }

    onSelection(event) {

        if (event && event.data) {
            if (this.selectedActivity) {
                if (event.data.relevantActivityName === 'None') {
                    let list = this.selectedActivity.filter(x => x.relevantActivityName !== event.data.relevantActivityName);
                    list.forEach(x => x.isChecked = false);
                    this.selectedActivity = this.selectedActivity.filter(x => x.relevantActivityName === 'None');
                    this.selectedActivity[0].isChecked = true;
                }
                else {
                    let object = this.selectedActivity.find(x => x.relevantActivityName == event.data.relevantActivityName);
                    object.isChecked = true;
                }
            }

        }
    }

    ngOnInit() {
        
        this.InitializeStep2FiledIdName();

     //   this.selectedActivity = this.economicsubstance.relevantActivities.filter(x => x.isChecked);

        this._economicSubstanceService.validations$.subscribe((data: ValidationResultMainDto[]) => {
            if (data) {
                data.forEach((validation: ValidationResultMainDto) => {
                    if (validation.activityName == "Step2") {
                        let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                        if (backendValidations != null) this.runNextValidations(backendValidations);
                    }
                });
            }
        });

    }



    IfItemBeingSelected(activity: any) {

        return (activity.relevantActivityName === 'None') ? false : this.selectedActivity.find(x => x.relevantActivityName === activity.relevantActivityName);
    }
    runNextValidations(validations: ValidationResultDto[]) {
        validations.forEach((validation: ValidationResultDto) =>
        {
            if (!this._economicSubstanceService.step2Error[validation.fieldName])
            {
                this._economicSubstanceService.step2Error[validation.fieldName] = true;
                    this._economicSubstanceService.errorList.push(validation.validationString);
                
            }
        })
    }



    InitializeStep2FiledIdName() {
        
        this.economicsubstance.relevantActivities.forEach((activity: RelevantActivityDetailDto) => {
            this.field[activity.releventActivityValue] = new Array(3);
            this.field[activity.releventActivityValue][0] = 'isGrossIncomeEarnedidT' + activity.releventActivityValue;
            this.field[activity.releventActivityValue][1] = 'isGrossIncomeEarnedidF' + activity.releventActivityValue;
            this.field[activity.releventActivityValue][2] = 'isGrossIncomeEarnedname' + activity.releventActivityValue;

        });
    }
}


