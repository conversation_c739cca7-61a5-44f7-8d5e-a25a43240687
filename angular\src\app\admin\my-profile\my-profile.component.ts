import { Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CurrentUserProfileEditDto, ProfileServiceProxy, UserServiceProxy, UserValidationDto } from '@shared/service-proxies/service-proxies';
import { CountryISO, SearchCountryField, TooltipLabel } from 'ngx-intl-tel-input';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';

@Component({
    templateUrl: './my-profile.component.html',
    encapsulation: ViewEncapsulation.None,
    styleUrls: ['./my-profile.component.less'],
    animations: [appModuleAnimation()]
})
export class MyProfileComponent extends AppComponentBase {
    @ViewChild('settingsFormValidation', { static: true }) settingsFormValidation: NgFormValidationComponent;
    @ViewChild('settingsForm', { static: true }) settingsForm: FormGroup;

    SearchCountryField = SearchCountryField;
    TooltipLabel = TooltipLabel;
    CountryISO = CountryISO;
    selectedCountryISO: string = CountryISO.BritishVirginIslands;
    preferredCountries: CountryISO[] = [CountryISO.BritishVirginIslands];

    validatingEmail = false;
    savingSettings: boolean = false;
    user: CurrentUserProfileEditDto = new CurrentUserProfileEditDto();
    phoneNumber: any;

    constructor(
        injector: Injector,
        private _profileService: ProfileServiceProxy,
        private _userService: UserServiceProxy
    ) {
        super(injector);
    }

    ngOnInit() {
        this.settingsFormValidation.formGroup = this.settingsForm;

        this._profileService.getCurrentUserProfileForEdit().subscribe((result) => {
            this.user = result;
            this.phoneNumber = this.user.phoneNumber;
        });
    }

    ngOnDestroy() {
    }

    settingsHasError(fieldName: string): boolean {
        return this.settingsFormValidation.fieldHasErrors(fieldName);
    }

    onUpdateEmail(): void {
        if (this.user.emailAddress == null || this.user.emailAddress == '') {
            return;
        }

        var control = this.settingsForm.controls["EmailAddress"];
        if (control) {
            control.markAsTouched();

            var input = new UserValidationDto();
            input.selfUpdate = true;
            input.emailAddress = this.user.emailAddress;

            this.validatingEmail = true;
            this._userService.validateEmail(input)
                .pipe(finalize(() => { this.validatingEmail = false; }))
                .subscribe((result) => {
                    if (result.hasErrors) {
                        control.setErrors({
                            customErrors: {
                                errors: result.errors
                            }
                        });
                    }
                });
        }
    }

    saveSettings(): void {
        if (!this.settingsFormValidation.isFormValid()) {
            return;
        }
        
        this.user.phoneNumber = this.phoneNumber.internationalNumber;

        this.savingSettings = true;
        this._profileService.updateCurrentUserProfile(this.user)
            .pipe(finalize(() => { this.savingSettings = false; }))
            .subscribe(() => {
                this.appSession.user.name = this.user.name;
                this.appSession.user.surname = this.user.surname;
                this.appSession.user.userName = this.user.userName;
                this.appSession.user.emailAddress = this.user.emailAddress;
                this.notify.info(this.l('SavedSuccessfully'));
            });
    }
}
