{"format": 1, "restore": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Application.Shared\\Bdo.Ess.Application.Shared.csproj": {}}, "projects": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj", "projectName": "Bdo.Ess.Shared", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Application.Shared\\Bdo.Ess.Application.Shared.csproj": {"version": "7.2.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Application.Shared\\Bdo.Ess.Application.Shared.csproj", "projectName": "Bdo.Ess.Application.Shared", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Application.Shared\\Bdo.Ess.Application.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Application.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\Bdo.Ess.Core.Shared.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\Bdo.Ess.Core.Shared.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Abp.Web.Common": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj", "projectName": "Bdo.Ess.Common", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Abp": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.AspNetCore.SignalR.Core": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider": {"target": "Package", "version": "[1.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.KeyPerFile": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.8, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}, "Polly": {"target": "Package", "version": "[8.2.0, )"}, "Polly.Contrib.WaitAndRetry": {"target": "Package", "version": "[1.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\Bdo.Ess.Core.Shared.csproj": {"version": "7.2.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\Bdo.Ess.Core.Shared.csproj", "projectName": "Bdo.Ess.Core.Shared", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\Bdo.Ess.Core.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Core.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Abp": {"target": "Package", "version": "[4.9.0, )"}, "Abp.Zero.Common": {"target": "Package", "version": "[4.9.0, )"}, "Abp.ZeroCore": {"target": "Package", "version": "[4.9.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj", "projectName": "Bdo.Ess.Dtos", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj", "projectName": "Bdo.Ess.Services", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.AspNetCore.Mvc.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}}}