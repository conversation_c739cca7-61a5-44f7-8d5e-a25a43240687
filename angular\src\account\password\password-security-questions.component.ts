import { Component, Injector } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AccountServiceProxy, VerifySecurityQuestionDto, VerifySecurityQuestionsInput } from '@shared/service-proxies/service-proxies';
import { SelectItem } from 'primeng/api';
import { Subscription, timer } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
    templateUrl: './password-security-questions.component.html',
    animations: [appModuleAnimation()]
})
export class PasswordSecurityQuestionsComponent extends AppComponentBase {
    saving: boolean = false;
    userName: string = null;
    verifySecurityQuestionsInput: VerifySecurityQuestionsInput = new VerifySecurityQuestionsInput();
    securityQuestion1: VerifySecurityQuestionDto = new VerifySecurityQuestionDto();
    securityQuestion2: VerifySecurityQuestionDto = new VerifySecurityQuestionDto();
    securityQuestion3: VerifySecurityQuestionDto = new VerifySecurityQuestionDto();

    remainingSeconds: number = 0;
    displayMinutes: number = 0;
    displaySeconds: number = 0;
    timerSubscription: Subscription;

    constructor(
        injector: Injector,
        private _accountService: AccountServiceProxy,
        private _router: Router,
        private route: ActivatedRoute,
    ) {
        super(injector);

        this.route.params.subscribe(x => {
            this.userName = x.userName
            this.verifySecurityQuestionsInput.userName = this.userName;

            this._accountService.getSecurityQuestions(this.userName)
                .subscribe(result => {
                    this.securityQuestion1 = result[0];
                    this.securityQuestion2 = result[1];
                    this.securityQuestion3 = result[2];
                });
        })
    }

    ngOnInit() {
        this.verifySecurityQuestionsInput.twoFactorProvider = this.twoFactorProviders[0].value;
    }

    ngOnDestroy() {
        if (this.timerSubscription) {
            this.timerSubscription.unsubscribe();
        }
    }

    verifySecurityQuestions(): void {
        this.saving = true;

        this.verifySecurityQuestionsInput.questions = [];
        this.verifySecurityQuestionsInput.questions.push(this.securityQuestion1);
        this.verifySecurityQuestionsInput.questions.push(this.securityQuestion2);
        this.verifySecurityQuestionsInput.questions.push(this.securityQuestion3);

        this._accountService.verifySecurityQuestions(this.verifySecurityQuestionsInput)
            .pipe(finalize(() => { this.saving = false; }))
            .subscribe((result) => {
                if (result.requiresTwoFactor) {
                    this.verifySecurityQuestionsInput.hasTwoFactorCode = true;
                    this.remainingSeconds = this.twoFactorExpirySeconds;

                    const timerSource = timer(0, 1000);
                    this.timerSubscription = timerSource.subscribe(() => {
                        this.remainingSeconds = this.remainingSeconds - 1;

                        this.displayMinutes = Math.floor(Math.round(this.remainingSeconds / 60 * 100) / 100);
                        this.displaySeconds = this.remainingSeconds - this.displayMinutes * 60;

                        if (this.remainingSeconds === 0) {
                            this.message.warn(this.l('TimeoutPleaseTryAgain')).then(() => {
                                this.verifySecurityQuestionsInput.hasTwoFactorCode = false;
                                this.timerSubscription.unsubscribe();
                            });
                        }
                    });
                } else {
                    location.href = result.redirectUrl;
                }
            });
            
    }
}
