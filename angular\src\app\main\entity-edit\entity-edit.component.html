<p-card *ngIf="errors && errors.length > 0" class="p-card-error">
    <p-header>
        <div><i
                class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Validation Errors")}}</label>
        </div>
    </p-header>
    <div *ngFor="let error of errors">
        {{error}}
    </div>
</p-card>

<p-card *ngIf="warnings && warnings.length > 0" class="p-card-warning">
    <p-header>
        <div><i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Warnings")}}</label></div>
    </p-header>
    <div *ngFor="let warning of warnings">
        {{warning}}
    </div>
</p-card>
<p-card class="p-card-properties_form">
    <p-header>
        <p>{{l("Entity Details")}}</p>
    </p-header>
    <form #f="ngForm" (ngSubmit)="onSubmit(f)">
        <div class="row">
            <div class="col-sm-6" *ngFor="let fieldsGroup of fields">
                <div *ngFor="let field of fieldsGroup" class="row">
                    <label class="col-sm-4 margin-top" [for]="field.id">{{l(field.name)}}
                        {{field.name ? ":" : ""}}</label>
                    <div class="col-sm-8 field-container" [ngSwitch]="field.type">
                        <div *ngSwitchCase="'string'"
                            [ngClass]="{ 'input-error': field.validation && ((field.validation.isRequired && !field.value) || (field.validation.hasErrors && field.validation.hasErrors())), 'input-warning': field.validation && ((field.validation.isRecommended && !field.value) || (field.validation.hasWarnings && field.validation.hasWarnings())) }">
                            <input type="text" [id]="field.id" [name]="field.id"
                                [placeholder]="field.placeholder ? field.placeholder : ''" [(ngModel)]="field.value"
                                [disabled]="field.disabled" pInputText [maxlength]="field.maxLength">
                        </div>
                        <input type="number" [id]="field.id" [name]="field.id" *ngSwitchCase="'number'" pInputText
                            ngModel [maxlength]="field.maxLength">
                        <div *ngSwitchCase="'date'"
                            [ngClass]="{ 'input-error': field.error, 'input-warning': field.warning }">
                            <p-calendar [utc]="true" [id]="field.id" [name]="field.id" [(ngModel)]="field.value"
                            [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                [style]="{'width':'100%', 'position': 'relative'}" dateFormat="{{dateFormatString}}"
                                placeholder="YYYY-MM-DD"
                                [showIcon]="true">
                            </p-calendar>
                        </div>
                        <!-- <select [id]="field.id" [name]="field.id" *ngSwitchCase="'select'" [(ngModel)]="field.value">
                            <option *ngFor="let item of field.items" value="{{item.value}}">{{item.text}}</option>
                        </select> -->
                        <div *ngSwitchCase="'select'"
                            [ngClass]="{ 'input-error': field.validation && field.validation.isRequired && !field.value }">
                            <p-dropdown [name]="field.id" [options]="field.items" [(ngModel)]="field.value"
                                [style]="{'width':'100%'}"></p-dropdown>
                        </div>
                        <div *ngSwitchCase="'options'"
                            [ngClass]="{ 'input-error': field.validation && field.validation.hasErrors && field.validation.hasErrors() }">
                            <div *ngFor="let item of field.items">
                                <p-checkbox [id]="item.id" [name]="item.id" [(ngModel)]="item.value"
                                    [disabled]="item.disabled" class="margin-right"
                                    (onChange)="item.change && item.change($event)" binary="true">
                                </p-checkbox>{{l(item.text)}}
                            </div>
                        </div>
                        <div *ngSwitchCase="'address'" class="field-container">
                            <div [ngClass]="{ 'input-error': !field.value[0] }">
                                <input type="text" [id]="field.id + '1'" [name]="field.id + '1'"
                                    [(ngModel)]="field.value[0]" pInputText [maxlength]="field.maxLength"><br>
                            </div>
                            <input type="text" [id]="field.id + '2'" [name]="field.id + '2'"
                                [(ngModel)]="field.value[1]" pInputText [maxlength]="field.maxLength"><br>
                            <div [ngClass]="{ 'input-error': !field.country.value }">
                                <!-- <select [id]="field.country.id" [name]="field.country.id"
                                    [(ngModel)]="field.country.value">
                                    <option *ngFor="let item of field.country.items" value="{{item.value}}">
                                        {{item.text}}
                                    </option>
                                </select> -->
                                <p-dropdown [name]="field.country.id" [options]="field.country.items"
                                    [(ngModel)]="field.country.value" [style]="{'width':'100%'}"></p-dropdown>
                            </div>
                        </div>
                        <textarea *ngSwitchCase="'notes'" [id]="field.id" [name]="field.id" [rows]="field.rows"
                            pInputTextarea [(ngModel)]="field.value" [maxlength]="field.maxLength"></textarea>
                        <div *ngSwitchCase="'tel'"
                            [ngClass]="{ 'input-error': field.validation && field.validation.notCorrectFormat, 'input-warning': field.validation && field.validation.isRecommended && !field.value }">
                            <!--<input type="tel" [id]="field.id" [name]="field.id" pInputText [(ngModel)]="field.value"
                                [maxlength]="field.maxLength">-->
                            <ngx-intl-tel-input class="intl-tel-properties" [(ngModel)]="field.value"
                                [enableAutoCountrySelect]="true" [enablePlaceholder]="true" [searchCountryFlag]="true"
                                [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                                [selectFirstCountry]="true" [preferredCountries]="preferredCountries"
                                [maxLength]="field.maxLength" [tooltipField]="TooltipLabel.Name"
                                [phoneValidation]="true" [separateDialCode]="true" [name]="field.id">
                            </ngx-intl-tel-input>
                        </div>
                        <div *ngSwitchCase="'email'"
                            [ngClass]="{ 'input-error': field.validation && field.validation.isRequired && !field.value, 'input-warning': field.validation && field.validation.isRecommended && !field.value }">
                            <input type="email" [id]="field.id" [name]="field.id" *ngSwitchCase="'email'" pInputText
                                [(ngModel)]="field.value" [maxlength]="field.maxLength" email>
                        </div>

                        <div *ngSwitchCase="'bool'">
                            <p-checkbox [id]="field.id" [name]="field.id" [(ngModel)]="field.value" binary="true"
                                [disabled]="field.disabled" class="margin-right">
                            </p-checkbox>{{l(field.text)}}
                        </div>
                        <div *ngSwitchCase="'periodEnd'" [ngClass]="{ 'input-error': !field.isValid }">
                            <input [id]="field.id + 'Text'" [name]="field.id + 'Text'" pInputText
                                [(ngModel)]="field.textValue" placeholder="MM-DD" [maxlength]=10>
                            <p-calendar [id]="field.id" [name]="field.id" [utc]="true" [(ngModel)]="field.value"
                            [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                [style]="{'display':'none'}" dateFormat="{{dateFormatString}}" placeholder="YYYY-MM-DD">
                            </p-calendar>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</p-card>