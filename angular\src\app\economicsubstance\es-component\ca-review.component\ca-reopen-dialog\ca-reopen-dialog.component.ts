import { Component, OnInit, Injector, ViewChild, EventEmitter, Output, Input  } from '@angular/core';

import { AppComponentBase } from '@shared/common/app-component-base';
import { ModalDirective } from 'ngx-bootstrap';
import { FormGroup } from '@angular/forms';
import { NgFormValidationComponent } from 'shared/utils/validation/ng-form-validation.component';
import { ESCAAssessmentInputDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
@Component({
    selector: 'reopenmodal',
  templateUrl: './ca-reopen-dialog.component.html',
  styleUrls: ['./ca-reopen-dialog.component.css']
})
export class CaReopenDialogComponent extends AppComponentBase implements OnInit {

    @ViewChild('reopenmodal', { static: true }) modal: ModalDirective;
    @ViewChild('reopenform', { static: true }) form: FormGroup;
    @ViewChild('reopenFormValidation', { static: true }) validation: NgFormValidationComponent;

    @Output() reopenSubmit: EventEmitter<ESCAAssessmentInputDto> = new EventEmitter<ESCAAssessmentInputDto>();

    assessmentDetail: ESCAAssessmentInputDto = new ESCAAssessmentInputDto();
    constructor(injector: Injector) { super(injector);}

    ngOnInit() {
        this.validation.formGroup = this.form;
  }

    show(detail: ESCAAssessmentInputDto) {
        this.form.reset();
        this.assessmentDetail = _.cloneDeep(detail);
        this.modal.show();
    }

    save(): void
    {
        if (!this.validation.isFormValid()) { return;   }
        this.reopenSubmit.emit(this.assessmentDetail);
        this.close();
    }

    close(): void
    {
        this.modal.hide();
    }

}
