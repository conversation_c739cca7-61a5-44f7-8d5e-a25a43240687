import { Component, Injector, ViewChild, ViewEncapsulation, AfterViewInit } from '@angular/core';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { RedFlagReportServiceProxy } from '@shared/service-proxies/service-proxies';
import { RedFlagsEditComponent } from './red-flags-edit.component'
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Paginator } from 'primeng/components/paginator/paginator';
import { Table } from 'primeng/components/table/table';
import { finalize } from 'rxjs/operators';

@Component({
    templateUrl: './red-flags.component.html',
    encapsulation: ViewEncapsulation.None,
    styleUrls: ['./red-flags.component.less'],
    animations: [appModuleAnimation()]
})
export class RedFlagsComponent extends AppComponentBase implements AfterViewInit {
    @ViewChild('redFlagsEdit', { static: true }) redFlagsEdit: RedFlagsEditComponent;
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    constructor(
        injector: Injector,
        private _redFlagReportServiceProxy: RedFlagReportServiceProxy
    ) {
        super(injector);
    }

    ngOnInit() {
    }

    ngAfterViewInit(): void {
        this.primengTableHelper.adjustScroll(this.dataTable);
    }

    ngOnDestroy() {
    }

    getRedFlags(event?: LazyLoadEvent) {
        if (this.primengTableHelper.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            return;
        }

        this.primengTableHelper.showLoadingIndicator();

        this._redFlagReportServiceProxy.getRedFlagSettings(
            this.primengTableHelper.getSorting(this.dataTable),
            this.primengTableHelper.getMaxResultCount(this.paginator, event),
            this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }

    reloadPage(): void {
        this.paginator.changePage(this.paginator.getPage());
    }

    onRowSelect(record: any) {
        this.redFlagsEdit.get(record.data.id);
    }
}
