<div class="col-flex">
    <!--provisional treatment-->
    <p-card [ngClass]="{'p-card-properties_form margin-top':!readOnlyMode,'p-card-properties_form_readonly margin-top':readOnlyMode}">
        <form #provisionalForm="ngForm">
                <div class="row-flex-justified ">
                    <label class="ac-pheader"> {{l('ProvisionalTreatment')}}</label>
                </div>
                <div class="row-flex-justified-largefont">
                    <label>{{l('DueDateInformation')}}</label>
                </div>
                <div class="row-flex-justified">
                    <div *ngIf="readOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.provisionalTreatmentDueDate| date: "dd/MM/yyyy"}} </label>
                    </div>
                </div>

                <div *ngIf="!displayFromCa" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont">
                        <label>{{l('UploadInformationToCA')}}</label>
                    </div>
                    <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.ProvisionalTreatment }" >
                        <div *ngIf="!checkIfProvisionalTreatmentSubmission(economicsubstance) ">
                            <app-upload-files [Documents]="economicsubstance.provisionalTreatmentDocument"
                                              [DocumentTypeName]="ProvisionalTreatmentDocumentDoc"
                                              [IsEconomicDocument]="true"
                                              [readOnlyMode]="!readOnlyMode"
                                              [displayFromCa]="displayFromCa"
                                              [ctspId]="ctspId">
                            </app-upload-files>
                        </div>
                    </div>
                    <div class="row-flex-justified" >
                            <div *ngIf="checkIfProvisionalTreatmentSubmission(economicsubstance) && economicsubstance.provisionalTreatmentSubmissionDate">
                                <app-upload-files [Documents]="economicsubstance.provisionalTreatmentDocument"
                                                  [DocumentTypeName]="ProvisionalTreatmentDocumentDoc"
                                                  [IsEconomicDocument]="true"
                                                  [readOnlyMode]="readOnlyMode"
                                                  [displayFromCa]="displayFromCa"
                                                  [ctspId]="ctspId">
                                </app-upload-files>
                            </div>

                     </div>
                    </div>


                <div *ngIf="displayFromCa && checkIfProvisionalTreatmentSubmission(economicsubstance)" class="row-flex-justified-rows">
                    <app-upload-files [Documents]="economicsubstance.provisionalTreatmentDocument"
                                      [DocumentTypeName]="ProvisionalTreatmentDocumentDoc"
                                      [IsEconomicDocument]="true"
                                      [readOnlyMode]="readOnlyMode"
                                      [displayFromCa]="displayFromCa"
                                      [ctspId]="ctspId">
                    </app-upload-files>
                </div>
          
        </form>
    </p-card>
</div>
