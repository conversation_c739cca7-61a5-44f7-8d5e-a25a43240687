import { Component, OnInit,Input, EventEmitter, Output, Injector, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    DocumentsDto, EvidenceNonResidencyDocumentsDto, EvidenceProvisionalTreatmentDocumentsDto,
    SupportingDocumentDto, OtherCIGADocumentsDto, HighRiskDocumentsDto,
    ProvisionalTreatmentDocumentDto, EconomicSubstanceInformationRequiredDocumentsDto,
    EconomicSubstanceServiceProxy, ESEnforcementDocumentsDto, EsCaAssessmentDocumentsDto, GenericRelevantActivityDocumentDto
} from '@shared/service-proxies/service-proxies';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { EconomicDocumentsName, RelevantDocumentsName } from '@app/economicsubstance/EconomicSubstance';
import { AppConsts } from '@shared/AppConsts';

@Component({
  selector: 'app-upload-files',
  templateUrl: './upload-files.component.html',
  styleUrls: ['./upload-files.component.css']
})
export class UploadFilesComponent extends AppComponentBase implements OnInit
{

    @Input() Documents: any;
    @Input() DocumentTypeName: any;
    @Input() IsEconomicDocument: boolean;
    @Input() readOnlyMode: boolean;

    @Input() displayFromCa: boolean;
    @Input() ctspId: any;

    evidanceDoc: any;
    constructor(injector: Injector, private _sharedComponet: SharedComponent, private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy) { super(injector); }

  ngOnInit() {
  }

    AddDocument(items: DocumentsDto[]): void
    {
       if (items)
       {
            items.forEach((item) =>
            {

                switch (this.DocumentTypeName) {
                    case EconomicDocumentsName.EvidenceNonResidency:
                        { this.evidanceDoc = new EvidenceNonResidencyDocumentsDto(); break; }
                    case EconomicDocumentsName.EvidenceProvisionalTreatment:
                        { this.evidanceDoc = new EvidenceProvisionalTreatmentDocumentsDto(); break; }
                    case RelevantDocumentsName.HighRiskDocuments:
                        {
                            this.evidanceDoc = new HighRiskDocumentsDto(); break;
                        }
                    case RelevantDocumentsName.OtherCIGADocuments:
                        {
                            this.evidanceDoc = new OtherCIGADocumentsDto(); break;
                        }
                    case EconomicDocumentsName.SupportingDocument:
                        {
                            this.evidanceDoc = new SupportingDocumentDto(); break;
                        }
                    case EconomicDocumentsName.ProvisionalTreatmentDocumentDoc:
                        {
                            this.evidanceDoc = new ProvisionalTreatmentDocumentDto(); break;
                        }

                    case EconomicDocumentsName.InformationRequestedDocumentDoc:
                        {
                            this.evidanceDoc = new EconomicSubstanceInformationRequiredDocumentsDto(); break;
                        }

                    case EconomicDocumentsName.EnforcmentDocumentDoc:
                        {
                            this.evidanceDoc = new ESEnforcementDocumentsDto(); break;
                        }
                    case EconomicDocumentsName.ESCAAssessmentDocumentsDoc:
                        {
                            this.evidanceDoc = new EsCaAssessmentDocumentsDto(); break;
                        }
                    // explanation of how that tangible asset is being used to generate income, this field that will allow user to upload supporting documentation 
                    case RelevantDocumentsName.TangibleAssetIncomeDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // identify the decisions for which each employee is responsible for in respect of the generation of income from the intangible asset, this field that will allow user to upload supporting documentation
                    case RelevantDocumentsName.TangibleAssetEmployeeResponsibilityDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // the nature and history of strategic decisions (if any) taken by the entity in the Virgin Islands, this field that will allow user to upload supporting documentation
                    case RelevantDocumentsName.HistoryofStrategicDecisionsInBVIDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // the nature and history of the trading activities (if any carried out in the Virgin Islands by which) the intangible assets is exploited for the purpose of generating income from third parties, this field that will allow user to upload supporting documentation
                    case RelevantDocumentsName.HistoryofTradingActivityIncomeDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // provide the detail business plans which explain the commercial rationale of holding the intellectual property assets in the Virgin Islands, this field that will allow user to upload supporting documentation 
                    case RelevantDocumentsName.IPAssetsInBVIDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // identify the decisions for which each employee is responsible in respect of the generation of income from the intangible asset, this field that will allow user to upload supporting documentation 
                    case RelevantDocumentsName.IPAssetsEmployeeResponsibilityDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }
                    // provide concrete evidence that decision making is taking place within the Virgin Islands, including but not limited to, minutes of meetings which have taken place in the Virgin Islands, this field that will allow user to upload supporting documentation
                    case RelevantDocumentsName.ConcreteEvidenceDecisionInBVIDocuments:
                        {
                            this.evidanceDoc = new GenericRelevantActivityDocumentDto(); break;
                        }

                }

               this.evidanceDoc.documents = item;
               this.evidanceDoc.documents.id = item.id;
               this.evidanceDoc.documentsId = item.id;
               this.evidanceDoc.fileName = item.fileName;
               this.evidanceDoc.contetntType = item.contetntType;
               this.Documents.push(this.evidanceDoc);
             }
            );
        }
    }


    RemoveDoc(file: any)
    {
        
        let self = this;
        if (file.id)
        {
            abp.message.confirm(
                AppConsts.messageList.EsDeletedConfirmation,
                'Are you sure you want to delete it?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        self.handleDelete(file);
                    }
                }
            );
        }
        else
        {
            abp.message.confirm(
                AppConsts.messageList.EsDeletedConfirmation,
                'Are you sure you want to delete it?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        self.handleRemove(file);
                    }
                }
            );
        }
    }

    handleRemove(file: any)
    {
        this.Documents = this.Documents.filter(x => x.documentsId != file.documentsId);
    }


    handleDelete(file: any)
    {
        if (this.IsEconomicDocument) {
            this._economicSubstanceServiceProxy.removeEconomicSubstanceDocuments(file.id).subscribe(data => {
                let object = this.Documents.find(x => x.id == file.id);
                object.isDeleted = true;

            }
            );
        }
        else {
            this._economicSubstanceServiceProxy.removeRelativeActivitiesDocuments(file.id).subscribe(data => {
                let object = this.Documents.find(x => x.id == file.id);
                object.isDeleted = true;

            }
            );
        }
    }
    PreviewDoc(id: any)
    {
        this._sharedComponet.PreviewDoc(id, this.ctspId, this.displayFromCa);
    }

    returnDate(obj) {
        if (obj) return new Date(obj.format('YYYY-MM-DD HH:mm:ss') + ' UTC');
    }
}
