<p-table [value]="sortedDeclarations" scrollHeight="102px" [scrollable]="true">
    <ng-template pTemplate="header">
        <tr>
            <th>{{l("Financial Year End Year")}}</th>
            <th>{{l("Submitted By")}}</th>
            <th>{{l("Submission Time")}}</th>
            <th>{{l("Last Saved Date")}}</th>
            <th>{{l("Submission Status")}}</th>
            <th>{{l("Action")}}</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-item>
        <tr>
            <td>{{item.fiscalEndDate | date: "yyyy"}}</td>
            <td>{{item.displayName}}</td>
            <td >
                <div *ngIf="isSubmitted(item.status)">
                    {{item.submissionDateLocal | date: "dd/MM/yyyy HH:mm"}}
                </div>

            </td>
            <td >
                <div>
                    {{item.submissionDateLocal | date: "dd/MM/yyyy HH:mm"}}
                </div>
            </td>
            <td>{{getstatus(item.status)}}</td>
            <td>
                <div *ngIf="isESStatusCompleted(item)">
                    <a id="viewSubmission" (click)="viewSubmission(item)"><i class="pi pi-search"></i></a>
                    <a id="showESSHistory" *ngIf="item.hasAnyDeclarationHistory" (click)="showESSHistory(item)"><i class="pi pi-undo"></i></a>
                </div>
                <div *ngIf="!isESStatusCompleted(item)">
                    <a id="discardSubmission" *ngIf="!item.hasAnyDeclarationHistory" (click)="discardSubmission(item)"><i class="pi pi-trash"></i></a>
                    <a id="editSubmission" (click)="editSubmission(item)"><i class="pi pi-pencil"></i></a>
                    <a id="showHistory" *ngIf="item.hasAnyDeclarationHistory" (click)="showESSHistory(item)"><i class="pi pi-undo"></i></a>
                </div>

            </td>
        </tr>
    </ng-template>
</p-table>


<esHistoryModal #esHistoryModal></esHistoryModal>


