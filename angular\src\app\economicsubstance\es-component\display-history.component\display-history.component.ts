import { Location } from '@angular/common';
import { Component, Injector, HostListener, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SubmittedLocation } from '@app/economicsubstance/EconomicSubstance';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, CorporateEntityDto, AuditEventServiceProxy, SingleFieldInputOfGuid, CaCtspServiceProxy, CASearchServiceServiceProxy} from '@shared/service-proxies/service-proxies';
import { forkJoin } from 'rxjs';
import { PrintService } from '@shared/services/print.service';

@Component({
  selector: 'app-display-history',
  templateUrl: './display-history.component.html',
  styleUrls: ['./display-history.component.css']
})
export class DisplayHistoryComponent extends AppComponentBase
{
    id: any;
    readytoDisplay: boolean = false;
    corporateEntity: CorporateEntityDto;
    currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    country: any;
    action: any;
    importOnlyMode: boolean = false;
    importStatus: string = '';
    fileid: any;
    ctspId: any;
    ctspIdisplay: any;


    constructor(injector: Injector, private location: Location,
        private  _caCtspServiceProxy:CaCtspServiceProxy,
        private _auditEventService: AuditEventServiceProxy,
        private _sharedComponet: SharedComponent,
        private router: Router,
        private route: ActivatedRoute,
        public printService: PrintService)
    {
        super(injector)
        this.route.params.subscribe(x =>
        {
            this.id = x.id;
            this.ctspId = x.ctspid;
            this.ctspIdisplay = x.ctspid;
            let test = this.ctspIdisplay;
            if (this.ctspId == -1) this.ctspId = undefined;
            forkJoin([
                this._caCtspServiceProxy.getEconomicSubstanceDetailHistory(this.id, this.ctspId ),
            ]).subscribe(responseList => {

                this.currentEconomicSubstance = responseList[0];
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, responseList[0]);
                this.corporateEntity = this.currentEconomicSubstance.corporateEntity;

                this.readytoDisplay = true;

            });
           
        });


    }



    @HostListener("window:afterprint", [])
    onWindowAfterPrint() {
        let auditInput = new SingleFieldInputOfGuid();
        auditInput.value = this.id;
        this._auditEventService.auditCtspEconomicSubstancePrintEvent(auditInput).subscribe();;
    }

    onPrint() {
        this.printService.print();
    }

    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'display': 'none',
            };
        }

        return {};
    }

  ngOnInit() {
  }

    onClose() {
            this.location.back();
    }

}
