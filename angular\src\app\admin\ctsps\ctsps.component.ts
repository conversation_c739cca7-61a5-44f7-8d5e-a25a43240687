import { AfterViewInit, Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CtspServiceProxy } from '@shared/service-proxies/service-proxies';
import { SelectItem } from 'primeng/api';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Paginator } from 'primeng/components/paginator/paginator';
import { Table } from 'primeng/components/table/table';
import { finalize } from 'rxjs/operators';
import { EditCtspComponent } from './edit-ctsp/edit-ctsp.component';

@Component({
    templateUrl: './ctsps.component.html',
    styleUrls: ['./ctsps.component.less'],
    encapsulation: ViewEncapsulation.None,
    animations: [appModuleAnimation()]
})
export class CtspsComponent extends AppComponentBase implements AfterViewInit {

    @ViewChild('EditCtsp', { static: true }) EditCtsp: EditCtspComponent;
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    //Filters
    filterText = '';
    status = '';

    activeStatus = 'Active';
    inactiveStatus = 'Inactive';

    statuses: SelectItem[] = [
        { label: "Active", value: this.activeStatus },
        { label: "Inactive", value: this.inactiveStatus },
        { label: "All", value: "All" }
    ];

    constructor(
        injector: Injector,
        private _ctspServiceProxy: CtspServiceProxy
    ) {
        super(injector);
    }

    ngOnInit() {
        this.status = this.statuses[0].value
    }

    ngAfterViewInit(): void {
        this.primengTableHelper.adjustScroll(this.dataTable);
    }

    ngOnDestroy() {
    }

    getCtsps(event?: LazyLoadEvent) {
        if (this.primengTableHelper.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            return;
        }

        this.primengTableHelper.showLoadingIndicator();

        var isActive = this.status == this.activeStatus;
        var isInactive = this.status == this.inactiveStatus;

        this._ctspServiceProxy.getCtsps(
            this.filterText,
            isActive,
            isInactive,
            this.primengTableHelper.getSorting(this.dataTable),
            this.primengTableHelper.getMaxResultCount(this.paginator, event),
            this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }

    reloadPage(): void {
        this.paginator.changePage(this.paginator.getPage());
    }

    onRowSelect(record: any)
    {
       this.EditCtsp.get(record.data.id);
    }
}
