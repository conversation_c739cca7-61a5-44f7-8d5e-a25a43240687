import { Component, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { PersonDetailsDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../../shared/utils/validation/ng-form-validation.component';

@Component({
  selector: 'app-person-detail-dialog',
  templateUrl: './person-detail-dialog.component.html',
  styleUrls: ['./person-detail-dialog.component.css']
})
export class PersonDetailDialogComponent extends AppComponentBase implements OnInit
{
    @Output() submitted: EventEmitter<PersonDetailsDto> = new EventEmitter<PersonDetailsDto>();
    personDetails: PersonDetailsDto= new PersonDetailsDto();

    @ViewChild('personalmodal', { static: true }) modal: ModalDirective;
    @ViewChild('personalForm', { static: true }) personalForm: FormGroup;
    @ViewChild('personFormValidation', { static: true }) personFormValidation: NgFormValidationComponent;

    constructor(injector: Injector) { super(injector);}

    ngOnInit()
    {
        this.personFormValidation.formGroup = this.personalForm;
    }
    shown(): void {
    }

    show(item, isNew): void {

        this.personDetails = new PersonDetailsDto();
        if (!isNew) this.personDetails = _.cloneDeep(item);
        this.modal.show();
    }

    close(): void {
        this.personalForm.reset();
        this.modal.hide();
    }

    save(): void {

        if (!this.personFormValidation.isFormValid()) {
            return;
        }
        if (!this.personDetails.id) { this.personDetails.id = uuid.v4(); this.personDetails.isNew = true; }
        this.submitted.emit(this.personDetails);
        this.close();

    }
}
