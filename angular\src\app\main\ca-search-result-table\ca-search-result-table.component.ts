import { Component, OnInit, Injector, Input, ViewChild, Output, EventEmitter } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CaSearchResultItems } from '../viewmodel/ca-search-result-viewmodel';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Table } from 'primeng/components/table/table';
import { CaEntityListDto, CASearchServiceServiceProxy, ESReviewStatusDto } from '@shared/service-proxies/service-proxies';
import { debounce } from 'rxjs/operators';
import { CustomPaginatorComponent } from '@shared/common/components/custom-paginator/custom-paginator.component';

@Component({
    selector: 'app-ca-search-result-table',
    templateUrl: './ca-search-result-table.component.html',
    styleUrls: ['./ca-search-result-table.component.css']
})
export class CaSearchResultTableComponent extends AppComponentBase implements OnInit {

    @ViewChild('resultTable', { static: true }) resultTable: Table;
    @ViewChild(CustomPaginatorComponent, { static: true }) paginationComponent: CustomPaginatorComponent;

    esReviewStatus: ESReviewStatusDto[];
    colorMap = [
        { code: 2, color: 'grey' },
        { code: 4, color: 'blue' },
        { code: 6, color: 'darkorange' },
        { code: 7, color: 'green' },
        { code: 8, color: 'red' }
    ];
    private _resultItems: CaSearchResultItems
    get resultItems() {
        return this._resultItems
    }
    @Input() set resultItems(val: CaSearchResultItems) {
        this._resultItems = val;
        if (this._resultItems) {
            this.resultTable.reset()
        }

    }
    @Output() setColorCodeList: EventEmitter<any[]> = new EventEmitter<any[]>();
    // need to get the list of review status fromt he server to map it to the int value 
    constructor(injector: Injector, private _caSearchServiceServiceProxy: CASearchServiceServiceProxy) {
        super(injector);

        this._caSearchServiceServiceProxy.getESReviewStatus("-1", undefined, undefined).subscribe(data => {
            this.esReviewStatus = data;
            this.setColorCodeList.emit(this.getColorCodeList(data));
        });
    }

    ngOnInit() {
       
    }
    getEntitiesRecords(event: LazyLoadEvent) {
        console.log(event);
        this.getItems(event);
    }
    onPageSizeChange(event) {
        var lazyEvent ={
            first: 0,
            rows: event,    
            pagecount: event,
            page:1
        }
        this.getItems(lazyEvent);
      }
    getItems(event?: LazyLoadEvent) {
        if (!this._resultItems) {
            return
        }
        return this._resultItems.getItems(event)
    }

    getReviewStatusName(item): string {
        let foundItem = this.esReviewStatus.find(x => x.code == item);
        if (foundItem) return foundItem.name;
        else
            return "Unsubmitted";
    }

    needTodisplaySubmissionTime(item): boolean {
        return this.getReviewStatusName(item) !== "Unsubmitted"
    }

    getReviewStatusColor(item: CaEntityListDto) {
        if (item.isDeleted) return "red";

        var color = this.colorMap.find(x => x.code == item.reviewStatus);
        if (color != null) {
            return color.color;
        }

        return 'black';
    }

    getColorCodeList(statusList: ESReviewStatusDto[]) {
        var colorList = [];

        statusList.map(s => {
            var color = this.colorMap.find(x => x.code == s.code);

            if (color) {
                colorList.push({ name: s.name, color: color.color });
            }
        });

        colorList.push({ name: 'Other than above', color: 'black' })
        return colorList;
    }
}


