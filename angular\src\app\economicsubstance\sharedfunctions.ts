import { AppComponentBase } from '@shared/common/app-component-base';
import { Injector, Injectable } from '@angular/core';
import { ActivityName, RelaventActivity, ES_Step1 } from './EconomicSubstance';
import { DialogService } from 'primeng/api';
import * as moment from 'moment';
import { DocumentPreviewDialogComponent } from '@app/economicsubstance/es-component/document-preview-dialog/document-preview-dialog.component';
import {
    CountryDto, CorporateEntityDto, AddressDto, RelevantActivityDetailDto, CurrencyDto,
    CASearchServiceServiceProxy, EconomicSubstanceServiceProxy, EconomicSubstanceDeclarationDto
} from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
@Injectable()


export class SharedComponent extends AppComponentBase {

    constructor(injector: Injector,
        private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy,
        private _caSearchServiceServiceProxy: CASearchServiceServiceProxy,
        private _dialogService: DialogService

    ) {
        super(injector);
    }


    public sort_country(data: any, includeLocalCountry?: boolean): CountryDto[] {
        let countryCodeNone: CountryDto;
        let country = data.sort((a, b) => {
            if (a.countryName == b.countryName) return 0;
            if (a.countryName < b.countryName) return -1;
            return 1;
        });
        countryCodeNone = new CountryDto();
        countryCodeNone.countryCode = 'N/A';
        countryCodeNone.countryName = this.l("Select country");

        if (typeof includeLocalCountry === 'undefined') {
            let bviCountry = country.find(x => x.countryCode === 'VGB');
            country = country.filter(x => x.countryCode !== 'VGB');
            country.unshift(bviCountry);
        }


        country.unshift(countryCodeNone);
        return country;
    }

    public sort_currency(data: any): CurrencyDto[] {

        let currencyCodeNone: CurrencyDto;
        let currency = data.sort((a, b) => {
            if (a.currencyCode == b.currencyCode) return 0;
            if (a.currencyCode < b.currencyCode) return -1;
            return 1;
        });

        let usdCurrency = currency.find(x => x.currencyCode === 'USD');
        currency = currency.filter(x => x.currencyCode !== 'USD');
        currency.unshift(usdCurrency);

        return currency;
    }

    public convertDatetime(currentEconomicSubstance: EconomicSubstanceDeclarationDto, data: any) {
        if (currentEconomicSubstance) {
            if (data.fiscalStartDate) currentEconomicSubstance.fiscalStartDate = data.fiscalStartDate.startOf('day').toDate();
            if (data.fiscalStartDate) currentEconomicSubstance.fiscalEndDate = data.fiscalEndDate.startOf('day').toDate();
            if (data.defaultStartDate) currentEconomicSubstance.defaultStartDate = data.defaultStartDate.startOf('day').toDate();
            if (data.defaultEndDate) currentEconomicSubstance.defaultEndDate = data.defaultEndDate.startOf('day').toDate();


            if (currentEconomicSubstance.relevantActivities) {
                for (var i = 0; i < currentEconomicSubstance.relevantActivities.length; i++) {
                    if (currentEconomicSubstance.relevantActivities[i].isChecked) {
                        currentEconomicSubstance.relevantActivities[i].startDate = data.relevantActivities[i].startDate ? data.relevantActivities[i].startDate.startOf('day').toDate() : currentEconomicSubstance.relevantActivities[i].startDate;
                        currentEconomicSubstance.relevantActivities[i].endDate = data.relevantActivities[i].endDate ? data.relevantActivities[i].endDate.startOf('day').toDate() : currentEconomicSubstance.relevantActivities[i].endDate;
                    }
                }
            }
        }

    }


    public getNextLetter(char: string): string {
        let code = char.charCodeAt(0);
        code++;
        return String.fromCharCode(code);
    }





    displaybool(value: boolean): string {

        if (value) return 'YES';
        return 'NO';

    }

    // utility to pass Coperation Entity and return the sorresponding activity

    // return as string sasasa, dsdsds 

    listoflicenseInderAct(entity: CorporateEntityDto): string {
        if (entity) {
            let result;
            if (entity.licenseInsuranceAct) result = 'Insurance Act';

            if (entity.licenseForeignCurrencyPermit) {
                if (result)
                    result = result + ', Foreign Currency Permit';
                else
                    result = 'Foreign Currency Permit';
            }

            if (entity.licenseFinancialInstitutionAct) {
                if (result)
                    result = result + ', Financial Institution Act';
                else
                    result = 'Financial Institution Act';
            }

            if (entity.licenseActOther) {
                if (result)
                    result = result + ', ' + entity.licenseActOtherName;
                else
                    result = entity.licenseActOtherName;
            }
            return result;
        }

    }

    formatAddress(address: any): string {
        if (address) {
            if (address.country) {
                const addrValues = [address.addressLine1, address.addressLine2, address.country.countryName]
                return addrValues.filter(x => x != 'Select country').join(', ');
            }
            {
                const addrValues = [address.addressLine1, address.addressLine2]
                return addrValues.filter(x => x != 'Select country').join(', ');
            }

        }
    }

    formatAddress2(addressLine1: any, addressLine2: any, country: any): string {
        const addrValues = [addressLine1, addressLine2, country.countryName]
        return addrValues.filter(x => x != 'Select country').join(', ');

    }
    PreviewDoc(id: any, ctspNumber: any, displayFromCa: boolean) {

        if (displayFromCa) {

            this._caSearchServiceServiceProxy.getDocument(ctspNumber, id).subscribe(data => {
                this._dialogService.open(DocumentPreviewDialogComponent, {
                    data: { doucmnet: data },
                    header: this.l("Document Preview"),
                    width: '80%',
                    height: '80%',
                    baseZIndex: 2000,
                    contentStyle: { 'height': '100%', 'display': 'flex', }
                });
            });
        }
        else {
            this._economicSubstanceServiceProxy.getDocument(undefined, undefined, id, undefined, undefined, undefined, undefined, undefined, undefined).subscribe(data => {
                this._dialogService.open(DocumentPreviewDialogComponent, {
                    data: { doucmnet: data },
                    header: this.l("Document Preview"),
                    width: '80%',
                    height: '80%',
                    contentStyle: { 'height': '100%', 'display': 'flex' }
                });
            });
        }

    }

    // Return true if the old and new object have difference

    CompareOldNewValue(newValue: EconomicSubstanceDeclarationDto, oldValue: EconomicSubstanceDeclarationDto): boolean {
        return _.isEqual(newValue, oldValue);

    }

}
