{"format": 1, "restore": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.Services\\Bdo.Ess.Microservice.EconomicSubstance.Services.csproj": {}}, "projects": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj", "projectName": "Bdo.Ess.Shared", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj", "projectName": "Bdo.Ess.Common", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Abp": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.AspNetCore.SignalR.Core": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider": {"target": "Package", "version": "[1.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.KeyPerFile": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.8, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}, "Polly": {"target": "Package", "version": "[8.2.0, )"}, "Polly.Contrib.WaitAndRetry": {"target": "Package", "version": "[1.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj", "projectName": "Bdo.Ess.Dtos", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.csproj", "projectName": "Bdo.Ess.Microservice.Core.EntityFramework.Ctsp", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"EFCore.BulkExtensions": {"target": "Package", "version": "[3.2.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj", "projectName": "Bdo.Ess.Microservice.Core.EntityFramework", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\Bdo.Ess.Microservice.Core.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\Bdo.Ess.Microservice.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.1, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[3.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\Bdo.Ess.Microservice.Core.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\Bdo.Ess.Microservice.Core.Services.csproj", "projectName": "Bdo.Ess.Microservice.Core.Services", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\Bdo.Ess.Microservice.Core.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework\\Bdo.Ess.Microservice.Core.EntityFramework.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\Bdo.Ess.Microservice.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\Bdo.Ess.Microservice.Core.csproj", "projectName": "Bdo.Ess.Microservice.Core", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\Bdo.Ess.Microservice.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Common\\Bdo.Ess.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[3.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.csproj", "projectName": "Bdo.Ess.Microservice.EconomicSubstance.EntityFramework", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp\\Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.Services\\Bdo.Ess.Microservice.EconomicSubstance.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.Services\\Bdo.Ess.Microservice.EconomicSubstance.Services.csproj", "projectName": "Bdo.Ess.Microservice.EconomicSubstance.Services", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.Services\\Bdo.Ess.Microservice.EconomicSubstance.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\Bdo.Ess.Microservice.Core.Services.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.Core.Services\\Bdo.Ess.Microservice.Core.Services.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework\\Bdo.Ess.Microservice.EconomicSubstance.EntityFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Reactive": {"target": "Package", "version": "[4.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj", "projectName": "Bdo.Ess.Services", "projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\Bdo.Ess.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\Bdo.Ess.Shared\\Bdo.Ess.Shared.csproj"}, "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj": {"projectPath": "C:\\Projects\\AzureGit\\BVI-ESS\\ESS\\aspnet-core\\src\\Bdo.Ess.Dtos\\Bdo.Ess.Dtos.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.AspNetCore.Mvc.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}}}