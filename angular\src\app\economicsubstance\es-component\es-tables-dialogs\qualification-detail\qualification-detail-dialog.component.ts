import { Component, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EmployeeQualificationDetailsDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../../shared/utils/validation/ng-form-validation.component';

@Component({
  selector: 'app-qualification-detail-dialog',
  templateUrl: './qualification-detail-dialog.component.html',
  styleUrls: ['./qualification-detail-dialog.component.css']
})
export class QualificationDetailDialogComponent extends AppComponentBase implements OnInit {

    @Output() submitted: EventEmitter<EmployeeQualificationDetailsDto> = new EventEmitter<EmployeeQualificationDetailsDto>();
    details: EmployeeQualificationDetailsDto = new EmployeeQualificationDetailsDto();

    @ViewChild('qualificationmodal', { static: true }) modal: ModalDirective;
    @ViewChild('qualificationform', { static: true }) qualificationform: FormGroup;
    @ViewChild('qualificationFormValidation', { static: true }) qualificationFormValidation: NgFormValidationComponent;
    importOnlyMode: boolean;

   
    constructor(injector: Injector) { super(injector); }

    ngOnInit()
    {
        this.qualificationFormValidation.formGroup = this.qualificationform;
    }
    shown(): void {
    }
    show(item, isNew, importOnlyMode): void {

        this.importOnlyMode = importOnlyMode;

        this.details = new EmployeeQualificationDetailsDto();
        if (!isNew) this.details = _.cloneDeep(item);
        this.modal.show();
    }

    close(): void {
        this.qualificationform.reset();
        this.modal.hide();
    }

    save(): void {
        if (!this.qualificationFormValidation.isFormValid()) {
            return;
        }
        if (!this.details.id) { this.details.id = uuid.v4(); this.details.isNew = true; }
        this.submitted.emit(this.details);
        this.close();

    }

}
