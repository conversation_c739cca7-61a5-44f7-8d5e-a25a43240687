import { Component, Injector, OnInit } from '@angular/core';
import { AppComponentBase } from "@shared/common/app-component-base";
import { CaSearchViewModel,CaSearchType } from '../viewmodel/ca-search-viewmodel';
import { EconomicSubstanceSearchServiceProxy, CtspServiceProxy, GetEntitiesSearchInput, } from "@shared/service-proxies/service-proxies";
import { CTSPListItem } from '../ctsp-list/ctsp-list-item';
import { ReportModel } from './ca-home-report-model';
import { downloadFile } from "@app/main/viewmodel/utils";
import { getRandomKey } from '../viewmodel/utils';

@Component({
  selector: 'app-ca-home-report',
  templateUrl: './ca-home-report.component.html',
  styleUrls: ['./ca-home-report.component.css']
})
export class CaHomeReportComponent extends AppComponentBase implements OnInit {
  ctspItems: CTSPListItem[];
  reportModel = new ReportModel();
  viewModel: CaSearchViewModel = new CaSearchViewModel(x => this.l(x));

  constructor(
    injector: Injector,
    private _economicSubstanceSearchServiceProxy: EconomicSubstanceSearchServiceProxy,
    private _ctspServiceProxy: CtspServiceProxy
  ) {

    super(injector);
    this._ctspServiceProxy.getAllCtsps().subscribe(result => {
      this.ctspItems = result.map(x => {
        let item = new CTSPListItem();
        item.id = x.id;
        item.number = x.number;
        item.name = x.name;
        item.isProduction = x.isProduction;
        item.emailAddress = x.emailAddress;
        item.phoneNumber = x.phoneNumber;
        item.itPhoneNumber = x.itPhoneNumber;
        item.comments = x.comments;
        item.active = x.active;
        item.address = x.address;
        item.caCtspName = x.caCtspName;
        return item;
      });
    });
  }

  ngOnInit() {
  }

  setSelectedRAs(selectedRAList: CTSPListItem[]) {
    this.viewModel.essSearch.ctspIds = selectedRAList.map(x => {
      let ss = x.id;
      return ss;

    });
  }

  reloadCurrentPage() {
    window.location.reload();
  }

  resetform() {
    this.reportModel.CSPNumber = null;
    this.reportModel.EsName = null;
    this.reportModel.FinancialPeriodDate = null;
    this.reportModel.AssessmentStatus = null;
    this.reportModel.RelevantActivity = null;
    this.reportModel.NonResidenceDeclaration = null;
  }

  generateESReport() {
    
  }
}
