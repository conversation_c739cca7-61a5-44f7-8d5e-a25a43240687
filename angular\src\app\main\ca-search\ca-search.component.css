.entity-container{
    width: 45rem;
    margin-top: 4rem;
}

.footer-note{
    font-weight: normal;
    text-align: center;
    font-size: 12px;
}



.ca-entitysearch-container
.ca-entitysearch-search-section {
    padding: 25px;
    margin: 0px auto;
    min-height: 374px;
    text-align: Left;
}

.basicSearch {
    max-width: 374px;
    margin: 0px auto;
}

.ca-entitysearch-title {
    font-size: 20px;
    padding: 1rem;
    margin-right: 2rem;
    text-align: center;
}



.ca-entitysearch-row {
    text-align: left;
}

.ca-entitysearch-submit {
    margin-top: 15px;
}

.ca-entitysearch-submit-section {
    clear: both;
}


.ca-entitysearch-ralist-section 
    .ca-ra-selection-list-grid
{
    overflow-y: auto;
    height: calc(100vh - 18em);
}



.ca-entitysearch-container,
.ca-entitysearch-search-section,
.ca-entitysearch-ralist-section {
    height: 100%;
}

.ca-entitysearch-search-section,
.ca-entitysearch-ralist-section {
    padding: 15px;
}

.button-section,
p-header p {
    text-align: center !important;
}

.searchtypelinks {
    margin-top: 5px;
}

.searchtypelinks a {
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
}

.ui-toast {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30em;
}

.ui-toast-message-content {
  font-family: 'Courier New', Courier, monospace;
  background: #FFD07B;
}

p-dialog p {
    text-align: center !important;
}

.customDialog {
    background: grey;
}
.multibuttons a {
    margin-left: 10px;
}