<div class="primeng-datatable-container es-dashboard"
    [busyIf]="redFlagEvents.primengTableHelper.isLoading || viewModel.nonResidentReportViewModel.busy">
    <p-card class="p-card-properties_dashboard">
        <p-header class="row-flex-space">
            <p>{{"Economic Substance (ES) Compliance Monitoring" | localize}}</p>
            <p *ngIf="tabIndex == 0" class="updateTime">
                Stats are refreshed nightly. Last update as of
                {{viewModel.updateTimeObservable | async | date: 'yyyy-MM-dd HH:mm'}}.
            </p>
            <p *ngIf="tabIndex == 1" class="updateTime">
                Red Flag Events are identified nightly. Last update as of
                {{viewModel.redFlagUpdateTimeObservable | async | date: 'yyyy-MM-dd HH:mm'}}.
            </p>
            <p *ngIf="tabIndex == 2" class="updateTime">
                Stats are refreshed nightly. Last update as of
                {{viewModel.updateTimeObservable | async | date: 'yyyy-MM-dd HH:mm'}}.
            </p>
            <p *ngIf="tabIndex == 3" class="updateTime">
                {{"FHTPReportUpdateTime" | localize}}
                {{viewModel.fhtpUpdateTimeObservable | async | date: 'yyyy-MM-dd HH:mm'}}.
            </p>
            <div class="row-flex-justified">
                <button id="settings" *ngIf="tabIndex == 1" pButton type="button" icon="pi pi-cog" class="margin-left bigger-icons"
                    iconPos="left" (click)="goToSettings($event)" tooltip="{{'Settings' | localize}}"></button>
            </div>
        </p-header>
        <div class="row-flex-space">
            <div *ngIf="tabIndex == 0" class="half-and-quarter-width">
                <b>{{"Stats Summary:" | localize}}</b>
                <p-table [value]="viewModel.summaryObservable | async">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>{{"Total # of Entities" | localize}}</th>
                            <th>{{"Total # of ES Declarations" | localize}}</th>
                            <!-- <th>{{"Total # of Entities with ES Declarations Not Started" | localize}}</th> -->
                            <th>{{"Total # of Entities with Filing Overdue" | localize}}</th>
                            <th>{{"# Pending Assessments with 6 years passed" | localize}}</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item>
                        <tr>
                            <td>{{item.numEntities}}</td>
                            <td>{{item.numDeclarations}}</td>
                            <!-- <td>{{item.numEntitiesWithDeclarationNotStarted}}</td> -->
                            <!-- <td>{{item.numEntitiesWithFilingOverdue}}</td> -->
                            <td>
                                <a id="numberOfEntitiesWithFilingOverdue" (click)="getSummaryOverdueLink(item)" class="dashboardLink">
                                    {{item.numEntitiesWithFilingOverdue}}
                                </a>
                            </td>
                            <td>
                                <a id="pendingAssessmentsWith6YearsPassed" (click)="getAssessmentPendingMoreThan6Years(item)" class="dashboardLink">
                                    {{item.numAssessmentPendingMoreThan6Years}}
                                </a>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
            <div *ngIf="tabIndex == 1" class="half-and-quarter-width">
                <b>{{"Red Flag Events Summary:" | localize}}</b>
                <p-table [value]="viewModel.redFlagSummaryObservable | async">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>{{"Total Events" | localize}}</th>
                            <th>{{"Total Filings with Events" | localize}}</th>
                            <th>{{"Total Assessments Not Started" | localize}}</th>
                            <th>{{"% Assessments Not Started" | localize}}</th>
                            <th>{{"Total Assessments Completed" | localize}}</th>
                            <th>{{"% Assessments Passed" | localize}}</th>
                            <th>{{"% Assessments Failed" | localize}}</th>
                            <th>{{"Total Assessments Closed" | localize}}</th>
                            <th>{{"% Assessments Closed" | localize}}</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item>
                        <tr>
                            <td>{{item.totalEvents}}</td>
                            <td>{{item.totalFilings}}</td>
                            <td>{{item.totalNotStarted}}</td>
                            <td>{{item.percentNotStarted | percent:'1.2-2'}}</td>
                            <td>{{item.totalCompleted}}</td>
                            <td>{{item.percentPassed | percent :'1.2-2'}}</td>
                            <td>{{item.percentFailed | percent:'1.2-2'}}</td>
                            <td>{{item.totalClosed | localize}}</td>
                            <td>{{item.percentClosed | percent :'1.2-2'}}</td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
            <!--<div *ngIf="tabIndex == 2" class="half-and-quarter-width">
                <b>{{"Non-Resident Summary:" | localize}}</b>
                <p-table [value]="viewModel.nonResidentReportViewModel.summary">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>{{"Total # of Reports" | localize}}</th>
                            <th>{{"Total # of ES Returns" | localize}}</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item>
                        <tr>
                            <td>{{item.numReports}}</td>
                            <td>{{item.numFilings}}</td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>-->
            <div class="col-flex">
                <label><b>{{"Fiscal Period End" | localize}}</b></label>
                <p-dropdown id="selectedYear" name="year" [options]="viewModel.years" [style]="{'width':'auto'}"
                    [(ngModel)]="viewModel.selectedYear"></p-dropdown>
            </div>
        </div>
        <div class="row-flex-space">
            <!-- <p class="statsUpdate">Stats are refreshed nightly. Last update as of {{viewModel.lastUpdateObservable | async | date:'long'}} </p> -->
        </div>
    </p-card>
    <hr />
    <p-tabView class="dashboard-tabview" (onChange)="handleTabChange($event)" [activeIndex]="activeIndex">
        <p-tabPanel [header]="'Statistics' | localize">
            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-60">
                        <p-header class="row-flex-space">
                            <p>{{"Entities, Filings and Assessments Overview" | localize}}</p>
                        </p-header>
                        <div>
                            <p-chart type="bar" [plugins]="chartLabelPlugin" [options]="options" class="clickable" [data]="viewModel.overviewDataObservable | async" (onDataSelect)="searchEntities($event)"></p-chart>
                        </div>
                    </p-card>
                    <p-card class="p-card-properties_dashboard width-40" [style]="{'min-height': '98.5%'}">
                        <p-header class="row-flex-space">
                            <p>{{"# of Tax Residents Outside of BVI" | localize}}</p>
                        </p-header>
                        <div>
                            <p-chart type="pie" [plugins]="chartLabelPlugin" [options]="barChartoptions" class="clickable" [data]="viewModel.externalResidentsObservable | async" (onDataSelect)="searchByTaxResident($event)"></p-chart>
                        </div>
                    </p-card>
                </div>
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-60">
                        <p-header class="row-flex-space">
                            <p>{{"Assessments with information required/received from CSPs" | localize}}</p>
                        </p-header>
                        <div>
                            <p-chart type="horizontalBar" [plugins]="chartLabelPlugin" [options]="options" class="clickable" [data]="viewModel.informationRequireedObservable | async" (onDataSelect)="selectChartData($event)"></p-chart>
                        </div>
                    </p-card>
                    <p-card class="p-card-properties_dashboard width-40" [style]="{'min-height': '98.5%'}">
                        <p-header class="row-flex-space">
                            <p>{{"Provisional Treatment for non-residency" | localize}}</p>
                        </p-header>
                        <div>
                            <p-chart type="horizontalBar" [plugins]="chartLabelPlugin" [options]="options" class="clickable" [data]="viewModel.provisionalTreatmentObservable | async" (onDataSelect)="selectChartData($event)"></p-chart>
                        </div>
                    </p-card>
                </div>
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-60">
                        <p-header class="row-flex-space">
                            <p>{{"Relevant Activities Overview" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.relaventActivityOverviewObservable | async">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th [style.width]="'32%'" [pSortableColumn]="'name'">{{"Relevant Activities" | localize}}<p-sortIcon [field]="'name'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numFilings'">{{"# of Filings" | localize}}<p-sortIcon [field]="'numFilings'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numBC'">{{"# of BC" | localize}}<p-sortIcon [field]="'numBC'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numFC'">{{"# Of FC" | localize}}<p-sortIcon [field]="'numFC'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numLP'">{{"# Of LP" | localize}}<p-sortIcon [field]="'numLP'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numFLP'">{{"# Of FLP" | localize}}<p-sortIcon [field]="'numFLP'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numMB'">{{"# Of MF" | localize}}<p-sortIcon [field]="'numMB'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numResidents'">{{"# of Residents" | localize}}<p-sortIcon [field]="'numResidents'"></p-sortIcon></th>
                                        <th [pSortableColumn]="'numFailures'">{{"# of Failures" | localize}}<p-sortIcon [field]="'numFailures'"></p-sortIcon></th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>
                                            <a id="relevantActivities" (click)="getRelevantActivities(item)" class="dashboardLink">
                                                {{item.name}}
                                            </a>
                                        </td>
                                        <td>{{item.numFilings}}</td>
                                        <td>{{item.numBC}}</td>
                                        <td>{{item.numFC}}</td>
                                        <td>{{item.numLP}}</td>
                                        <td>{{item.numFLP}}</td>
                                        <td>{{item.numMF}}</td>
                                        <td>{{item.numResidents}}</td>
                                        <td>{{item.numFailures}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                    <p-card class="p-card-properties_dashboard width-40" [style]="{'min-height': '98.5%'}">
                        <p-header class="row-flex-space">
                            <p>{{"Average Assessment Processing Days" | localize}}</p>
                        </p-header>
                        <div>
                            <p-chart type="horizontalBar" [plugins]="chartLabelPlugin" [options]="options" [data]="viewModel.processingDaysObservable | async"></p-chart>
                        </div>
                    </p-card>
                </div>
                <div>
                    <p-card class="p-card-properties_dashboard">
                        <p-header class="row-flex-space">
                            <p>{{"Overview by CSP" | localize}}</p>
                        </p-header>
                        <p-table [value]="viewModel.ctspOverviewObservable | async" [paginator]="true" [rows]="pageSize">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th [pSortableColumn]="'name'">{{"CSP Name" | localize}}<p-sortIcon [field]="'name'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numEntities'">{{"Total # of Entities" | localize}}<p-sortIcon [field]="'numEntities'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numFilings'">{{"# of ES Filings" | localize}}<p-sortIcon [field]="'numFilings'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentFilingsSubmitted'">{{"% of ES Filings Submitted" | localize}}<p-sortIcon [field]="'percentFilingsSubmitted'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numFilingsOverdue'">{{"# of ES Filings Overdue" | localize}}<p-sortIcon [field]="'numFilingsOverdue'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numFilingsOverdue'">{{"# of ES Filings Overdue (Not Struck Off)" | localize}}<p-sortIcon [field]="'numFilingsOverdueNotStruckOff'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentNonResidentCompanies'">{{"% of Non-Resident Companies" | localize}}<p-sortIcon [field]="'percentNonResidentCompanies'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentResidentCompanies'">{{"% of Resident Companies" | localize}}<p-sortIcon [field]="'percentResidentCompanies'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentFilingsWithRedFlag'">{{"% of ES Fillings with Red Flag Events" | localize}}<p-sortIcon [field]="'percentFilingsWithRedFlag'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numAssessmentNotStarted'">{{"# of ES Assessment Not Started" | localize}}<p-sortIcon [field]="'numAssessmentNotStarted'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numAssessmentClosed'">{{"# of ES Assessment Closed" | localize}}<p-sortIcon [field]="'numAssessmentClosed'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'numAssessmentCompleted'">{{"# of ES Assessment Completed" | localize}}<p-sortIcon [field]="'numAssessmentCompleted'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentAssessmentPassed'">{{"% of ES Assessment Passed" | localize}}<p-sortIcon [field]="'percentAssessmentPassed'"></p-sortIcon></th>
                                    <th [pSortableColumn]="'percentAssessmentFailed'">{{"% of ES Assessment Failed" | localize}}<p-sortIcon [field]="'percentAssessmentFailed'"></p-sortIcon></th>
                                    <!-- <th [pSortableColumn]="'percentBarbadianOwnedCompanies'">{{"% of BVI Owned Companies" | localize}}<p-sortIcon [field]="'percentBarbadianOwnedCompanies'"></p-sortIcon></th> -->

                                    <!-- <th [pSortableColumn]="'percentFilingsWithRedFlag'">{{"% ES Filings with Red Flag Events" | localize}}<p-sortIcon [field]="'percentFilingsWithRedFlag'"></p-sortIcon></th> -->
                                    <!-- <th [pSortableColumn]="'percentAssessmentClosed'">{{"% of ES Assessment Closed" | localize}}<p-sortIcon [field]="'percentAssessmentClosed'"></p-sortIcon></th> -->
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item>
                                <tr>
                                    <td>{{item.name}}</td>
                                    <td>{{item.numEntities}}</td>
                                    <td>{{item.numFilingsSubmitted}}</td>
                                    <td>{{item.percentFilingsSubmitted | percent:'1.2-2'}}</td>
                                    <!-- <td>{{item.numFilingsOverdue}}</td> -->
                                    <td>
                                        <a id="numFilingsOverdue" (click)="getCtspOverdueLink(item)" class="dashboardLink">
                                            {{item.numFilingsOverdue}}
                                        </a>
                                    </td>
                                    <!-- <td>{{item.numFilingsOverdueNotStruckOff}}</td> -->
                                    <td>
                                        <a id="numFilingsOverdueNotStruckOff" (click)="getCtspOverdueNotStruckOffLink(item)" class="dashboardLink">
                                            {{item.numFilingsOverdueNotStruckOff}}
                                        </a>
                                    </td>
                                    <td>{{item.percentNonResidentCompanies | percent :'1.2-2'}}</td>
                                    <td>{{item.percentResidentCompanies | percent :'1.2-2'}}</td>
                                    <!-- <td>{{item.percentBarbadianOwnedCompanies | percent:'1.2-2'}}</td> -->
                                    <td>{{item.percentFilingsWithRedFlag | percent :'1.2-2'}}</td>
                                    <td>
                                        <a id="numAssessmentNotStarted" (click)="getCtspNotStartedLink(item)" class="dashboardLink">
                                            {{item.numAssessmentNotStarted}}
                                        </a>
                                    </td>
                                    <td>
                                        <a id="numAssessmentClosed" (click)="getCtspClosedLink(item)" class="dashboardLink">
                                            {{item.numAssessmentClosed}}
                                        </a>
                                    </td>
                                    <td>
                                        <a id="numAssessmentCompleted" (click)="getCtspCompletedLink(item)" class="dashboardLink">
                                            {{item.numAssessmentCompleted}}
                                        </a>
                                        </td>
                                    <td>{{item.percentAssessmentPassed | percent:'1.2-2'}}</td>
                                    <td>{{item.percentAssessmentFailed | percent:'1.2-2'}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <div class="top-right">
                            <div class="page-size-label">
                                {{
                              "Items Per Page"
                                  | localize
                                }}</div>
                            <div class="page-size-select">
                                <select id="pageSize" [(ngModel)]="primengTableHelper.defaultRecordsCountPerPage" (ngModelChange)="onPageSizeChange($event)" class="form-control">
                                    <option *ngFor="let pageSizeOption of primengTableHelper.predefinedRecordsCountPerPage" [ngValue]="pageSizeOption">
                                        {{ pageSizeOption }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel [header]="'Red Flag Events' | localize">
            <redFlagEvents #redFlagEvents></redFlagEvents>
        </p-tabPanel>
        <!--<p-tabPanel [header]="'Information Exchange' | localize">
            <informationExchange #informationExchange></informationExchange>
        </p-tabPanel>-->


        <p-tabPanel [header]="'Additional Statistics' | localize">
            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatYearlist" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.additionalStatictisbservable | async">
                                <ng-template pTemplate="header">
                                    
                                    <tr>
                                        <th [style.width]="'65%'">{{ "AddStatFileDeclarations" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatLocalOwnership" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatLORelevantActivity" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatTaxResidence" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatTaxResidenceRelevantActivity" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatPercentageLocalOwnership" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatPercentageTaxResidence" | localize }}</th>
                                        <!-- From Ticket SysAid # 69078:  Remove the column called "Total Number Of Full-time Qualified Employees Performing The CIGA For Relevant Activities" for all the years from the "Additional Statistics" dashboard  -->
                                        <!-- <th [style.width]="'75%'">{{ "AddStatEmployeesCIGA" | localize }}</th> -->
                                        <th [style.width]="'75%'">{{ "AddStatClaimNonResidence" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatClaimProvisionalTreatment" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatProvidedEvidence" | localize }}</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>{{item.numOfHighRiskEntitiesCount}}</td>
                                        <!--
                                            not implemented yet.  Waiting for Client's validation
                                        <td>{{item.numOfLocalOwnerShips}}</td>
                                        <td>{{item.numOfPercentageOfEntitiesCarrying}}</td>-->
                                        <td>{{"N/A" | localize}}</td>
                                        <td>{{"N/A" | localize}}</td>
                                        <td>{{item.numOfEntitiesClainmingTaxResidenceCount}}</td>
                                        <td>{{item.numOfPercentageOfEntitiesClainmingTaxResidenceCount}}</td>
                                       <!--  not implemented yet.  Waiting for Client's validation 
                                        <td>{{item.percentageTextOfEntitiesCarryingOnRelevantActivityForLocalOwnerShip}}</td>-->
                                        <td>{{"N/A" | localize}}</td>
                                        <td>{{item.percentageTextOfTaxResidenceAbroad}}</td>
                                        <!-- From Ticket SysAid # 69078:  Remove the column called "Total Number Of Full-time Qualified Employees Performing The CIGA For Relevant Activities" for all the years from the "Additional Statistics" dashboard  -->
                                        <!-- <td>{{item.totalCIGAEmployees}}</td> -->
                                        <td>{{item.numberOfEntitiesNonResidence}}</td>
                                        <td>{{item.numberOfProvisionalTreatment}}</td>
                                        <td>{{item.numberOfEntitiesProvidedEvidence}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                </div>
            </div>
            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatCarryinglist" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.additionalStatictisbservable | async">
                                <ng-template pTemplate="header">
                                    
                                    <tr>
                                        <th [style.width]="'75%'">{{ "AddStatBanking" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatDistribution" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFinance" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatFund" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatHeadQuarters" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHolding" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatInsurance" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatIntellectual" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatShipping" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatTotalCarry" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatTotalRelevant" | localize }}</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>{{item.bankingCountOne}}</td>
                                        <td>{{item.distrubutionCount}}</td>
                                        <td>{{item.financeCount}}</td>
                                        <td>{{item.fundManagementCount}}</td>
                                        <td>{{item.headQuartersCount}}</td>
                                        <td>{{item.holdingCount}}</td>
                                        <td>{{item.insuranceCountTwo}}</td>
                                        <td>{{item.ipCount}}</td>
                                        <td>{{item.shippingCount}}</td>
                                        <td>{{item.totalCarryingOnActivityUnderEachCategory}}</td>
                                        <td>{{item.noRelevantActivity}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                </div>
            </div>
            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatExpenditurelist" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.businessCategoriesObservable | async">
                                <ng-template pTemplate="header">
                                    
                                    <tr>
                                        <th [style.width]="'120%'">{{ "AddStatTitleExpenditure" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatBanking" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatDistribution" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFinance" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFund" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHeadQuarters" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHolding" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatInsurance" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatIntellectual" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatShipping" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatTotalExpenditure" | localize }}</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>{{item.title}}</td>
                                        <td>{{item.bankingBusinessCount}}</td>
                                        <td>{{item.distrubutionBusinessCount}}</td>
                                        <td>{{item.financeBusinessCount}}</td>
                                        <td>{{item.fundBusinessCount}}</td>
                                        <td>{{item.headQuartersCount}}</td>
                                        <td>{{item.holdingBusinessDetailCount}}</td>
                                        <td>{{item.insuranceBusinessCount}}</td>
                                        <td>{{item.intellectualBusinessCount}}</td>
                                        <td>{{item.shippingBusinessCount}}</td>
                                        <td>{{item.totalOfBusinesses}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                </div>
            </div>
            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatEmployeelist" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.employeesObservable | async">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th [style.width]="'120%'">{{ "AddStatTitleEmployee" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatBanking" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatDistribution" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFinance" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFund" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHeadQuarters" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHolding" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatInsurance" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatIntellectual" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatShipping" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatTotalEmployee" | localize }}</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>{{item.title}}</td>
                                        <td>{{item.bankingBusinessCount}}</td>
                                        <td>{{item.distrubutionBusinessCount}}</td>
                                        <td>{{item.financeBusinessCount}}</td>
                                        <td>{{item.fundBusinessCount}}</td>
                                        <td>{{item.headQuartersCount}}</td>
                                        <td>{{item.holdingBusinessDetailCount}}</td>
                                        <td>{{item.insuranceBusinessCount}}</td>
                                        <td>{{item.intellectualBusinessCount}}</td>
                                        <td>{{item.shippingBusinessCount}}</td>
                                        <td>{{item.totalOfBusinesses}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                </div>
            </div>

            <!-- From Ticket SysAid # 69078: Remove the CIGA BREAKDOWN chart complement for all the years from "Additional Statistics" dashboard  -->
            <!-- <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatCIGAlist" | localize}}</p>
                        </p-header>
                        <div>
                            <p-table [value]="viewModel.cigaDetailsObservable | async">
                                <ng-template pTemplate="header">
                                    
                                    <tr>
                                        <th [style.width]="'120%'">{{ "AddStatTitleCIGA" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatBanking" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatDistribution" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFinance" | localize }}</th>
                                        <th [style.width]="'65%'">{{ "AddStatFund" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHeadQuarters" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatInsurance" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatShipping" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatIntellectual" | localize }}</th>
                                        <th [style.width]="'75%'">{{ "AddStatHolding" | localize }}</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item>
                                    <tr>
                                        <td>{{item.title}}</td>
                                        <td>{{item.bankingBusinessCIGACount}}</td>
                                        <td>{{item.distrubutionBusinessCIGACount}}</td>
                                        <td>{{item.financeBusinessCIGACount}}</td>
                                        <td>{{item.fundBusinessCIGACount}}</td>
                                        <td>{{item.headquartersBusinessBusinessCIGACount}}</td>
                                        <td>{{item.insuranceBusinessCIGACount}}</td>
                                        <td>{{item.shippingBusinessCIGACount}}</td>
                                        <td>{{item.intellectualBusinessCIGACount}}</td>
                                        <td>{{item.holdingBusinessDetailCIGACount}}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </p-card>
                </div>
            </div> -->

            <div class="col-flex">
                <div class="row-flex-space">
                    <p-card class="p-card-properties_dashboard width-100">
                        <p-header class="row-flex-space">
                            <p>{{"AddStatJurisdictionlist" | localize}}</p>
                        </p-header>
                        <p-table [value]="viewModel.additionalStatictisbservable | async">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th [style.width]="'120%'">{{"Name" | localize}}</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item>
                                <tr>
                                    <td>{{item.jurisdictionTaxResidentNames}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </p-card>
                    </div>
            </div>
        </p-tabPanel>
        <p-tabPanel [header]="'FHTPReport' | localize" *ngIf="viewModel.selectedYear >= 2019">
        <div class="col-flex">
            <p-card class="p-card-properties_dashboard width-100">               
                <p class="row-flex-space">{{"FHTPReportNotes" | localize}}</p>
                <br />               
                <div>
                    <button id="ExcelDownload" pButton type="submit" [label]="'FHTPReportDownloadBtn' | localize" (click)="excelDownload()" class="ui-button-rounded ui-button-warning margin-right-5"></button>            
                </div>
            </p-card>
         </div>
         </p-tabPanel>
    </p-tabView>
</div>
