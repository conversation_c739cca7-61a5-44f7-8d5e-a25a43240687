.ui-button-text {
    font-size: 13px;
}

.ess-um-grid,
.ess-um-details {
    padding: 1em;
}

.ess-um-grid {
    .ess-um-form-control {
        width: 150px;

        + .ess-um-form-control,
        + .ess-um-form-item {
            margin-left: 10px;
        }
    }
}

.ess-um-grid-container {
    margin-top: 10px;
    height: calc(100vh - 140px);
    overflow-y: auto;

    .ess-um-grid-button {
        font-size: 1.5em !important;
        cursor: pointer;
        line-height: 0.5;
        position: relative;
        top: 4px;

        &.pi-trash {
            color: red;
        }

        &.pi-unlock {
            color: #116fbf;
        }

        + .um-grid-button {
            margin-left: 5px;
        }
    }
}

.ess-um-details {
    background-color: #f3fafd;
}

.ess-um-edit-container {
    margin-top: 10px;
    width: 500px;
    height: calc(100vh - 130px);
    overflow-y: auto;
}

.ess-um-label {
    width: 30%;
    padding: 0.8rem 0;
}

.ess-um-input {
    width: 70%;
    margin: 5px auto;

    select {
        width: 100% !important;
        margin: 5px auto;
        font-size: 14px !important;
        padding: 4px 15px !important;
        background: #ececec;
        border: solid 2px #e1e1e1;
        border-radius: 0px;
    }
}

.ess-um-input-phone {
    padding: 5px 0;
}
