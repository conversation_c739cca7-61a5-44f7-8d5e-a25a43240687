<p-table [value]="sortedDeclarations">
    <ng-template pTemplate="header">
    <tr>
        <th>{{"Financial Year End" | localize}}</th>
        <!--<th>{{"Submitted By" | localize}}</th>-->
        <th>{{"Submission Time" | localize}}</th>
        <th>{{"Assessment Status" | localize}}</th>
        <th>{{"Action" | localize}}</th>
    </tr>
    </ng-template>
    <ng-template pTemplate="body" let-item>
        <tr>
            <td>{{item.fiscalEndDate | date: "yyyy"}}</td>
            <!--<td>{{item.submitterName}}</td>-->
        <td>{{item.submissionDateLocal | date: "dd/MM/yyyy HH:mm"}}</td>

            <td>{{getstatus(item)}}</td>
            <td>
                <div *ngIf="item.hasAnyDeclarationHistory">
                    <a id="caViewSubmission" (click)="viewSubmission(item)"><i class="pi pi-search"></i></a>
                    <a id="showESSHistory" (click)="showESSHistory(item)"><i class="pi pi-undo"></i></a>
                </div>
                <div *ngIf="!item.hasAnyDeclarationHistory">
                    <a id="caSearch" (click)="viewSubmission(item)"><i class="pi pi-search"></i></a>
                </div>
            </td>
        </tr>
    </ng-template>
</p-table>

<esHistoryModal #esHistoryModal></esHistoryModal>
