<div *ngIf="languages.length > 1" class="language-switch-area">
    <a id="changeLanguage" *ngFor="let language of languages" (click)="changeLanguage(language)">
        <span [ngClass]="{'language-icon-current': language.name==currentLanguage.name}"
              title="{{language.displayName}}">
            <i class="{{language.icon}}" [attr.aria-label]="language.displayName"></i>
        </span>
    </a>
</div>
