<div class="col-flex">
    <p-card [ngClass]="{'p-card-properties_form':!readOnlyMode,'p-card-properties_form_readonly':readOnlyMode}">
        <p-header>
            {{l('ESTaxResidance')}}
        </p-header>

        <form #step3="ngForm">
            <div class="row-flex-justified-rows">
                <div class="row-flex-unjustified">
                    <div *ngIf="!readOnlyMode" style="font-size:14px">
                        8a. Does the entity intend to make a claim of 
                        <label tooltip="Non Resident means resident for tax purposes in a jurisdiction outside the Virgin Islands which is not on annex 1 to the EU list of non co-operative jurisdictions for tax purposes.">&nbsp;<u>tax residency outside the Virgin Islands</u>&nbsp;</label>
                         under rule 2? 
                        <span class="required">*</span>
                    </div>
                    <div *ngIf="readOnlyMode" style="font-size:14px">
                        <div>{{l('ESTaxResidanceA')}}<span class="required">*</span></div>
                    </div>
                </div>
                <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.DoesEntityMakeClaimOutisedBVI }">
                    <div *ngIf="!readOnlyMode" class="radio-input">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="economicsubstance.doesEntityMakeClaimOutisedBVI" id="doesEntityMakeClaimOutisedBVIT" name="doesEntityMakeClaimOutisedBVI"
                            (onClick)="chkEntityClaimOutsideBVI($event)">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="economicsubstance.doesEntityMakeClaimOutisedBVI" id="doesEntityMakeClaimOutisedBVIF" name="doesEntityMakeClaimOutisedBVI"
                            (onClick)="chkEntityClaimOutsideBVI($event)">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">

                        <div *ngIf="!importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.doesEntityMakeClaimOutisedBVI | yesNo }} </label>
                        </div>

                        <div *ngIf="importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.doesEntityMakeClaimOutisedBVI | yesNo }} </label>
                        </div>

                    </div>
                    </div>
            </div>
            <div *ngIf="showHideConditions[0]">

                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont ">
                        <div>{{l('ESTaxResidanceB')}}<span class="required">*</span></div>
                    </div>
                    <div *ngIf="!readOnlyMode" class="row-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.JurisdictionTaxResident }">

                        <p-dropdown [options]="euListcountries"
                                    optionLabel="countryName" 
                                    [(ngModel)]="economicsubstance.jurisdictionTaxResident"
                                    id="JurisdictionTaxResidenid"
                                    name="JurisdictionTaxResidenname"
                                    (onChange)="SetjurisdictionId($event)">
                        </p-dropdown>
                    </div>
                    <div *ngIf="readOnlyMode" class="row-flex-justified ">
                            <label class="p-readonly-label">{{economicsubstance.jurisdictionTaxResident.countryName }} </label>
                    </div>
                    </div>                    
                <div *ngIf="showHideConditions[2]">
                   <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>{{l('ESTaxResidanceB1')}}<span class="required">*</span></div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.TaxPayerIdentificationNumber }">
                            <div *ngIf="!importOnlyMode">
                                <input pInputText type="text" class="form-control" maxlength="100" size="25" [(ngModel)]="economicsubstance.taxPayerIdentificationNumber" id="taxPayerIdentificationNumberId" name="taxPayerIdentificationNumber">
                            </div>

                            <div *ngIf="importOnlyMode">
                                <input pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.taxPayerIdentificationNumber" id="taxPayerIdentificationNumberIdImport" name="taxPayerIdentificationNumberImport">
                            </div>

                        </div>
                            <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                                <label class="p-readonly-label word-wrapping">{{economicsubstance.taxPayerIdentificationNumber}} </label>
                            </div>
                    </div> 
                </div> 
                <div *ngIf="showHideConditions[3]" class="row-flex-justified-rows">
                    <div  *ngIf="!readOnlyMode" class="row-flex-unjustified " style="font-size:14px">
                        8c. Does the entity have a
                        <label tooltip="Parent entity refers to a corporate and legal entity that:(a) holds, directly or indirectly, a beneficial interest in 75% or more of the shares in the subsidiary; or
                                        (b) holds, directly or indirectly, more than 75% of the voting rights in the subsidiary.">&nbsp;<u>parent entity?</u>&nbsp;<span class="required">*</span></label>
                    </div>
                    <div *ngIf="readOnlyMode" style="font-size:14px">
                        <div>{{l('ESTaxResidanceC')}}<span class="required">*</span></div>
                    </div>
                    <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.DoesEntityHaveParentEntity }">
                        <div *ngIf="!readOnlyMode" class="radio-input">
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.doesEntityHaveParentEntity" id="doesEntityHaveParentEntityT" name="doesEntityHaveParentEntity"
                                (onClick)="chkEntityHaveParent($event)"></p-radioButton>
                                <span>YES</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.doesEntityHaveParentEntity" id="doesEntityHaveParentEntityF" name="doesEntityHaveParentEntity"
                                (onClick)="chkEntityHaveParent($event)"></p-radioButton>
                                <span>NO</span>
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode">
                            <div *ngIf="!importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.doesEntityHaveParentEntity?'Yes':'No' }} </label>
                            </div>
                            <div *ngIf="importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.doesEntityHaveParentEntity!==null?(economicsubstance.doesEntityHaveParentEntity?'Yes':'No'):'' }} </label>
                            </div>
                        </div>
                    </div>

                </div>

                <div *ngIf="showHideConditions[1]">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>{{l('ESTaxResidanceC1')}}<span class="required">*</span></div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.ParentEntityName }">
                            <div *ngIf="!importOnlyMode">
                                <input pInputText type="text" class="form-control" maxlength="100" size="25" [style]="{'width':'50%'}" [(ngModel)]="economicsubstance.parentEntityName" id="parentEntityNameId" name="parentEntityName">
                            </div>

                            <div *ngIf="importOnlyMode">
                                <input pInputText type="text" class="form-control"  [style]="{'width':'50%'}" [(ngModel)]="economicsubstance.parentEntityName" id="parentEntityNameIdImport" name="parentEntityNameImport">
                            </div>

                        </div>
                            <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                                <label class="p-readonly-label word-wrapping">{{economicsubstance.parentEntityName}} </label>
                            </div>
                     </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>{{l('ESTaxResidanceC2')}}</div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.ParentEntityAlternativeName }">
                            <div *ngIf="!importOnlyMode">
                                <input pInputText type="text" class="form-control" maxlength="100" size="25" [(ngModel)]="economicsubstance.parentEntityAlternativeName" id="parentEntityAlternativeNameId" name="parentEntityAlternativeName">
                            </div>
                            <div *ngIf="importOnlyMode">
                                <input pInputText type="text" class="form-control"  [(ngModel)]="economicsubstance.parentEntityAlternativeName" id="parentEntityAlternativeNameIdImport" name="parentEntityAlternativeNameImport">
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                                <label class="p-readonly-label word-wrapping">{{economicsubstance.parentEntityAlternativeName}} </label>
                        </div>
                        </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>{{l('ESTaxResidanceC3')}}<span class="required">*</span></div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-justified margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.ParentJurisdiction }">
                            <p-dropdown [options]="listOfCountry"
                                        optionLabel="countryName" 
                                        [(ngModel)]="economicsubstance.parentJurisdiction"
                                        id="parentJurisdictionid"
                                        name="parentJurisdictionid"
                                        (onChange)="SetParentjurisdictionId($event)">
                            </p-dropdown>
                        </div>
                        <div *ngIf="readOnlyMode" class="row-flex-justified margin-left">
                            <label class="p-readonly-label">{{economicsubstance.parentJurisdiction.countryName }} </label>
                        </div>
                    </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>{{l('ESTaxResidanceC4')}}<span class="required">*</span></div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.EntityIncorporationNumber }">
                            <div *ngIf="!importOnlyMode">
                                <input pInputText type="text" class="form-control" maxlength="20" size="25" [(ngModel)]="economicsubstance.entityIncorporationNumber" id="entityIncorporationNumberId" name="entityIncorporationNumber">
                            </div>

                            <div *ngIf="importOnlyMode">
                                <input pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.entityIncorporationNumber" id="entityIncorporationNumberId" name="entityIncorporationNumber">
                            </div>

                        </div>
                        <div *ngIf="readOnlyMode" class="row-flex-justified margin-left">
                                <label class="p-readonly-label word-wrapping">{{economicsubstance.entityIncorporationNumber }} </label>
                        </div>
                        </div>

                </div>
                <div class="row-flex-justified-rows">
                    <div *ngIf="!readOnlyMode" class="row-flex-unjustified" style="font-size:14px">
                        8d. Evidence of 
                        <label tooltip="Non Resident means resident for tax purposes in a jurisdiction outside the Virgin Islands which is not an annex 1 to the EU list of non co-operative jurisdictions for tax purpose">&nbsp;<u>non-residency</u>&nbsp;</label>
                        or provisional treatment:<span class="required">*</span>
                    </div>
                    <div *ngIf="readOnlyMode" class="row-flex-unjustified" style="font-size:14px">
                        <div>{{l('ESTaxResidanceD')}}<span class="required">*</span></div>
                    </div>
                    <div class="row-flex-justified">
                        <div *ngIf="!readOnlyMode" class="radio-input-col"
                             [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.IsEvidenceNonResidenceOrTreatment }">
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.isEvidenceNonResidenceOrTreatment" name="selectUploadEvidence" id="selectUploadEvidenceidT">
                                </p-radioButton>
                                <span style="font-size:14px">{{l('ESTaxResidanceD1')}}</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.isEvidenceNonResidenceOrTreatment" name="selectUploadEvidence" id="selectUploadEvidenceidF">
                                </p-radioButton>
                                <span style="font-size:14px">{{l('ESTaxResidanceD2')}}</span>
                            </div>
                        </div>

                        <div *ngIf="readOnlyMode">
                            <div *ngIf="!importOnlyMode">
                                <label *ngIf="economicsubstance.isEvidenceNonResidenceOrTreatment" class="p-readonly-label" style="font-size:14px">{{l('ESTaxResidanceD1')}} </label>
                                <label *ngIf="!economicsubstance.isEvidenceNonResidenceOrTreatment" class="p-readonly-label" style="font-size:14px">{{l('ESTaxResidanceD2')}} </label>
                            </div>
                            <div *ngIf="importOnlyMode">
                                <label *ngIf="economicsubstance.isEvidenceNonResidenceOrTreatment!==null && economicsubstance.isEvidenceNonResidenceOrTreatment" class="p-readonly-label" style="font-size:14px">{{l('ESTaxResidanceD1')}} </label>
                                <label *ngIf="economicsubstance.isEvidenceNonResidenceOrTreatment!==null && !economicsubstance.isEvidenceNonResidenceOrTreatment" class="p-readonly-label" style="font-size:14px">{{l('ESTaxResidanceD2')}} </label>
                            </div>
                        </div>
                        

                    </div>
                </div>

                <div *ngIf="!readOnlyMode">
                    <a>Upload evidence files</a>
                </div>
                <div [ngClass]="{ 'input-error-box': _economicSubstanceService.step3Error.ResidanceDocument }">
                    <div *ngIf="showUpload(1)">
                        <app-upload-files
                       [Documents]="economicsubstance.evidenceOfNonResidanceDocument"
                       [DocumentTypeName]="NonResidencyDoc"
                       [IsEconomicDocument]="true"
                       [readOnlyMode]="readOnlyMode"
                       [displayFromCa] ="displayFromCa || historyDisplay"
                       [ctspId]="ctspId">

                        </app-upload-files>
                    </div>
                    <div *ngIf="showUpload(2)">
                        <app-upload-files
                        [Documents]="economicsubstance.evidenceProvisionalTreatmentDocuments"
                        [DocumentTypeName]="TreatmentDoc" [IsEconomicDocument]="true"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa || historyDisplay"
                        [ctspId]="ctspId">

                        </app-upload-files>
                    </div>
                </div>
            </div>

</form>
    </p-card>
</div>
