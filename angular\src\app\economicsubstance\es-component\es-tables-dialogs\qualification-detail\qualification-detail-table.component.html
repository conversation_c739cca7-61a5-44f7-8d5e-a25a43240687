<div class="row-flex-justified-largefont">
<div>9{{headingSeq}}.{{sequenceNo}}c. {{l("EmployeeC")}} 9{{headingSeq}}.{{sequenceNo}}b.<span *ngIf="showHideCondition" class="required">*</span></div>
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="model.show(null,true,importOnlyMode)">Add row</a>
    </div>
</div>
<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="details">
    <ng-template pTemplate="header">
        <tr>
            <th style="width:25%" class="table-text-header">Name</th>
            <th style="width:30%" class="table-text-header">Qualification</th>
            <th style="width:25%" class="table-text-header">Years of relevant experience</th>
            <th *ngIf="!readOnlyMode" style="width:20%" class="table-text-header">Action</th>

        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
            <td style="width:25%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping"> {{source.name}} </label>
            </td>
            <td style="width:30%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.qualification}} </label>
            </td>
            <td style="width:25%" *ngIf="!importOnlyMode">
                    <label [ngClass]="{'p-readonly-label':readOnlyMode}">  {{source.yearRelevantExperience}} </label>
            </td>
            <td style="width:25%" *ngIf="importOnlyMode">
                    <label [ngClass]="{'p-readonly-label':readOnlyMode}">  {{source.yearRelevantExperienceString}} </label>
            </td>
            

            <td style="width:20%" *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="model.show(source,false)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-qualification-detail-dialog #model (submitted)="updateSource($event)"></app-qualification-detail-dialog>
