<div *ngIf="esEntityDetail"
    [ngClass]="{'col-flex p-card-properties_form':!readOnlyMode,'col-flex p-card-properties_form_readonly':readOnlyMode}">
    <form #entityDetails="ngForm">
        <p-card [ngClass]="{'p-card-properties_form':!readOnlyMode,'p-card-properties_form_readonly':readOnlyMode}">
            <p-header>
                {{l('ESEntityDetailsHeader')}}
            </p-header>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont">
                    <div>{{l('EntityTaxNumber')}}</div>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width" [ngClass]="{ 'input-warnning-box': _economicSubstanceService.entityDetailsError.IdentificationNumber }">
                    <div *ngIf="!importOnlyMode">
                        <input pInputText type="text" class="form-control" maxlength="100" size="25"
                            [style]="{'width':'50%'}" [(ngModel)]="esEntityDetail.identificationNumber"
                            id="identificationNumberId" name="identificationNumberName">
                    </div>

                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [style]="{'width':'50%'}"
                            [(ngModel)]="esEntityDetail.identificationNumber" id="identificationNumberIdImport"
                            name="identificationNumberNameImport">
                    </div>

                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width">
                    <label class="p-readonly-label word-wrapping">{{esEntityDetail.identificationNumber}}
                    </label>
                </div>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont">
                    <div>{{l('EntityGrossTotalAnnualIncome')}}</div>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width"
                     [ngClass]="{
                        'input-warnning-box': _economicSubstanceService.entityDetailsError.TotalGrossAnnualIncome,
                        'input-error-box': _economicSubstanceService.entityDetailsError.TotalGrossAnnualIncomeE    }">
                    <p-dropdown [options]="currency" optionLabel="currencyCode"
                                (onChange)="setCurrencyId($event)"
                                [(ngModel)]="economicsubstance.currency" class="width-95" id="currencyId" name="currencyName">
                    </p-dropdown>

                    <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="esEntityDetail.totalGrossAnnualIncome" (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="totalGrossAnnualIncomeId" name="totalGrossAnnualIncomeName"></p-spinner>

                    <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="esEntityDetail.totalGrossAnnualIncomeString" id="totalGrossAnnualIncomeId" name="totalGrossAnnualIncomeName">
                </div>

                <div *ngIf="readOnlyMode"
                     [ngClass]="{
                         'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !== 'USD',
                         'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                         'row-flex-justified-width_100':!displayFromCa,
                         'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                    <div class="col-flex-width20 ">
                        <label class="p-readonly-label">{{ economicsubstance.currency ? economicsubstance.currency.currencyCode : 'USD' }}</label>
                    </div>
                    <div *ngIf="!importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{ esEntityDetail.totalGrossAnnualIncome }}</label>
                        </div>
                    </div>

                    <div *ngIf="importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{ esEntityDetail.totalGrossAnnualIncomeString }}</label>
                        </div>
                    </div>

                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                        <label class="p-readonly-label">USD</label>
                    </div>
                    <div *ngIf="displayFromCa &&economicsubstance.currency &&  economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                        <label class="p-readonly-label">{{ getTotalInUSD(esEntityDetail.totalGrossAnnualIncome) }}</label>
                    </div>
                </div>
            </div>

            <div class="row-flex-justified-largefont">
                <div>{{l('BusinessAddressSameRegisteredAddress')}} <span class="required">*</span></div>
            </div>
            <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.IsSameAsRegisteredAddress }">
                <div *ngIf="!readOnlyMode" class="radio-input">
                    <div>
                        <p-radioButton name="isSameAsRegisteredAddress" [value]="true"
                            [(ngModel)]="esEntityDetail.isSameAsRegisteredAddress" id="isSameAsRegisteredAddressT"
                            (onClick)="setBusinessAddress($event)">
                        </p-radioButton>
                        <span>YES</span>
                    </div>
                    <div>
                        <p-radioButton name="isSameAsRegisteredAddress" [value]="false"
                            [(ngModel)]="esEntityDetail.isSameAsRegisteredAddress" id="isSameAsRegisteredAddressf"
                            (onClick)="setBusinessAddress($event)">
                        </p-radioButton>
                        <span>NO</span>
                    </div>
                </div>
                <div *ngIf="readOnlyMode">
                    <div *ngIf="!importOnlyMode">
                        <label class="p-readonly-label">{{esEntityDetail.isSameAsRegisteredAddress | yesNo  }} </label>
                    </div>

                    <div *ngIf="importOnlyMode">
                        <label class="p-readonly-label">{{ esEntityDetail.isSameAsRegisteredAddressString | yesNo }} </label>
                    </div>
                </div>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <div>{{l('ESEntityDetailBusinessAddress')}} <span class="required">*</span></div>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.AddressLine1 }">
                    <div *ngIf="!importOnlyMode">
                        <input pInputText type="text" class="form-control" maxlength="100" size="25"
                            [(ngModel)]="esEntityDetail.addressLine1" id="addressLine1Id" name="addressLine1Name"
                            [disabled]="esEntityDetail.isSameAsRegisteredAddress"
                            placeholder="AddressLine1">
                    </div>

                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [(ngModel)]="esEntityDetail.addressLine1"
                        [disabled]="esEntityDetail.isSameAsRegisteredAddress"
                            id="addressLine1IdImport" name="addressLine1NameImport" placeholder="AddressLine1">
                    </div>
                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                    <label class="p-readonly-label word-wrapping">{{esEntityDetail.addressLine1}}
                    </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left">
                    <div *ngIf="!importOnlyMode">
                        <input pInputText type="text" class="form-control" maxlength="100" size="25"
                            [(ngModel)]="esEntityDetail.addressLine2" id="addressLine2Id" name="addressLine2Name"
                            [disabled]="esEntityDetail.isSameAsRegisteredAddress"
                            placeholder="AddressLine2">
                    </div>

                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [(ngModel)]="esEntityDetail.addressLine2"
                        [disabled]="esEntityDetail.isSameAsRegisteredAddress"
                        id="addressLine2IdImport" name="addressLine2NameImport" placeholder="AddressLine2">
                    </div>
                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                    <label class="p-readonly-label word-wrapping">{{esEntityDetail.addressLine2}}
                    </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.Country }">
                    <p-dropdown [options]="listOfCountry" optionLabel="countryName" [(ngModel)]="esEntityDetail.country"
                        id="BusinessAddressCountryid" name="BusinessAddressCountryname"
                        [disabled]="esEntityDetail.isSameAsRegisteredAddress"
                        (onChange)="setBusinessAddressId($event)">
                    </p-dropdown>
                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">
                    <label class="p-readonly-label">{{ esEntityDetail.country?.countryName }}
                    </label>
                </div>
            </div>

            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont">
                    <div>{{l('ESEntityMNEB2')}}</div>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width" [ngClass]="{ 'input-warnning-box': _economicSubstanceService.entityDetailsError.MNEGroupName }">
                    <div *ngIf="!importOnlyMode">
                        <input pInputText type="text" class="form-control mmn-text-box-length" maxlength="100" size="25" [(ngModel)]="esEntityDetail.mneGroupName" id="mneGroupNameId" name="mneGroupName">
                    </div>

                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control mmn-text-box-length" [(ngModel)]="esEntityDetail.mneGroupName" id="mneGroupNameIdImport" name="mneGroupNameImport">
                    </div>

                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width">
                    <label class="p-readonly-label word-wrapping">{{esEntityDetail.mneGroupName}} </label>
                </div>
            </div>

            <div class="row-flex-justified-rows">
                <div *ngIf="!readOnlyMode" class="row-flex-unjustified " style="font-size:14px">
                     {{l('ESUltimateParentC')}} <span class="required">*</span>
                </div>
                <div *ngIf="readOnlyMode" style="font-size:14px">
                    <div>{{l('ESUltimateParentC')}} <span class="required">*</span></div>
                </div>
                <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.DoesEntityHaveUltimateParent }">
                    <div *ngIf="!readOnlyMode" class="radio-input">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="esEntityDetail.doesEntityHaveUltimateParent"
                                id="doesEntityHaveUltimateParentT" name="doesEntityHaveUltimateParentEntity">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="esEntityDetail.doesEntityHaveUltimateParent"
                                id="doesEntityHaveUltimateParentF" name="doesEntityHaveUltimateParentEntity">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <div *ngIf="!importOnlyMode">
                            <label class="p-readonly-label">{{esEntityDetail.doesEntityHaveUltimateParent | yesNo  }}
                            </label>
                        </div>
                        <div *ngIf="importOnlyMode">
                            <label class="p-readonly-label">{{esEntityDetail.doesEntityHaveUltimateParentString | yesNo }} </label>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="showHideResult(0)">
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.UltimateParent }">
                        <app-entity-detail-table [details]="esEntityDetail.esEntityAdditionalDetails"
                                                        [headingSeq]="'j'" [sequenceNo]="1"
                                                        [entityTypeName]="ultimateParentEntityType"
                                                        [listOfCountry]="listOfCountry"
                                                        [readOnlyMode]="readOnlyMode"
                                                        [importOnlyMode]="importOnlyMode"></app-entity-detail-table>
                    </div>
                </div>
            </div>


            <div class="row-flex-justified-rows">
                <div *ngIf="!readOnlyMode" class="row-flex-unjustified " style="font-size:14px">
                    {{l('ESImmediateParentC')}} <span class="required">*</span>
                </div>
                <div *ngIf="readOnlyMode" style="font-size:14px">
                    <div>{{l('ESImmediateParentC')}} <span class="required">*</span></div>
                </div>
                <div class="row-flex-justified" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.DoesEntityHaveImmediateParent }">
                    <div *ngIf="!readOnlyMode" class="radio-input">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="esEntityDetail.doesEntityHaveImmediateParent"
                                id="doesEntityHaveImmediateParentT" name="doesEntityHaveImmediateParentEntity">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="esEntityDetail.doesEntityHaveImmediateParent"
                                id="doesEntityHaveImmediateParentF" name="doesEntityHaveImmediateParentEntity">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <div *ngIf="!importOnlyMode">
                            <label class="p-readonly-label">{{esEntityDetail.doesEntityHaveImmediateParent | yesNo }}
                            </label>
                        </div>
                        <div *ngIf="importOnlyMode">
                            <label class="p-readonly-label">{{esEntityDetail.doesEntityHaveImmediateParentString | yesNo  }} </label>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="showHideResult(1)">
                 <div class="row-flex-justified-rows">
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': _economicSubstanceService.entityDetailsError.ImmediateParent }">
                        <app-entity-detail-table [details]="esEntityDetail.esEntityAdditionalDetails"
                                                        [headingSeq]="'k'" [sequenceNo]="1"
                                                        [entityTypeName]="immediateParentEntityType"
                                                        [listOfCountry]="listOfCountry"
                                                        [readOnlyMode]="readOnlyMode"
                                                        [importOnlyMode]="importOnlyMode"></app-entity-detail-table>
                    </div>
                </div>
            </div>

        </p-card>

    </form>
</div>
