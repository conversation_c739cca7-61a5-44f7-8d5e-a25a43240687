import { Component, OnInit, Injector, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ModalDirective } from 'ngx-bootstrap';
import { Location } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router'

import { ESActionStatus } from '@app/economicsubstance/EconomicSubstance'
@Component({
    selector: 'declarationComp',
    templateUrl: './declaration-disclaimer.component.html',
    styleUrls: ['./declaration-disclaimer.component.less']
})
export class DeclarationComponent extends AppComponentBase implements OnInit
{
    entityId: string;
    constructor(private router: Router, private route: ActivatedRoute,
        private location: Location, injector: Injector)
    {
               
        super(injector);


        this.route.params.subscribe(x =>
        {

             this.entityId = x.entityid;
        })
         


    }

    ngOnInit()
    {
        this.topbarService.clearValues();
        this.topbarService.notifyTitle(this.l('Economic Substance Declaration'));
        
      //
    }
    handleCancel() {
        this.location.back();
    }

    startes()
    {
        this.router.navigate(['app/economicsubstance/startes/' + this.entityId + '/' + ESActionStatus.Add])
    
    }
}
