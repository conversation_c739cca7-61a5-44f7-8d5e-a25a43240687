
    <div *ngIf="readytoDisplay" class="declaration-form-container es-import-container">
        <div class="es-header-title">
            <p class="ess-title">{{l('ECONOMIC SUBSTANCE IMPORTED DECLARATION')}}</p>
        </div>

        <div class="es-header-container">
            <p-toast></p-toast>
            <div class="row-flex-end margin-top-wide">

                <button pButton type="button" icon="pi pi-pencil" [disabled]="isEdit || disableIfImported"
                        class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left" (click)="onEdit()"
                        tooltip="Edit"></button>
                <button pButton type="button" icon="pi pi-print"
                        class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left"     (click)="onPrint()" tooltip="Print"></button>
                <button pButton type="button" [disabled]="disableIfImported" icon="pi pi-trash"
                        class="ui-button-rounded ui-button-secondary margin-left bigger-icons" (click)="onDelete()" iconPos="left" tooltip="Delete"></button>

                <button pButton type="button" icon="pi pi-times"
                         class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left" (click)="onClose()" tooltip="Close"> </button>

                <button pButton type="button" [disabled]="disableIfAnyError || disableIfImported" class="ui-button-rounded ui-button-warning margin-left" (click)="onSaveDraft()" label="Save as draft"></button>
                <button pButton type="button" [disabled]="disableIfAnyError || disableIfImported" class="ui-button-rounded ui-button-warning margin-left" (click)="onSubmitForReview()" label="Submit"></button>


            </div>
        </div>


        <div class="es-header es-header-spacing">
            <div class="es-header-item-small">
                <h5>{{l("ESCE1")}}</h5>
                <p class="word-wrapping">{{corporateEntity.name}}</p>
            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE1b")}}</h5>
                <p class="word-wrapping">{{corporateEntity.clientNumber}}</p>
            </div>
            <div class="es-header-item-medsmall">
                <h5>{{l("ESCE2")}}</h5>
                <p>{{corporateEntity |entityIncoperationFormation}}</p>
            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE3")}}</h5>
                <p>{{corporateEntity.incorporationDate | date: "dd/MM/yyyy"}}</p>
            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE4")}}</h5>
                <p>{{ corporateEntity.entityType | entityDescription}}</p>
            </div>
            <div class="es-header-item-med">
                <h5>{{l("ESCE5")}}</h5>
                <div>
                    {{corporateEntity.listedOnStockExchange?'Yes':'No'}}
                </div>

            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE5A")}}</h5>
                <p class="word-wrapping">  {{corporateEntity.stockExchange}}</p>

            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE6")}}</h5>
                <p class="word-wrapping">  {{corporateEntity.statusText}}</p>
    
            </div>
            <div class="es-header-item-small">
                <h5>{{l("ESCE6D")}}</h5>
                <p class="word-wrapping"> {{corporateEntity?.addressLine1 || '' }} {{ corporateEntity?.addressLine2 || '' }} {{ corporateEntity?.countryName || '' }}</p>    
            </div>
        </div>

        <p-card *ngIf="_economicSubstanceService.errorList && _economicSubstanceService.errorList.length > 0 "
                class="p-card-error">
            <p-header>
                <div>
                    <i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Validation Errors")}}</label>
                </div>
            </p-header>
            <div class="col-flex">
                <div *ngFor="let error of _economicSubstanceService.errorList">
                    <p>{{error}}</p>
                </div>
            </div>
        </p-card>
        <p-card *ngIf="_economicSubstanceService.warningList && _economicSubstanceService.warningList.length > 0 " class="p-card-warning">
            <p-header>

                <div><i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Warnings")}}</label></div>
            </p-header>
            <div class="col-flex">
                <div *ngFor="let warning of _economicSubstanceService.warningList">
                    <p>{{warning}}</p>
                </div>
            </div>
        </p-card>





        <app-step1-es [economicsubstance]="currentEconomicSubstance" [readOnlyMode]="true"
                      [localFiscalStartDate]="localFiscalStartDate" [localFiscalEndDate]="localFiscalEndDate"
                      [displayFromCa]="false">
        </app-step1-es>



        <app-es-entity-details *ngIf="showHideEntityDetails" [economicsubstance]="currentEconomicSubstance"  
                          [esEntityDetail]="esEntityDetail"
                          [corporateEntity]="corporateEntity"
                          [readOnlyMode]="true"
                          [importOnlyMode]="true"
                          [displayFromCa]="false">

            </app-es-entity-details>


        <app-step2-es [economicsubstance]="currentEconomicSubstance"
                      [selectedActivity]="selectedActivity"
                      [readOnlyMode]="true"
                      [importOnlyMode]="true"
                      [displayFromCa]="false">

        </app-step2-es>


        <div *ngIf="CheckIfNonIsSelected()">
            <app-step3-es [economicsubstance]="currentEconomicSubstance"
                          [readOnlyMode]="true"
                          [displayFromCa]="false"
                          [importOnlyMode]="true"
                          [ctspId]="ctspId"></app-step3-es>
        </div>
        <app-step4-es [economicsubstance]="currentEconomicSubstance"
                      [corporateEntity]="corporateEntity"
                      [readOnlyMode]="true"
                      [displayFromCa]="false"
                      [importOnlyMode]="true"
                      [ctspId]="ctspId"
                      ></app-step4-es>


        <app-step5-es [economicsubstance]="currentEconomicSubstance"
                      [readOnlyMode]="true"
                      [displayFromCa]="false"
                      [ctspId]="ctspId"> </app-step5-es>


    </div>
        <!--<app-main-economics-reo [corporateEntity]="corporateEntity"
                            [currentEconomicSubstance]="currentEconomicSubstance"
                            [displayHeader]="true"
                            [displayFromCa]="false">
    </app-main-economics-reo>-->



    

