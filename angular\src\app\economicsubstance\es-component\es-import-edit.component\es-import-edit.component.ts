import { Location } from '@angular/common';
import { Component, HostListener, Injector, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ESActionStatus, ESRouting, EntityDescription, SubmittedLocation } from '@app/economicsubstance/EconomicSubstance';
import { EconomicSubstanceService, EsAction } from '@app/economicsubstance/economicsubstance.service';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { ComponentCanDeactivate } from '@app/shared/common/auth/auth-route-guard';
import { AppConsts } from '@shared/AppConsts';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    CorporateEntityDto, CorporateEntityServiceProxy, EconomicSubstanceDeclarationDto, EconomicSubstanceServiceProxy, RelevantActivity,
    EconomicSubstanceStepDto, LookUpMainServiceProxy, RelevantActivityDetailDto, ValidationResultMainDto, CIGALookUpDto,
    AuditEventServiceProxy, FileImportAuditDto, SingleFieldInputOfGuid, AddressDto, CountryDto, CurrencyDto, EconomicSubstanceStatus, EntityType,
    ValidationResultDto, FileUploadPairValue, FileUploadServiceProxy, ValidationType, UploadedDeclarationListDto, EsEntityDetailDto,

    UploadedDeclarationDto, UploadedDeclarationStatus
} from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { MessageService } from 'primeng/api';
import { forkJoin, Observable } from 'rxjs';
import { Address } from 'cluster';
import * as moment from 'moment';
import { PrintService } from '@shared/services/print.service';

@Component({
    selector: 'app-es-import-edit',
    templateUrl: './es-import-edit.component.html',
    styleUrls: ['./es-import-edit.component.less']
})
export class ESImportEditComponent extends AppComponentBase implements OnInit {
    economicSubstanceStepDto: EconomicSubstanceStepDto = new EconomicSubstanceStepDto();
    currentEconomicSubstance: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();


    corporateEntity: CorporateEntityDto;
    selectedActivity: RelevantActivityDetailDto[] = [];

    corporateEntityAddress: AddressDto;

    currencies: CurrencyDto[];
    countries: CountryDto[];
    euListcountries: CountryDto[];
    cigaLookup: CIGALookUpDto[];
    headerValues: any;
    licensedUnderAct: any;
    fiscalyearResult: any;
    registeredAddress: any;

    localFiscalStartDate: any;
    localFiscalEndDate: any;

    country: CountryDto;
    head1: string;
    head2: string;
    id: any;

    activeIndex: number = 0;
    newStep: number = 0;
    disableButtons: boolean = false;
    disableNoError: boolean = true;
    disableIfImported: boolean = false;

    importobject: UploadedDeclarationDto = new UploadedDeclarationDto();
    public esEntityDetail: EsEntityDetailDto;

    steps = [
        { number: 1, label: "Financial Period" },
        { number: 2, label: "Entity Details" },
        { number: 3, label: "Relevant Activity" },
        { number: 4, label: "Tax Residency" },
        { number: 5, label: "Activity Details" },
        { number: 6, label: "Supporting Details" },
        { number: 7, label: "Confirmation" },
    ];


    constructor(injector: Injector,
        private router: Router, private location: Location, private route: ActivatedRoute,
        private _lookUpMainServiceServiceProxy: LookUpMainServiceProxy,
        private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy,
        private _sharedComponet: SharedComponent,
        public _economicSubstanceService: EconomicSubstanceService,
        private _auditEventService: AuditEventServiceProxy,
        private _fileUploadServiceProxy: FileUploadServiceProxy

    ) {
        super(injector);
        this.route.params.subscribe(x => {

            this.id = x.id;
        });

        this.callTheMainServices(this.id);
    }

    ngOnInit() {
        this._economicSubstanceService.initializeEmptyError();

    }

    // perform close 
    onClose() {

        this.performAction(EsAction.Exit);
    }

    onDelete() {
        this.performAction(EsAction.Discard);
    }


    CallValidateForAllES(index: number) {
        this._economicSubstanceService.initializeEmptyError();
        this._economicSubstanceServiceProxy.allStepValidation(this.currentEconomicSubstance).subscribe((data: ValidationResultMainDto[]) => {
            if (data) {

                if (this.notifyGUIWithErroOnStep[index]) this.notifyGUIWithErroOnStep[index](data);
                this.checkErrorAndSave(data, index);
                this.disableNoError = false;
                data.forEach((validation: ValidationResultMainDto) => {
                    validation.validationResultDto.forEach((validation: ValidationResultDto) => {
                        if (validation.validationType == ValidationType.Error) {
                            this.disableNoError = true;
                            this._economicSubstanceService.errorList.push(validation.validationString);

                        }
                        else {
                            this._economicSubstanceService.warningList.push(validation.validationString);

                        }

                    });
                });
            }
        },
            err => {
                console.error(err);
            });

    }

    private checkIfErrorExistsInStep: { [index: number]: (data: ValidationResultMainDto[], validationType: ValidationType) => boolean } = {
        [0]: (data: ValidationResultMainDto[], validationType: ValidationType) => (data.filter(x => x.activityName === "Step1" && x.validationResultDto.find(y => y.validationType === validationType)).length > 0),
        [1]: (data: ValidationResultMainDto[], validationType: ValidationType) => (data.filter(x => x.activityName === "EntityDetails" && x.validationResultDto.find(y => y.validationType === validationType)).length > 0),
        [2]: (data: ValidationResultMainDto[], validationType: ValidationType) => (data.filter(x => x.activityName === "Step2" && x.validationResultDto.find(y => y.validationType === validationType)).length > 0),
        [3]: (data: ValidationResultMainDto[], validationType: ValidationType) => (data.filter(x => x.activityName === "Step3" && x.validationResultDto.find(y => y.validationType === validationType)).length > 0),
        [4]: (data: ValidationResultMainDto[], validationType: ValidationType) => {
            let listOfName = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked).map(x => x.relevantActivityName);
            return (data.filter(x => listOfName.includes(x.activityName) && x.validationResultDto.find(y => y.validationType === validationType)).length > 0)
        },
        [5]: (data: ValidationResultMainDto[], validationType: ValidationType) => (data.filter(x => x.activityName === "Step5" && x.validationResultDto.find(y => y.validationType === validationType)).length > 0),
    };

    private notifyGUIWithErroOnStep: { [index: number]: (data: ValidationResultMainDto[]) => void } = {
        [0]: (data: ValidationResultMainDto[]) => {
            data.forEach((validation: ValidationResultMainDto) => {
                if (validation.activityName == "Step1") {
                    let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                    if (backendValidations != null) {
                        backendValidations.forEach((validation: ValidationResultDto) => {
                            if (!this._economicSubstanceService.step1Error[validation.fieldName]) {
                                this._economicSubstanceService.step1Error[validation.fieldName] = true;
                            }
                        })
                    }
                }
            })
        },
        [1]: (data: ValidationResultMainDto[]) => {
            data.forEach((validation: ValidationResultMainDto) => {
                if (validation.activityName == "EntityDetails") {
                    let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                    if (backendValidations != null) {
                        backendValidations.forEach((validation: ValidationResultDto) => {
                            if (!this._economicSubstanceService.entityDetailsError[validation.fieldName]) {
                                this._economicSubstanceService.entityDetailsError[validation.fieldName] = true;
                            }
                        })
                    }
                }
            });
        },
        [2]: (data: ValidationResultMainDto[]) => {
            data.forEach((validation: ValidationResultMainDto) => {
                if (validation.activityName == "Step2") {
                    let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                    if (backendValidations != null) {
                        backendValidations.forEach((validation: ValidationResultDto) => {
                            if (!this._economicSubstanceService.step2Error[validation.fieldName]) {
                                this._economicSubstanceService.step2Error[validation.fieldName] = true;
                            }
                        })
                    }
                }
            });
        },
        [3]: (data: ValidationResultMainDto[]) => {
            data.forEach((validation: ValidationResultMainDto) => {
                if (validation.activityName == "Step3") {
                    let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                    if (backendValidations != null) {
                        backendValidations.forEach((validation: ValidationResultDto) => {
                            if (!this._economicSubstanceService.step3Error[validation.fieldName]) {
                                this._economicSubstanceService.step3Error[validation.fieldName] = true;
                            }
                        })
                    }
                }
            });
        },
        [4]: (data: ValidationResultMainDto[]) => {
            this.currentEconomicSubstance.relevantActivities.forEach(x => {
                data.forEach((validation: any) => {
                    if (validation.headerSeq && validation.validationResultDto && validation.activityName === x.relevantActivityName) {
                        let headingSeq = validation.headerSeq;
                        let activityError = this._economicSubstanceService.step4Error[headingSeq];
                        validation.validationResultDto.forEach((v: any) => {
                            if (activityError && !activityError[v.fieldName]) {
                                this._economicSubstanceService.step4Error[headingSeq][v.fieldName] = true;

                            }
                        })
                    }

                })
            });
        },
        [5]: (data: ValidationResultMainDto[]) => {
            data.forEach((validation: ValidationResultMainDto) => {
                if (validation.activityName == "Step5") {
                    let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                    if (backendValidations != null) {
                        backendValidations.forEach((validation: ValidationResultDto) => {
                            if (!this._economicSubstanceService.step5Error[validation.fieldName]) {
                                this._economicSubstanceService.step5Error[validation.fieldName] = true;
                            }
                        })
                    }
                }
            });
        },
    };

    //call the services
    callTheMainServices(id: any) {
        this.corporateEntity = new CorporateEntityDto();

        this.corporateEntityAddress = new AddressDto();

        this.esEntityDetail = new EsEntityDetailDto();
        // update ES

        forkJoin([
            // difference
            this._fileUploadServiceProxy.getDeclarationDetailsById(this.id),
            this._lookUpMainServiceServiceProxy.getAllCountries(undefined, undefined),
            this._lookUpMainServiceServiceProxy.geTCIGALookUp(0),
            this._lookUpMainServiceServiceProxy.getAllCurrencies(undefined),
            this._lookUpMainServiceServiceProxy.getEUCountryList(undefined)
        ]).subscribe(responseList => {
            this.importobject = responseList[0];
            this.currentEconomicSubstance = responseList[0].fileUploadValue.parsedValue;

            this.currentEconomicSubstance.uploadedDeclarationId = this.id;

            this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
            this.corporateEntity = responseList[0].fileUploadValue.parsedValue.corporateEntity;
            this.countries = this._sharedComponet.sort_country(responseList[1]);
            this.country = this.countries.find(x => x.countryCode == 'VGB');

            this.cigaLookup = responseList[2];
            this.currencies = this._sharedComponet.sort_currency(responseList[3]);
            this.currentEconomicSubstance.defaultEndDateString = this.currentEconomicSubstance.defaultEndDateString;
            this.currentEconomicSubstance.defaultStartDateString = this.currentEconomicSubstance.defaultStartDateString;
            this.euListcountries = this._sharedComponet.sort_country(responseList[4], false);
            this.localFiscalStartDate = _.cloneDeep(this.currentEconomicSubstance.fiscalStartDateString);
            this.localFiscalEndDate = _.cloneDeep(this.currentEconomicSubstance.fiscalEndDateString);

            if (this.currentEconomicSubstance.esEntityDetails)
                this.esEntityDetail = this.currentEconomicSubstance.esEntityDetails.find(e => typeof e !== 'undefined');

            if (!this.esEntityDetail) {
                this.esEntityDetail = new EsEntityDetailDto();
            }

            if (this.importobject.uploadStatus !== UploadedDeclarationStatus.WithErrors
                && this.importobject.uploadStatus !== UploadedDeclarationStatus.WithWarnings

                && this.importobject.uploadStatus !== UploadedDeclarationStatus.Pending) {
                this.disableIfImported = true;
            }
            else {
                this.CallValidateForAllES(0);
                this.CreateAnyNonDefinedProperty()
            }

        });
    }




    getDeclarationFormStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'max-height': 'initial',
            };
        }

        return {};
    }

    CreateAnyNonDefinedProperty() {
        if (!this.currentEconomicSubstance.evidenceOfNonResidanceDocument)
            this.currentEconomicSubstance.evidenceOfNonResidanceDocument = [];


        if (!this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments)
            this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments = [];


        if (!this.currentEconomicSubstance.supportingDocument)
            this.currentEconomicSubstance.supportingDocument = [];



    }

    onSaveDraft() {
        this.performAction(EsAction.Save);
    }

    onNextStep() {
        this.performAction(EsAction.Next);
    }

    onPreviousStep() {
        this.performAction(EsAction.Previous);
    }

    onDiscardDraft() {

        this.performAction(EsAction.Discard);
    }
    onEdit() {
        this.performAction(EsAction.Edit);
    }
    onSubmitForReview() {

        this.performAction(EsAction.Submit);
    }
    enablestep4() { return this.activeIndex == 6; }

    IsDiscardEnabled(): boolean {
        if (this.currentEconomicSubstance.id) return true;
        return false;
    }


    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }

    determinNextStep(currentIndex): number {

        if (currentIndex === 0) {
            if (moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day')) return 1;
            else currentIndex = 1;
        }

        if (currentIndex === 2) {
            // first condition if the current index ==1 and the user select none then jump to step 5
            let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);
            if (object.length > 0) return 5;
        }

        if (currentIndex === 3) {
            if (!this.IsNullorUndefined(this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) && this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) return 5;
        }


        return currentIndex + 1
    }

    determinPreviousStep(currentIndex): number {

        if (currentIndex === 2) {
            if (moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day')) return 1;
            else currentIndex = 1;
        }

        if (currentIndex === 5) {
            let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);
            if (object.length > 0) return 1;
            if (!this.IsNullorUndefined(this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) && this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) return 3;
        }

        return currentIndex - 1
    }

    performAction(action: EsAction) {
        this.disableButtons = true;
        this.economicSubstanceStepDto.stepLevel = this.activeIndex;
        //this.economicSubstanceStepDto.onStore = false;
        this.economicSubstanceStepDto.economicSubstanceDeclarationDto = this.currentEconomicSubstance;

        // clear and push object
        this.economicSubstanceStepDto.economicSubstanceDeclarationDto.esEntityDetails = [];
        this.economicSubstanceStepDto.economicSubstanceDeclarationDto.esEntityDetails.push(this.esEntityDetail);

        if (action === EsAction.Submit) {
            let self = this;
            // ask for confirmation before submitting
            abp.message.confirm(
                AppConsts.messageList.EsSubmitConfirmation,
                'Are you sure?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        self.saveESDraft(EconomicSubstanceStatus.Submitted);
                    }
                }
            );
        }


        if (action === EsAction.Edit) {
            this.newStep = 0;
            this.updateStep();
        }
        // On Save Draft click
        if (action === EsAction.Save) {
            let self = this;

            abp.message.confirm(
                AppConsts.messageList.EsDarftConfirmation,
                'Are you sure?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        self.saveESDraft(EconomicSubstanceStatus.Draft);
                    }
                }
            );

        }


        // on Next button click
        if (action === EsAction.Next) {
            this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
            this.newStep = this.determinNextStep(this.activeIndex);
            this.CallValidateForAllES(this.activeIndex);
        }

        // on Previous button click
        if (action === EsAction.Previous) {
            this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
            this.newStep = this.determinPreviousStep(this.activeIndex);
            this.updateStep();
        }


        // on Discard Draft
        if (action === EsAction.Discard) {
            let self = this;
            let items = [];
            items.push(this.importobject);
            if (items) {
                abp.message.confirm(
                    AppConsts.messageList.ESRemoveImportedDeclaration,
                    'Are you sure?',
                    function (isConfirmed) {
                        if (isConfirmed) {
                            self._fileUploadServiceProxy.selectedImportedItemsToRemove(items)
                                .subscribe(() => {
                                    self.router.navigate(['app/main/filetriage/' + self.importobject.uploadedFileId]);

                                });
                        }
                        else {

                            self.disableButtons = false;
                        }
                    }
                );


            }

        }


        // on close and exit
        if (action === EsAction.Exit) {
            this.router.navigate(['app/main/filetriage/' + this.importobject.uploadedFileId]);

        }

    }



    checkErrorAndSave(data: ValidationResultMainDto[], index: number, needTochangeStep: boolean = true) {

        let isErrorInStep = this.checkIfErrorExistsInStep[index] ? this.checkIfErrorExistsInStep[index](data, ValidationType.Error) : false;
        if (!isErrorInStep) {
            // need to check for the warnning
            let isWarningInStep = this.checkIfErrorExistsInStep[index] ? this.checkIfErrorExistsInStep[index](data, ValidationType.Warning) : false;
            if (!isWarningInStep) {
                // Perform current step related operations

                // this.saveESDraft(EconomicSubstanceStatus.Draft, needTochangeStep);
                this.updateStep(needTochangeStep, data);
            }
            else {

                this.disableButtons = false;
                if (!this._economicSubstanceService.warningFirst) {
                    this._economicSubstanceService.warningFirst = true;
                    this.updateStep(needTochangeStep, data);
                    //    this.saveESDraft(EconomicSubstanceStatus.Draft, needTochangeStep);
                }
                else {
                    this._economicSubstanceService.warningFirst = false;
                }
            }
        }
        else {
            // need to scroll to top for user to see validations
            window.scroll(0, 0);
            console.log("Validation error")
            this.disableButtons = false;
        }
    }


    updateStep(needTochangeStep: boolean = true, data?: any) {
        if (needTochangeStep && this.newStep > -1 && this.newStep < 7) {
            this.activeIndex = this.newStep;

            if (data) {
                if (this.notifyGUIWithErroOnStep[this.newStep]) this.notifyGUIWithErroOnStep[this.newStep](data);
            }
        }

        this.disableButtons = false;
    }



    ClearOldData() {
        if (!this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {
            this.currentEconomicSubstance.jurisdictionTaxResident = null;
            this.currentEconomicSubstance.jurisdictionTaxResidentId = null;
            this.currentEconomicSubstance.doesEntityHaveParentEntity = null;
            this.currentEconomicSubstance.parentEntityName = null;
            this.currentEconomicSubstance.parentEntityAlternativeName = null;
            this.currentEconomicSubstance.parentJurisdiction = null;
            this.currentEconomicSubstance.parentJurisdictionId = null;
            this.currentEconomicSubstance.entityIncorporationNumber = null;
            this.currentEconomicSubstance.isEvidenceNonResidenceOrTreatment = null;
            this.currentEconomicSubstance.taxPayerIdentificationNumber = null;

            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments) {
                this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments.forEach(x => { x.isDeleted = true; })
            }
        }
        else {
            if (!this.currentEconomicSubstance.doesEntityHaveParentEntity) {
                this.currentEconomicSubstance.parentEntityName = null;
                this.currentEconomicSubstance.parentEntityAlternativeName = null;
                this.currentEconomicSubstance.parentJurisdiction = null;
                this.currentEconomicSubstance.parentJurisdictionId = null;
                this.currentEconomicSubstance.entityIncorporationNumber = null;
            }
        }
        let activities = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
        activities.forEach(x => {
            if (!x.isCarriedForPartFinancialPeriod) { x.startDateString = null; x.endDateString = null; x.startDate = null, x.endDate = null; }
            if (this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {

                x.isActivityDirectedInBVI = null;
                x.noofConductedMeetingString = null;
                x.noofMeetingHeldInBVIString = null;

                x.isMeetingMinutesInBVI = null;
                x.totalTurnoverString = null;

                x.totalExpeditureString = null;
                x.totalExpeditureInBVIString = null;
                x.totalNoFullTimeEmployeeString = null;
                x.totalFullTimeEmployeeInBVIString = null;

                x.cigaOtherDetail = null;
                x.hasAnyIncomeBeenOutsourced = null;
                x.wasCIGAOutsourcedInBVI = null;
                x.totalExpenditureIncurredInBVI = null;


                x.doesEntityComplyItsStatutoryObligations = null;
                x.doesEntityManageEquity = null;
                x.doesEntityHaveAdequateEmployee = null;

                // intelect
                x.isLegalEntityHighRisk = null;
                x.doesEntityProvideEvidence = null;
                x.totalExpenditureIncurredInBVIString = null;
                x.grossIncomeRoyalitiesString = null;

                x.totalGrossAnnualIncomeString = null;
                x.grossIncomeGainsString = null;
                x.doesLegalEntityConductCIGA = null;
                x.doesEntityProvideEvidenceroRebut = null;
                x.doesBusinessRequireEquipment = null;
                x.equipmentInJurisdiction = null;
                x.equipmentDescription = null;

                if (x.conductedCIGAActivity) x.conductedCIGAActivity.forEach(x => x.isDeleted = true);
                if (x.premisesAddress) x.premisesAddress.forEach(x => x.isDeleted = true);
                if (x.serviceProviders) x.serviceProviders.forEach(x => x.isDeleted = true);
                if (x.managementDetails) x.managementDetails.forEach(x => x.isDeleted = true);
                if (x.attendMeetingDetails) x.attendMeetingDetails.forEach(x => x.isDeleted = true);
                if (x.employeeQualificationDetails) x.managementDetails.forEach(x => x.isDeleted = true);
                if (x.otherCIGADocuments) x.otherCIGADocuments.forEach(x => x.isDeleted = true);
                if (x.highRiskDocuments) x.highRiskDocuments.forEach(x => x.isDeleted = true);

                //BVI Declaration Changes 2.0                
                x.grossIncomeType = null;
                x.totalAssetsValue = null;
                x.netAssetsValue = null;
                x.noofQuorumBoardMeeting = null;
                x.isQuorumBoardMeetingInBVI = null;
                x.totalNoCorporateLegalEmployee = null;
                x.tangibleAsset = null;
                x.tangibleAssetIncome = null;
                x.tangibleAssetEmployeeResponsibility = null;
                x.historyofStrategicDecisionsInBVI = null;
                x.historyofTradingActivityIncome = null;
                x.relevantIPAsset = null;
                x.ipAssetsInBVI = null;
                x.ipAssetsEmployeeResponsibility = null;
                x.concreteEvidenceDecisionInBVI = null;
                x.grossIncomeOthersString = null;

                if (x.tangibleAssetIncomeDocuments) x.tangibleAssetIncomeDocuments.forEach(x => x.isDeleted = true);
                if (x.tangibleAssetEmployeeResponsibilityDocuments) x.tangibleAssetEmployeeResponsibilityDocuments.forEach(x => x.isDeleted = true);
                if (x.historyofStrategicDecisionsInBVIDocuments) x.historyofStrategicDecisionsInBVIDocuments.forEach(x => x.isDeleted = true);
                if (x.historyofTradingActivityIncomeDocuments) x.historyofTradingActivityIncomeDocuments.forEach(x => x.isDeleted = true);
                if (x.ipAssetsInBVIDocuments) x.ipAssetsInBVIDocuments.forEach(x => x.isDeleted = true);
                if (x.ipAssetsEmployeeResponsibilityDocuments) x.ipAssetsEmployeeResponsibilityDocuments.forEach(x => x.isDeleted = true);
                if (x.concreteEvidenceDecisionInBVIDocuments) x.concreteEvidenceDecisionInBVIDocuments.forEach(x => x.isDeleted = true);

            }

        });

        //BVI Declaration Changes 2.0
        this.currentEconomicSubstance.esEntityDetails = [];
    }

    getIncoperationFormatationNumber(formatationNumber: any, companyNumber: any): string {
        if (formatationNumber) return formatationNumber;
        if (companyNumber) return companyNumber;
        return "";
    }




    saveESDraft(status: EconomicSubstanceStatus) {

        this.ClearOldData();

        let fileImportAudit = new FileImportAuditDto();
        fileImportAudit.corporateEntityId = this.currentEconomicSubstance.corporateEntityId;
        fileImportAudit.entityName = this.currentEconomicSubstance.corporateEntity.name;
        fileImportAudit.entityUniqueID = this.currentEconomicSubstance.corporateEntity.clientNumber;
        fileImportAudit.incorFormationNumber = this.getIncoperationFormatationNumber(this.currentEconomicSubstance.corporateEntity.formationNumber, this.currentEconomicSubstance.corporateEntity.companyNumber);
        fileImportAudit.fiscalStartDate = this.currentEconomicSubstance.fiscalStartDateString;
        fileImportAudit.fiscalEndDate = this.currentEconomicSubstance.fiscalEndDateString;
        fileImportAudit.fileName = this.importobject.fileName;
        if (status === EconomicSubstanceStatus.Draft)
            this._auditEventService.auditImportDraftEntityViewEvent(fileImportAudit).subscribe();
        if (status === EconomicSubstanceStatus.Submitted)
            this._auditEventService.auditImportSubmitEntityViewEvent(fileImportAudit).subscribe();


        this.currentEconomicSubstance.status = status;
        this.currentEconomicSubstance.localSubmissionDate = moment();
        this.currentEconomicSubstance.submissionDate = moment.utc();
        this.currentEconomicSubstance.esEntityDetails.push(this.esEntityDetail);
        this._economicSubstanceServiceProxy.importEconomicSubstance(this.currentEconomicSubstance).subscribe((data: any) => {

            if (data) {

                this.importobject.fiscalEndDateString = data.fiscalEndDateString;
                this.importobject.fiscalStartDateString = data.fiscalStartDateString;
                this.importobject.uploadStatus = UploadedDeclarationStatus.Imported;
                this.importobject.fileUploadValue = null;
                this._fileUploadServiceProxy.updateUploadDeclarationStatus(this.importobject).subscribe((dataAny: any) => { });

                this.disableButtons = true;
                this.disableNoError = true;
                this.disableIfImported = true;
                if (status === EconomicSubstanceStatus.Submitted)
                    this.router.navigate(['app/economicsubstance/display/' + data.id + '/' + SubmittedLocation.FromImport + '/' + this.importobject.uploadedFileId])
                else {

                    this.router.navigate(['app/economicsubstance/edit/' + data.id + '/' + this.importobject.uploadedFileId + '/' + ESActionStatus.Import])
                }


            }

        });

    }



}
