import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DeclarationComponent } from './es-component/declaration-disclaimer.component/declaration-disclaimer.component';
import { MainEconomicsComponent } from './es-component/main-economics.component/main-economics.component'
import { DocumentPreviewDialogComponent } from './es-component/document-preview-dialog/document-preview-dialog.component';
import { DisplaySubmittedComponent } from './es-component/display-submitted.component/display-submitted.component';
import { EsCaReviewComponent } from './es-component/es-ca-review.component/es-ca-review.component';
import { CaReviewComponent } from './es-component/ca-review.component/ca-review.component';
import { PendingChangesGuard } from '@app/shared/common/auth/auth-route-guard';
import { ESImportComponent } from './es-component/es-import.component/es-import.component';
import { ESImportEditComponent } from './es-component/es-import-edit.component/es-import-edit.component';
import { DisplayHistoryComponent } from './es-component/display-history.component/display-history.component';
@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                
                children: [
                    { path: 'add/:entityid', component: DeclarationComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'startes/:entityid/:action', component: MainEconomicsComponent, canDeactivate: [PendingChangesGuard], data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'edit/:entityid/:action', component: MainEconomicsComponent, canDeactivate: [PendingChangesGuard], data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'import/:id', component: ESImportComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'edit/:entityid/:uploadid/:action', component: MainEconomicsComponent, canDeactivate: [PendingChangesGuard], data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'document-preview/:id', component: DocumentPreviewDialogComponent, data: { permissions: ['Ctsp.EconomicSubstance', 'Ca.Assessment'] } },
                    { path: 'display/:economicsubid/:action/:uploadid', component: DisplaySubmittedComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'display/:economicsubid/:action', component: DisplaySubmittedComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'careview/:economicsubid/:ctspid', component: EsCaReviewComponent, canDeactivate: [PendingChangesGuard], data: { permissions: ['Ca.Entity'] } },
                    { path: 'editimport/:id', component: ESImportEditComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create'] } },
                    { path: 'displayhistory/:id/:ctspid', component: DisplayHistoryComponent, data: { permissions: ['Ctsp.EconomicSubstance.Create', 'Ca.Entity'] } },

                ]
            }
        ])
    ],
    exports: [
        RouterModule
    ]
})
export class EconomicSubstanceRoutingModule { }
