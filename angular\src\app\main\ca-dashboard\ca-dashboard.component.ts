import { Component, OnInit, ViewChild, ViewEncapsulation, Injector } from "@angular/core";
import {
    CADashboardServiceProxy,
    RedFlagReportServiceProxy,
    CASearchServiceServiceProxy,
    CaDashboardSummaryDto,
    CaDashboardCtspOverviewDto,
    CaDashboardRelaventActivityOverviewDto,
    CaAdvSearchInput,
    SearchableFieldId,
    SearchMethod,
    NoInput,
    FHTPServiceProxy,
} from "@shared/service-proxies/service-proxies";
import { CaDashboardViewModel } from "../viewmodel/ca-dashboard-viewmodel";
import { RedFlagEventsComponent } from "../red-flag-events/red-flag-events.component";
import { Router, ActivatedRoute } from "@angular/router";
import * as moment from "moment";
import {
    getRandomKey,
    getSearchCriteria,
    getResidentJurisdictionSearchCriteria,
    getNumEntitiesSearchCriteria,
    getFilingsSubmittedSearchCriteria,
    getAssessmentCompletedSearchCriteria,
    getAssessmentFailedSearchCriteria,
    getAssessmentStartedSearchCriteria,
    getFillingsOverdueSearchCriteria,
    getFillingsOverdueNotStruckOffSearchCriteria,
    downloadFile,
    setNewTabForSearch,
    setSearchValueToStorageNewTab,
} from "../viewmodel/utils";
import ChartDataLabels from "chartjs-plugin-datalabels";
import Chart from "chart.js";
import { AppComponentBase } from "@shared/common/app-component-base";
import { FileDownloadService } from "@shared/utils/file-download.service";

@Component({
    encapsulation: ViewEncapsulation.None,
    selector: "app-ca-dashboard",
    templateUrl: "./ca-dashboard.component.html",
    styleUrls: ["./ca-dashboard.component.css"],
})
export class CaDashboardComponent extends AppComponentBase implements OnInit {
    @ViewChild("redFlagEvents", { static: true })
    redFlagEvents: RedFlagEventsComponent;

    viewModel: CaDashboardViewModel;

    tabIndex: number = 0;
    activeIndex: number = 0;
    options: any;
    barChartoptions: any;
    chartJs = Chart;
    chartLabelPlugin = ChartDataLabels;
    pageSize: number = 10;

    constructor(
        injector: Injector,
        dashboardService: CADashboardServiceProxy,
        redFlagServiceProxy: RedFlagReportServiceProxy,
        private _redFlagReportServiceProxy: RedFlagReportServiceProxy,
        private _fileDownloadService: FileDownloadService,
        searchService: CASearchServiceServiceProxy,
        private _router: Router,
        private _route: ActivatedRoute,
        private _fhtpServiceProxy: FHTPServiceProxy
    ) {
        super(injector);
        let params: any;
        this._route.queryParams.subscribe((x) => {
            params = x;
        });

        let year: number | null = null;
        if (params.year) {
            year = +params.year;
        }

        let weekStart: moment.Moment | null = null;
        if (params.week) {
            weekStart = moment(params.week);
        }
        this.viewModel = new CaDashboardViewModel(
            dashboardService,
            redFlagServiceProxy,
            searchService,
            year,
            weekStart
        );
        this.viewModel.onRefresh.subscribe((x) =>
            this.redFlagEvents.refresh(x)
        );
        this.barChartoptions = {
            responsive: true,
            layout: {
                padding: {
                    bottom: 20,
                }
            },
            plugins: {
                datalabels: {
                    align: "end",
                    anchor: "end",
                    offset: 0,
                    color: "gray",
                    font: {
                        size: 10,
                    },
                },
            },
        };
        this.options = {
            responsive: true,
            //display labels on data elements in graph
            plugins: {
                datalabels: {
                    align: "end",
                    anchor: "end",
                    offset: 0,
                    // backgroundColor: 'white',
                    // borderColor: 'gray',
                    // borderWidth: 1,
                    // borderRadius: 4,
                    color: "gray",
                    font: {
                        size: 10,
                    },
                },
            },
            layout: {
                padding: {
                    right: 20,
                },
            }
        };
        this._route.params.subscribe((x) => {
            if (!x.defaultTab) {
                return;
            }
            if (x.defaultTab.toUpperCase() == "REDFLAGS") {
                this.activeIndex = 1;
                this.tabIndex = 1;
            } else if (x.defaultTab.toUpperCase() == "NONRESIDENT") {
                this.activeIndex = 2;
            }
        });
    }

    ngOnInit() { }

    handleTabChange(e: any): void {
        this.tabIndex = e.index;
    }
    onPageSizeChange(event: any) {
        this.pageSize = event;
    }
    goToSettings(e: any): void {
        if (this.tabIndex == 1) {
            this._router.navigate(["app/admin/redFlags/"]);
        }
    }

    excelDownload(): void {
        this._fhtpServiceProxy.getFHTPExcelFileFromBlob(this.viewModel.selectedYear, null, null).subscribe(x => {
            if (x.content != null && x.fileName != null) {
                downloadFile(x.content, x.fileName)
            }
        });
    }

    showSummaryOverdue(item: CaDashboardSummaryDto) {
        this.viewModel.updateTimeObservable.subscribe((x) => {
            this._router.navigate(["../caentitysearch"], {
                relativeTo: this._route,
                queryParams: {
                    type: "overdue",
                    year: this.viewModel.selectedYear,
                    updatetime: moment(x).format("YYYY-MM-DD"),
                },
            });
        });
    }
    setSearchValueToStorage(value: any) {
        if (typeof Storage !== "undefined") {
            if (value) {
                localStorage.setItem(
                    "advancedSearchCriteria",
                    JSON.stringify(value)
                );
                return value;
            }
        }
    }
    drilldownNavigate(criteria: CaAdvSearchInput) {
        criteria.randomKey = getRandomKey();
        let obj = JSON.parse(JSON.stringify(criteria));
        if (criteria["isForNewTab"] && criteria["tabId"]) {
            this.openNewTab(criteria);
            return;
        }
        this.setSearchValueToStorage(obj);
        let queryParams = { type: "ESS", isAdvancedSearch: true };
        const queryParamsFiltered = Object.keys(queryParams).reduce(
            (acc, key) =>
                queryParams[key] != null && queryParams[key] !== false
                    ? { ...acc, [key]: queryParams[key] }
                    : acc,
            {}
        );
        this._router.navigate(["../casearchresult"], {
            relativeTo: this._route,
            queryParams: queryParamsFiltered,
        });
    }
    getSummaryOverdueLink(item: CaDashboardSummaryDto) {
        item.fillingOverdueSearchCriteria = setNewTabForSearch(item.fillingOverdueSearchCriteria);
        this.drilldownNavigate(item.fillingOverdueSearchCriteria);
    }
    getAssessmentPendingMoreThan6Years(item: CaDashboardSummaryDto) {
        item.assessmentPendingMoreThan6YearsSearchCriteria = setNewTabForSearch(item.assessmentPendingMoreThan6YearsSearchCriteria);
        this.drilldownNavigate(item.assessmentPendingMoreThan6YearsSearchCriteria);
    }
    getRelevantActivities(item: CaDashboardRelaventActivityOverviewDto) {
        item.searchCriteria = setNewTabForSearch(item.searchCriteria);
        this.drilldownNavigate(item.searchCriteria);
    }

    getCtspOverdueLink(item: CaDashboardCtspOverviewDto) {
        item.fillingsOverdueSearchCriteria = setNewTabForSearch(item.fillingsOverdueSearchCriteria);
        this.drilldownNavigate(item.fillingsOverdueSearchCriteria);
    }

    getCtspNotStartedLink(item: CaDashboardCtspOverviewDto) {
        item.startedAssessmentSearchCriteria = setNewTabForSearch(item.startedAssessmentSearchCriteria);
        this.drilldownNavigate(item.startedAssessmentSearchCriteria);
    }
    getCtspClosedLink(item: CaDashboardCtspOverviewDto) {
        item.closedAssessmentSearchCriteria = setNewTabForSearch(item.closedAssessmentSearchCriteria);
        this.drilldownNavigate(item.closedAssessmentSearchCriteria);
    }
    getCtspCompletedLink(item: CaDashboardCtspOverviewDto) {
        item.completedAssessmentSearchCriteria = setNewTabForSearch(item.completedAssessmentSearchCriteria);
        this.drilldownNavigate(item.completedAssessmentSearchCriteria);
    }

    getCtspOverdueNotStruckOffLink(item: CaDashboardCtspOverviewDto) {
        let searchCriteria: any = null;
        searchCriteria = getFillingsOverdueNotStruckOffSearchCriteria(this.viewModel.selectedYear);
        searchCriteria.ctspIds = [item.id];
        searchCriteria.financialPeriodEndYearForLess = null;
        searchCriteria.includeRequiringDeclaration = null;
        searchCriteria.isSearch = false;
        searchCriteria.maxResultCount = 10;
        searchCriteria.skipCount = 0;
        searchCriteria.sorting = null;
        if (searchCriteria) {
            searchCriteria = setNewTabForSearch(searchCriteria);
            this.drilldownNavigate(searchCriteria);
        }
    }

    selectChartData(event: any) {
        let model = event.element._model;
        if (model) {
            let searchCriteria = getSearchCriteria(
                model,
                this.viewModel.selectedYear - CaDashboardViewModel.limitedYear
            );
            if (model.label == 'Other') {
                // If it is, do nothing
                return;
            }
            if (searchCriteria) {
                searchCriteria = setNewTabForSearch(searchCriteria);
                this.drilldownNavigate(searchCriteria);
            }
        }
    }

    searchByTaxResident(event: any) {
        let model = event.element._model;
        if (model && model.label) {
            let searchCriteria = getResidentJurisdictionSearchCriteria(
                model,
                this.viewModel.selectedYear
            );
            if (model.label == 'Other') {
                // If it is, do nothing
                return;
            }
            if (searchCriteria) {
                searchCriteria = setNewTabForSearch(searchCriteria);
                this.drilldownNavigate(searchCriteria);
            }
        }
    }

    searchEntities(event: any) {
        const label = event.element._model.datasetLabel;
        const year = event.element._model.label; //this.viewModel.selectedYear;
        let searchCriteria: any = null;

        switch (label) {
            case "# of Entities":
                searchCriteria = getNumEntitiesSearchCriteria(year);
                break;
            case "# of Filings Submitted":
                searchCriteria = getFilingsSubmittedSearchCriteria(year);
                break;
            case "# of Filings Overdue":
                searchCriteria = getFillingsOverdueSearchCriteria(year);
                break;
            case "# of Filings Overdue (Not Struck Off)":
                searchCriteria = getFillingsOverdueNotStruckOffSearchCriteria(year);
                break;
            case "# of Assessments Started":
                searchCriteria = getAssessmentStartedSearchCriteria(year);
                break;
            case "# of Assessments Completed":
                searchCriteria = getAssessmentCompletedSearchCriteria(year);
                break;
            case "# of Assessments Failed":
                searchCriteria = getAssessmentFailedSearchCriteria(year);
                break;
            default:
                console.error(`Unknown label: ${label}`);
                break;
        }

        if (searchCriteria) {
            searchCriteria = setNewTabForSearch(searchCriteria);
            this.drilldownNavigate(searchCriteria);
        }
    }

    openNewTab(criteria: CaAdvSearchInput) {
        const obj = Object.assign({}, criteria);
        setSearchValueToStorageNewTab(obj);
        let queryParams = { type: "ESS", isAdvancedSearch: true, tabId: criteria["tabId"] };
        const queryParamsFiltered = Object.keys(queryParams).reduce(
            (acc, key) =>
                queryParams[key] != null && queryParams[key] !== false
                    ? { ...acc, [key]: queryParams[key] }
                    : acc,
            {}
        );
        const url = this._router.serializeUrl(this._router.createUrlTree(["../casearchresult"], {
            relativeTo: this._route,
            queryParams: queryParamsFiltered,
        }));
        window.open(url, '_blank');
    }

    static overviewDatasetLabelAndKeys = [
        { label: "# of Entities", key: "numEntities" },
        { label: "# of Filings Submitted", key: "numFilingsSubmitted" },
        { label: "# of Filings Overdue", key: "numFilingsOverdue" },
        {
            label: "# of Filings Overdue (Not Struck Off)",
            key: "numFilingsOverdueNotStruckOff",
        },
        { label: "# of Assessments Started", key: "numAssessmentsStarted" },
        { label: "# of Assessments Completed", key: "numAssessmentsCompleted" },
        { label: "# of Assessments Failed", key: "numAssessmentsFailed" },
    ];
}
