import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { accountModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AccountServiceProxy, PasswordComplexitySetting, ResetPasswordOutput, ResolveTenantIdInput } from '@shared/service-proxies/service-proxies';
import { Subscription, timer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../shared/utils/validation/ng-form-validation.component';
import { ResetPasswordModel } from './reset-password.model';

@Component({
    templateUrl: './reset-password.component.html',
    animations: [accountModuleAnimation()]
})
export class ResetPasswordComponent extends AppComponentBase implements OnInit {
    @ViewChild('resetPassFormValidation', { static: true }) passwordFormValidation: NgFormValidationComponent;
    @ViewChild('resetPassForm', { static: true }) passwordForm: FormGroup;

    model: ResetPasswordModel = new ResetPasswordModel();
    passwordComplexitySetting: PasswordComplexitySetting = new PasswordComplexitySetting();
    saving = false;

    remainingSeconds: number = 0;
    displayMinutes: number = 0;
    displaySeconds: number = 0;
    timerSubscription: Subscription;

    constructor(
        injector: Injector,
        private _accountService: AccountServiceProxy,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {
        super(injector);
    }

    ngOnInit(): void {
        this.passwordFormValidation.formGroup = this.passwordForm;

        if (this._activatedRoute.snapshot.queryParams['c']) {
            this.model.c = this._activatedRoute.snapshot.queryParams['c'];

            this._accountService.resolveTenantId(new ResolveTenantIdInput({ c: this.model.c })).subscribe((tenantId) => {
                this.appSession.changeTenantIfNeeded(
                    tenantId
                );
            });
        } else {
            this.model.userId = this._activatedRoute.snapshot.queryParams['userId'];
            this.model.resetCode = this._activatedRoute.snapshot.queryParams['resetCode'];

            this.appSession.changeTenantIfNeeded(
                this.parseTenantId(
                    this._activatedRoute.snapshot.queryParams['tenantId']
                )
            );
        }
    }

    ngOnDestroy() {
        if (this.timerSubscription) {
            this.timerSubscription.unsubscribe();
        }
    }

    hasError(fieldName: string): boolean {
        return this.passwordFormValidation.fieldHasErrors(fieldName);
    }

    save(): void {
        if (!this.passwordFormValidation.isFormValid()) {
            return;
        }

        this.saving = true;
        this._accountService.resetPassword(this.model)
            .pipe(finalize(() => { this.saving = false; }))
            .subscribe((result: ResetPasswordOutput) => {
                if (result.requiresTwoFactor) {
                    this.model.hasTwoFactorCode = true;
                    this.remainingSeconds = this.twoFactorExpirySeconds;

                    const timerSource = timer(0, 1000);
                    this.timerSubscription = timerSource.subscribe(() => {
                        this.remainingSeconds = this.remainingSeconds - 1;

                        this.displayMinutes = Math.floor(Math.round(this.remainingSeconds / 60 * 100) / 100);
                        this.displaySeconds = this.remainingSeconds - this.displayMinutes * 60;

                        if (this.remainingSeconds === 0) {
                            this.message.warn(this.l('TimeoutPleaseTryAgain')).then(() => {
                                this.model.hasTwoFactorCode = false;
                                this.timerSubscription.unsubscribe();
                            });
                        }
                    });
                } else {
                    this._router.navigate(['account/login']);
                }
            });
    }

    parseTenantId(tenantIdAsStr?: string): number {
        let tenantId = !tenantIdAsStr ? undefined : parseInt(tenantIdAsStr);
        if (tenantId === NaN) {
            tenantId = undefined;
        }

        return tenantId;
    }

    passwordMatch(password, passwordMatch): boolean {
        return !password
            || !passwordMatch
            || password == passwordMatch;
    }
}
