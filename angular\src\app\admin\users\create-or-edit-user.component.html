<div class="ess-um-details col-flex">
    <div class="row-flex-space">
        <p *ngIf="!user.idInMaster" class="ess-title">{{l("AddUser")}}</p>
        <p *ngIf="user.idInMaster" class="ess-title">{{l("EditUser")}}</p>
        <!--<div class="row-flex-space" *ngIf="user.idInMaster !== null">
            <button pButton type="button" (click)="changeHistory()" class="margin-left" icon="pi pi-undo"></button>
        </div>-->
    </div>

    <div class="col-flex ess-um-edit-container">
        <hr />
        <ngFormValidation #userFormValidation displayMode="2"></ngFormValidation>

        <form #userForm="ngForm">
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="EmailAddress">{{"EmailAddress" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('EmailAddress') }">
                    <input id="EmailAddress" #emailAddressInput="ngModel" type="email" [disabled]="!user.isActive"
                           name="EmailAddress" class="form-control" [(ngModel)]="user.emailAddress" required maxlength="512" email
                           (change)="onUpdateEmail()" />
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="Username">{{"Username" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('Username') }">
                    <input id="Username" #usernameInput="ngModel" type="text" [disabled]="user.idInMaster"
                           name="Username" class="form-control" [(ngModel)]="user.userName" required maxlength="64"
                           (change)="onUpdateUsername()">
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="FirstName">{{"FirstName" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('FirstName') }">
                    <input id="FirstName" #firstNameInput="ngModel" class="form-control" type="text" [disabled]="!user.isActive" name="FirstName" [(ngModel)]="user.firstName" required maxlength="256">
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="LastName">{{"LastName" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('LastName') }">
                    <input id="LastName" #lastNameInput="ngModel" type="text" [disabled]="!user.isActive" name="LastName" class="form-control" [(ngModel)]="user.lastName" required maxlength="256">
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="PhoneNumber">{{"Business Phone" | localize}} *</label>
                </div>
                <div class="ess-um-input ess-um-input-phone" [ngClass]="{ 'input-error': hasError('PhoneNumber') }">
                    <ngx-intl-tel-input id="phoneNumber"
                                        #phoneNumberInput
                                        [(ngModel)]="phoneNumber"
                                        [enableAutoCountrySelect]="true"
                                        [enablePlaceholder]="true"
                                        [searchCountryFlag]="true"
                                        [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                                        [selectFirstCountry]="true"
                                        [preferredCountries]="preferredCountries"
                                        [maxLength]="15"
                                        [tooltipField]="TooltipLabel.Name"
                                        [phoneValidation]="true"
                                        [separateDialCode]="true"
                                        name="PhoneNumber"
                                        required
                                        [disabled]="!user.isActive">
                    </ngx-intl-tel-input>
                </div>
            </div>
            <div *ngIf="user.idInMaster != null" class="row-flex">
                <div class="ess-um-label">
                    <label for="Status">{{"Status" | localize}}</label>
                </div>
                <div class="ess-um-input">
                    <input id="Status" type="text" name="Status" class="form-control" value="{{user.isActive ? 'Active' : 'Inactive'}}" disabled>
                </div>
            </div>
            <div *ngIf="user.idInMaster != null && user.isActive" class="row-flex">
                <div class="ess-um-label">
                    <label for="Locked">{{"Locked" | localize}}</label>
                </div>
                <div class="ess-um-input">
                    <input id="Locked" type="text" name="Locked" class="form-control" value="{{user.isLocked ? 'Yes' : 'No'}}" disabled>
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="Role">{{"Role" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('Role') }">
                    <select id="Role" #roleSelect [(ngModel)]="user.roleId" class="form-control" name="Role" [disabled]="!user.isActive">
                        <option *ngFor="let r of roles" [ngValue]="r.roleId">{{r.roleDisplayName}}</option>
                    </select>
                </div>
            </div>
        </form>

        <div *ngIf="user.isActive" class="row-flex-start margin-top">
            <button id="createUserCancel" pButton type="button" [disabled]="saving" (click)="get()" label="{{l('Cancel')}}"></button>
            <button id="createUserResetPassword" pButton type="button" [disabled]="saving" *ngIf="user.isActive" (click)="resetPassword()" label="{{l('ResetPassword')}}" class="margin-left"></button>
            <button id="removeUser" pButton type="button" [disabled]="saving" *ngIf="user.isActive" (click)="remove()" label="{{l('Remove')}}" class="margin-left"></button>
            <button id="createUserSave" pButton type="button" [disabled]="validatingEmail || validatingUsername || saving"
                    (click)="save()" label="{{l('Save')}}" class="margin-left"></button>
        </div>

        <div class="margin-top-15">
            <p>Note: Please make sure the user account is used by one person only.</p>
            <p>All user's activity is being tracked.</p>
        </div>
    </div>
</div>

<entityChangeHistoryModal #entityChangeHistoryModal></entityChangeHistoryModal>
