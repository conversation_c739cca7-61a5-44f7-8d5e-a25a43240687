
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FileUploadModule } from 'primeng/fileupload';
import { AppCommonModule } from '@app/shared/common/app-common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { CountoModule } from 'angular2-counto';
import { ModalModule, TabsModule, TooltipModule, BsDropdownModule, PopoverModule } from 'ngx-bootstrap';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { BsDatepickerModule, BsDatepickerConfig, BsDaterangepickerConfig, BsLocaleService } from 'ngx-bootstrap/datepicker';
import { NgxBootstrapDatePickerConfigService } from 'assets/ngx-bootstrap/ngx-bootstrap-datepicker-config.service';
import { EconomicSubstanceRoutingModule } from './economicsubstance-routing.module';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { DeclarationComponent } from './es-component/declaration-disclaimer.component/declaration-disclaimer.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { RadioButtonModule } from 'primeng/radiobutton';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DynamicDialogModule } from 'primeng/dynamicdialog';

import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { StepsModule } from 'primeng/steps';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { ListboxModule } from 'primeng/listbox';
import { InputMaskModule } from 'primeng/inputmask';
import { KeyFilterModule } from 'primeng/keyfilter';
import { SharedComponent } from './sharedfunctions';
import { TableModule } from 'primeng/table';
import { SpinnerModule } from 'primeng/spinner';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { CigaDetailDialogComponent } from './es-component/es-tables-dialogs/ciga-detail/ciga-detail-dialog.component';

import { CigaDetailTableComponent } from './es-component/es-tables-dialogs/ciga-detail/ciga-detail-table.component';
import { MessageService } from 'primeng/api';
import { MainEconomicsComponent } from './es-component/main-economics.component/main-economics.component';
import { MainEconomicsReoComponent } from './es-component/main-economics-reo.component/main-economics-reo.component';
import { DocumentPreviewComponent } from './es-component/document-preview/document-preview.component';
import { UploadDoumentComponent } from './es-component/upload-doument/upload-doument.component';
import { PdfJsViewerModule } from 'ng2-pdfjs-viewer';

import { DocumentPreviewDialogComponent } from './es-component/document-preview-dialog/document-preview-dialog.component';
import { DisplaySubmittedComponent } from './es-component/display-submitted.component/display-submitted.component';
import { NgxIntlTelInputModule } from 'ngx-intl-tel-input';
import { EsCaReviewComponent } from './es-component/es-ca-review.component/es-ca-review.component';
import { DialogService } from 'primeng/api';
import { DisplayCaReviewCommentComponent } from './es-component/display-ca-review-comment.component/display-ca-review-comment.component';
import { CaReviewComponent } from './es-component/ca-review.component/ca-review.component';
import { CaReReviewComponent } from './es-component/ca-re-review.component/ca-re-review.component';
import { ChangeCaReviewstatusComponent } from './es-component/change-ca-reviewstatus.component/change-ca-reviewstatus.component';
import { ReviewstatusRelativeactivityComponent } from './es-component/reviewstatus-relativeactivity.component/reviewstatus-relativeactivity.component';
import { Step1ESComponent } from './es-component/step1-es/step1-es.component';
import { Step2ESComponent } from './es-component/step2-es/step2-es.component';
import { Step3EsComponent } from './es-component/step3-es/step3-es.component';
import { Step4EsComponent } from './es-component/step4-es/step4-es.component';
import { Step5EsComponent } from './es-component/step5-es/step5-es.component';
import { PersonDetailTableComponent } from './es-component/es-tables-dialogs/person-detail/person-detail-table.component';
import { PersonDetailDialogComponent } from './es-component/es-tables-dialogs/person-detail/person-detail-dialog.component';
import { MeetingDetailDialogComponent } from './es-component/es-tables-dialogs/meeting-detail/meeting-detail-dialog.component';
import { MeetingDetailTableComponent } from './es-component/es-tables-dialogs/meeting-detail/meeting-detail-table.component';
import { QualificationDetailDialogComponent } from './es-component/es-tables-dialogs/qualification-detail/qualification-detail-dialog.component';
import { QualificationDetailTableComponent } from './es-component/es-tables-dialogs/qualification-detail/qualification-detail-table.component';
import { OutsourceDetailTableComponent } from './es-component/es-tables-dialogs/outsource-detail/outsource-detail-table.component';
import { OutsourceDetailDialogComponent } from './es-component/es-tables-dialogs/outsource-detail/outsource-detail-dialog.component';
import { AddressDetailDialogComponent } from './es-component/es-tables-dialogs/address-detail/address-detail-dialog.component';
import { AddressDetailTableComponent } from './es-component/es-tables-dialogs/address-detail/address-detail-table.component';
import { RelevantActivityComponent } from './es-component/relevant-activity/relevant-activity.component';
import { UploadFilesComponent } from './es-component/es-tables-dialogs/upload-files/upload-files.component';
import { ESImportComponent } from './es-component/es-import.component/es-import.component';
import { ESImportEditComponent } from './es-component/es-import-edit.component/es-import-edit.component';
import { DisplayHistoryComponent } from './es-component/display-history.component/display-history.component';
import { ProvisionalTreatmentEsComponent } from './es-component/provisional-treatment-es.component/provisional-treatment-es.component';
import { RequestedInformationEsComponent } from './es-component/requested-information-es.component/requested-information-es.component';
import { ActionEnforcedEsComponent } from './es-component/action-enforced-es.component/action-enforced-es.component';
import { CaReopenDialogComponent } from './es-component/ca-review.component/ca-reopen-dialog/ca-reopen-dialog.component';
import { ESEntityDetailsComponent } from './es-component/es-entity-details/es-entity-details.component';
import { EntityDetailTableComponent } from './es-component/es-entity-details/entity-detail-table.component';
import { EntityDetailDialogComponent } from './es-component/es-entity-details/entity-detail-dialog.component';

NgxBootstrapDatePickerConfigService.registerNgxBootstrapDatePickerLocales();

@NgModule({
    imports: [
        FormsModule,
        ReactiveFormsModule,
        CommonModule,
        FileUploadModule,
        ModalModule.forRoot(),
        TabsModule.forRoot(),
        TooltipModule.forRoot(),
        PopoverModule.forRoot(),
        BsDropdownModule.forRoot(),
        NgxIntlTelInputModule,
        FormsModule,
        MultiSelectModule,
        AppCommonModule,
        TableModule,
        UtilsModule,
        CalendarModule,
        CheckboxModule,
        InputTextareaModule,
        ListboxModule,
        EconomicSubstanceRoutingModule,
        CountoModule,
        NgxChartsModule,
        RadioButtonModule,
        DropdownModule,
        InputTextModule,
        StepsModule,
        CardModule,
        InputMaskModule,
        ButtonModule,
        SpinnerModule,
        KeyFilterModule,
        ProgressSpinnerModule,
        ToastModule,
        BsDatepickerModule.forRoot(),
        BsDropdownModule.forRoot(),
        PopoverModule.forRoot(),
        PdfJsViewerModule,
        DynamicDialogModule
    ],
    declarations: [
        CigaDetailDialogComponent,
        CigaDetailTableComponent,
        DeclarationComponent,
        MainEconomicsComponent,
        MainEconomicsReoComponent,
        DocumentPreviewComponent,
        UploadDoumentComponent,
        DocumentPreviewDialogComponent,
        DisplaySubmittedComponent,
        EsCaReviewComponent,
        DisplayCaReviewCommentComponent,
        CaReviewComponent,
        CaReReviewComponent,
        ChangeCaReviewstatusComponent,
        ReviewstatusRelativeactivityComponent,
        Step1ESComponent,
        Step2ESComponent,
        Step3EsComponent,
        Step4EsComponent,
        Step5EsComponent,
        PersonDetailTableComponent,
        PersonDetailDialogComponent,
        MeetingDetailDialogComponent,
        MeetingDetailTableComponent,
        QualificationDetailDialogComponent,
        QualificationDetailTableComponent,
        OutsourceDetailTableComponent,
        OutsourceDetailDialogComponent,
        AddressDetailDialogComponent,
        AddressDetailTableComponent,
        RelevantActivityComponent,
        UploadFilesComponent,
        ESImportComponent,
        ESImportEditComponent,
        DisplayHistoryComponent,
        ProvisionalTreatmentEsComponent,
        RequestedInformationEsComponent,
        ActionEnforcedEsComponent,
        CaReopenDialogComponent,
        ESEntityDetailsComponent,
        EntityDetailTableComponent,
        EntityDetailDialogComponent
    ],
    providers: [
        SharedComponent,
        MessageService,
        DialogService,
        { provide: BsDatepickerConfig, useFactory: NgxBootstrapDatePickerConfigService.getDatepickerConfig },
        { provide: BsDaterangepickerConfig, useFactory: NgxBootstrapDatePickerConfigService.getDaterangepickerConfig },
        { provide: BsLocaleService, useFactory: NgxBootstrapDatePickerConfigService.getDatepickerLocale }

    ],
   
    entryComponents: [
        DocumentPreviewComponent,
        DocumentPreviewDialogComponent,
        DisplayCaReviewCommentComponent,
        ChangeCaReviewstatusComponent,
        ReviewstatusRelativeactivityComponent
    ]

})


export class EconomicSubstanceModule {
}
