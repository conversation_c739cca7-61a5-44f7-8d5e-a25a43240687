<div class="ess-csp-details col-flex">
    <p class="ess-title">{{l("CSP Details")}}</p>

    <div class="ess-csp-details-container col-flex">
        <hr />
        <ngFormValidation  #ctspFormValidation displayMode="2"></ngFormValidation>
        <form [hidden]="ctspDetail.id == null" #ctspForm="ngForm">
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="CtspNumber">{{"Code" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('Code') }">
                    <input id="CtspNumber" #ctspNumberInput="ngModel" type="text"  [disabled]="!isEditMode" required
                    name="Code" class="form-control" [(ngModel)]="ctspDetail.number" />
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="CtspName">{{"Name" | localize}} *</label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('Name') }">
                    <input id="CtspName" #ctspNameInput="ngModel" type="text"  [disabled]="!isEditMode" required
                    name="Name" class="form-control" [(ngModel)]="ctspDetail.name" />
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="EmailAddress">{{"Email Address" | localize}} </label>
                </div>
                <div class="ess-um-input" [ngClass]="{ 'input-error': hasError('EmailAddress') }">
                    <input id="EmailAddress" #emailAddressInput="ngModel" type="email" [disabled]="!isEditMode" 
                           name="EmailAddress" class="form-control" [(ngModel)]="ctspDetail.emailAddress"  maxlength="512" email
                          />
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="PhoneNumber">{{"CSPs Phone" | localize}}</label>
                </div>
                <div class="ess-um-input ess-um-input-phone" [ngClass]="{ 'input-error': hasError('PhoneNumber') }">
                    <ngx-intl-tel-input id="phoneNumber"
                                        #phoneNumberInput
                                        [(ngModel)]="phoneNumber"
                                        
                                        [enableAutoCountrySelect]="true"
                                        [enablePlaceholder]="true"
                                        [searchCountryFlag]="true"
                                        [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                                        [selectFirstCountry]="true"
                                        [preferredCountries]="preferredCountries"
                                        [maxLength]="15"
                                        [tooltipField]="TooltipLabel.Name"
                                        [phoneValidation]="true"
                                        [separateDialCode]="true"
                                        name="PhoneNumber"
                                        [disabled]="!isEditMode">
                    </ngx-intl-tel-input>
                </div>
            </div>
            <div class="row-flex">
                <div class="ess-um-label">
                    <label for="ItPhoneNumber">{{"IT Phone" | localize}}</label>
                </div>
                <div class="ess-um-input ess-um-input-phone" [ngClass]="{ 'input-error': hasError('ITphoneNumber') }">
                    <ngx-intl-tel-input id="ITphoneNumber"
                                        #phoneNumberInput
                                        [(ngModel)]="itPhoneNumber"
                                        [enableAutoCountrySelect]="true"
                                        [enablePlaceholder]="true"
                                        [searchCountryFlag]="true"
                                        [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                                        [selectFirstCountry]="true"
                                        [preferredCountries]="preferredCountries"
                                        [maxLength]="15"
                                        [tooltipField]="TooltipLabel.Name"
                                        [phoneValidation]="true"
                                        [separateDialCode]="true"
                                        name="ITphoneNumber"
                                        [disabled]="!isEditMode">
                    </ngx-intl-tel-input>
                </div>
            </div>

            <div class="ess-csp-form-control row-flex">
                <label for="CtspAddress">{{"Address" | localize}}</label>
                <textarea id="CtspAddress" #ctspAdressInput="ngModel" type="text"  [disabled]="!isEditMode" rows="4"
                          name="CtspAddress" class="form-control" [(ngModel)]="ctspDetail.address">
                </textarea>
            </div>

            <div class="ess-csp-form-control row-flex">
                <label for="CtspComment">{{"Comments" | localize}}</label>
                <textarea id="CtspComment" #ctspCommentInput="ngModel" type="text"  [disabled]="!isEditMode" rows="4"
                          name="CtspComment" class="form-control" [(ngModel)]="ctspDetail.comments">
                </textarea>
            </div>
            <div>
                <label for="active">{{"Active" | localize}} *</label>
                <input id="active" #isActiveInput="ngModel" type="checkbox" name="active"  [disabled]="!isEditMode" style="padding-left:5px"[(ngModel)]="ctspDetail.active">
            </div>
            
            <div *ngIf="!isEditMode" class="row-flex-start margin-top">
                <button id="editButton" pButton type="button" (click)="onEdit()" label="{{l('Edit')}}"></button>
              </div>
              
              <div *ngIf="isEditMode" class="row-flex-start margin-top">
                <button id="cancelButton" pButton type="button" (click)="onCancel()" label="{{l('Cancel')}}" style="margin-right: 20px;"></button>
                <button id="saveButton" pButton type="button" (click)="Save()" label="{{l('Save')}}"></button>
              </div>
        </form>
    </div>
</div>
