import { Component, OnInit, Injector } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EsCaAssessmentCommentsDto } from '@shared/service-proxies/service-proxies';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/api';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
@Component({
  selector: 'app-display-ca-review-comment',
  templateUrl: './display-ca-review-comment.component.html',
  styleUrls: ['./display-ca-review-comment.component.css']
})
export class DisplayCaReviewCommentComponent extends AppComponentBase implements OnInit
{

    escaAssessmentCommentsDto : EsCaAssessmentCommentsDto[];
    ctspId: string;
    constructor(injector: Injector, public config: DynamicDialogConfig, private _sharedComponet: SharedComponent)
    {
        super(injector)
        this.escaAssessmentCommentsDto = config.data.escaAssessmentCommentsDto;
        this.ctspId = config.data.ctspId;
    }


    returnDate(obj) {
        return new Date(obj.format('YYYY-MM-DD HH:mm:ss') + ' UTC');
    }
  ngOnInit() {
    }

    PreviewDoc(id: any) {
        this._sharedComponet.PreviewDoc(id, this.ctspId, true);
    }

    removeExtension(filename): string
    {
    let lastDotPosition = filename.lastIndexOf(".");
    if (lastDotPosition === -1) return filename;
    else return filename.substr(0, lastDotPosition);
}

    GetShortFileName(filename: any):string
    {
        // get the extension;
        let fileExtension= filename.substring(filename.lastIndexOf('.') + 1, filename.length) || filename;

        let name = this.removeExtension(filename);

     return name.length > 10 ? name.substring(0, 10) + '.' + fileExtension : filename
     
    }

    GeTAssessmentType(action: any)
    {
        if (action == 5) return "Provisional Treatment Approved";
        if (action == 6) return "Provisional Treatment Rejected";
        if (action == 1) return "Assessment Updated";
        return "Assessment performed";
    }
}

