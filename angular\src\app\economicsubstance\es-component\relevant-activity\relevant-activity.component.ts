import { Component, OnInit, Injector, Input, ViewChild, AfterViewInit } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    EconomicSubstanceDeclarationDto, CIGALookUpDto, CountryDto, RelevantActivity, CurrencyDto,
    ESCAAssessmentDto, ESCAAssessmentInputDto, AssessmentActionType, WrokflowServiceProxy, RelativeActivityStatusDto,
    RelativeActivitySelection,
    CorporateEntityDto
} from '@shared/service-proxies/service-proxies';
import { NgForm } from '@angular/forms';
import { RelevantDocumentsName, FieldDetail } from '@app/economicsubstance/EconomicSubstance';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';
import { RelaventActivityReviewStatus } from '@app/economicsubstance/EconomicSubstance';
import { ReviewstatusRelativeactivityComponent } from '@app/economicsubstance/es-component/reviewstatus-relativeactivity.component/reviewstatus-relativeactivity.component';
import { DialogService, SelectItem } from 'primeng/api';
import * as moment from 'moment';
import { AppConsts } from '@shared/AppConsts';

@Component({
    selector: 'app-relevant-activity',
    templateUrl: './relevant-activity.component.html',
    styleUrls: ['./relevant-activity.component.css']
})

export class RelevantActivityComponent extends AppComponentBase implements OnInit 
 {
    @Input() public economicsubstance: EconomicSubstanceDeclarationDto; // this is typed as string, but you can use any type you want
    @Input() public economicsubstanceOld: EconomicSubstanceDeclarationDto;
    @Input() public corporateEntity: CorporateEntityDto;
    @Input() headingSeq: string;
    @Input() public index: number;
    @Input() country: CountryDto;
    @Input() CIGAlookup: CIGALookUpDto[];
    @Input() sectionEnablement: any;
    @Input() currency: CurrencyDto[];
    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() historyDisplay: boolean;
    @Input() esassessment: ESCAAssessmentDto;
    @Input() relevantActivityStatus: RelaventActivityReviewStatus[];
    @Input() ctspId: any;
    public highRiskDoc:RelevantDocumentsName;    
    public OtherCIGADoc: RelevantDocumentsName;
    public tangibleAssetIncomeDoc:RelevantDocumentsName;
    public tangibleAssetEmployeeResponsibilityDoc:RelevantDocumentsName;
    public historyofStrategicDecisionsInBVIDoc:RelevantDocumentsName;
    public historyofTradingActivityIncomeDoc:RelevantDocumentsName;
    public ipAssetsInBVIDoc:RelevantDocumentsName;
    public ipAssetsEmployeeResponsibilityDoc:RelevantDocumentsName;
    public concreteEvidenceDecisionInBVIDoc:RelevantDocumentsName;

    @Input() importOnlyMode: boolean = false;


    escaassessmentInputDto: ESCAAssessmentInputDto;
    selectedActivityStatus: RelaventActivityReviewStatus = new RelaventActivityReviewStatus();

    fieldDetails: FieldDetail[]=[];
    field = new Array();
    ShowHideConditions: boolean[] = [];

    @ViewChild('#relevantform', { static: false }) form: NgForm; 
    
    public otherCode: number;

    public mobileTypes : SelectItem[] = AppConsts.incomeType.map(x => ({ label: x.value, value: x.value }));

    constructor(injector: Injector,
        public _economicSubstanceService: EconomicSubstanceService,
        private _dialogService: DialogService,
        private _wrokflowServiceProxy: WrokflowServiceProxy
    ) { super(injector); }

    ngOnInit()
    {       
        this.InitializeFiledIdName();
        this.highRiskDoc = RelevantDocumentsName.HighRiskDocuments;
        this.OtherCIGADoc = RelevantDocumentsName.OtherCIGADocuments;
        this.tangibleAssetIncomeDoc = RelevantDocumentsName.TangibleAssetIncomeDocuments;
        this.tangibleAssetEmployeeResponsibilityDoc = RelevantDocumentsName.TangibleAssetEmployeeResponsibilityDocuments;
        this.historyofStrategicDecisionsInBVIDoc = RelevantDocumentsName.HistoryofStrategicDecisionsInBVIDocuments;
        this.historyofTradingActivityIncomeDoc = RelevantDocumentsName.HistoryofTradingActivityIncomeDocuments;
        this.ipAssetsInBVIDoc = RelevantDocumentsName.IPAssetsInBVIDocuments;
        this.ipAssetsEmployeeResponsibilityDoc = RelevantDocumentsName.IPAssetsEmployeeResponsibilityDocuments;
        this.concreteEvidenceDecisionInBVIDoc = RelevantDocumentsName.ConcreteEvidenceDecisionInBVIDocuments;

        this.fieldDetails = this.sectionEnablement.map(y => ({ enablment: y.enablment, number: y.number }))

        if (!this.readOnlyMode)
        {
            
            if (typeof this.economicsubstance.currency === 'undefined' || this.economicsubstance.currency === null) {
                this.economicsubstance.currency = this.currency.find(x => x.currencyCode === 'USD');
                this.economicsubstance.currencyId = this.economicsubstance.currency.id;
                // need to set the old value the initial value
                if (this.economicsubstanceOld) {
                    this.economicsubstanceOld.currency = this.currency.find(x => x.currencyCode === 'USD');
                    this.economicsubstanceOld.currencyId = this.economicsubstanceOld.currency.id;
                }
            }
           
            this.otherCode = this.CIGAlookup.filter(x => x.activityDetails.toLowerCase().includes("please specify"))[0].activityOrder;
            if (!this.economicsubstance.relevantActivities[this.index].highRiskDocuments)
                this.economicsubstance.relevantActivities[this.index].highRiskDocuments = [];


            if (!this.economicsubstance.relevantActivities[this.index].otherCIGADocuments)
                this.economicsubstance.relevantActivities[this.index].otherCIGADocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].tangibleAssetIncomeDocuments)
                this.economicsubstance.relevantActivities[this.index].tangibleAssetIncomeDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].tangibleAssetEmployeeResponsibilityDocuments)
                this.economicsubstance.relevantActivities[this.index].tangibleAssetEmployeeResponsibilityDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].historyofStrategicDecisionsInBVIDocuments)
                this.economicsubstance.relevantActivities[this.index].historyofStrategicDecisionsInBVIDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].historyofTradingActivityIncomeDocuments)
                this.economicsubstance.relevantActivities[this.index].historyofTradingActivityIncomeDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].ipAssetsInBVIDocuments)
                this.economicsubstance.relevantActivities[this.index].ipAssetsInBVIDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].ipAssetsEmployeeResponsibilityDocuments)
                this.economicsubstance.relevantActivities[this.index].ipAssetsEmployeeResponsibilityDocuments = [];

            if (!this.economicsubstance.relevantActivities[this.index].concreteEvidenceDecisionInBVIDocuments)
                this.economicsubstance.relevantActivities[this.index].concreteEvidenceDecisionInBVIDocuments = [];

            // need to intializa all the arrays in case they are not.

            if (!this.economicsubstance.relevantActivities[this.index].premisesAddress)
                this.economicsubstance.relevantActivities[this.index].premisesAddress = [];
            if (!this.economicsubstance.relevantActivities[this.index].serviceProviders)
                this.economicsubstance.relevantActivities[this.index].serviceProviders = [];
            if (!this.economicsubstance.relevantActivities[this.index].managementDetails)
                this.economicsubstance.relevantActivities[this.index].managementDetails = [];
            if (!this.economicsubstance.relevantActivities[this.index].attendMeetingDetails)
                this.economicsubstance.relevantActivities[this.index].attendMeetingDetails = [];
            if (!this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity)
                this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity = [];
            if (!this.economicsubstance.relevantActivities[this.index].employeeQualificationDetails)
                this.economicsubstance.relevantActivities[this.index].employeeQualificationDetails = [];  
        }
        

        this._economicSubstanceService.validations$.subscribe((data: any[]) => {
            if (data) {
                data.forEach((validation: any) => {
                    this.runNextValidations(validation);
                });
            }

        });

        if (this.displayFromCa)        this.setSelectedActivityValue();

    }


    ChangeRAStatus(event: any)
    {
        // need to pass the following
        // the pass and fail
        // the id of assessment
        // the name of the activity for comment
        if (event.value.label != 'Select')
        {
            this.escaassessmentInputDto = new ESCAAssessmentInputDto();
            this.escaassessmentInputDto.ctspId = this.ctspId;

            this.escaassessmentInputDto.id = this.esassessment.id;
            this.escaassessmentInputDto.createdAt = moment.utc();
            this.escaassessmentInputDto.action = AssessmentActionType.AddComment;
            this.escaassessmentInputDto.currentRelativeActivityStatus = new RelativeActivityStatusDto();

            this.escaassessmentInputDto.currentRelativeActivityStatus.id = this.economicsubstance.relevantActivities[this.index].id;
            this.escaassessmentInputDto.currentRelativeActivityStatus.status = event.value.value;
            this.escaassessmentInputDto.comments = this.economicsubstance.relevantActivities[this.index].relevantActivityName
                                                   + " change Status to "
                + RelativeActivitySelection[event.value.value];




            const ref = this._dialogService.open(ReviewstatusRelativeactivityComponent, {
                data: { value: this.escaassessmentInputDto, economicsubstance: this.economicsubstance },
                header: this.l("Relevant Activity Status"),
                width: '20%',
                height: '10%',
                contentStyle: { 'height': '100%', 'display': 'flex' }
            });

            ref.onClose.subscribe
                (
                    () => {
                        this._wrokflowServiceProxy.getESAssessment(this.ctspId, this.esassessment.economicSubstanceDeclarationId, null).subscribe(
                            newresult => {
                                this.esassessment = newresult;
                                this.setSelectedActivityValue();
                            }
                        );
                    }
                );
        }


    }


    // Function to populate the selected value of the relative activity status Ca
    setSelectedActivityValue() {
        this.selectedActivityStatus = this.relevantActivityStatus[0];
        if (this.esassessment) {
            if (this.esassessment.relativeActivityStatus) {
                let findItem =
                    this.esassessment.relativeActivityStatus.relativeActivityList.find(x => x.id == this.economicsubstance.relevantActivities[this.index].id);

                if (findItem) {
                    this.selectedActivityStatus = this.relevantActivityStatus.find(x => x.value == findItem.status);
                    this.relevantActivityStatus = this.relevantActivityStatus.filter(x => x.label !== 'Select');
                }
            }

        }
    }

    runNextValidations(validation) {
        if (validation.headerSeq && validation.validationResultDto && validation.headerSeq === this.headingSeq) {
            let headingSeq = validation.headerSeq;
            let activityError = this._economicSubstanceService.step4Error[headingSeq];
            validation.validationResultDto.forEach((v: any) =>
            {
                if (activityError && !activityError[v.fieldName]) {
                    this._economicSubstanceService.step4Error[headingSeq][v.fieldName] = true;
                        if (v.validationType === 1) {
                            // Format validation string by adding heading title and sequence
                            this._economicSubstanceService.errorList.push(validation.activityName + ": " + v.validationString);
                        }
                        else {
                            this._economicSubstanceService.warningList.push(validation.activityName + ": " + v.validationString);

                        }
                    
                }
            })
        }

    }

    GetTotalInUSD(total: any)
    {
        
        if (total)
        {
            if (this.economicsubstance.currencyExchangeRate === null || typeof (this.economicsubstance.currencyExchangeRate)=== undefined ) return "";
            let result = total * this.economicsubstance.currencyExchangeRate;
            return result.toFixed(2);
        }
    }
    SetCurrencyId(item: any) {
        this.economicsubstance.currencyId = item.value.id;
    }

    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }
   

    InitializeFiledIdName()
    {
        this.field[0] = new Array(3);
        this.field[0][0] = 'isActivityDirectedInBVIT' + this.headingSeq;
        this.field[0][1] = 'isActivityDirectedInBVIF' + this.headingSeq;
        this.field[0][2] = 'isActivityDirectedInBVI' + this.headingSeq;
        this.field[1] = new Array(3);
        this.field[1][0] = 'noofconductedmeetingid' + this.headingSeq;
        this.field[1][2] = 'noofconductedmeetingname' + this.headingSeq;
        this.field[2] = new Array(3);
        this.field[2][0] = 'nnoofMeetingheldinBVIid' + this.headingSeq;
        this.field[2][2] = 'nnoofMeetingheldinBVIname' + this.headingSeq;
        this.field[3] = new Array(3);
        this.field[3][0] = 'isMeetingMinutesInBVIT' + this.headingSeq;
        this.field[3][1] = 'isMeetingMinutesInBVIF' + this.headingSeq;
        this.field[3][2] = 'isMeetingMinutesInBVIname' + this.headingSeq;
        this.field[4] = new Array(3);
        this.field[4][0] = 'currencyID' + this.headingSeq;
        this.field[4][2] = 'currencyId' + this.headingSeq;
        this.field[5] = new Array(3);
        this.field[5][0] = 'totalturnoverid' + this.headingSeq;
        this.field[5][2] = 'totalturnovername' + this.headingSeq;
        this.field[6] = new Array(3);
        this.field[6][0] = 'currencyID1' + this.headingSeq;
        this.field[6][2] = 'currencyId1' + this.headingSeq;
        this.field[7] = new Array(3);
        this.field[7][0] = 'totalexpeditureid' + this.headingSeq;
        this.field[7][2] = 'totalexpediturename' + this.headingSeq;
        this.field[8] = new Array(3);
        this.field[8][0] = 'currencyID2' + this.headingSeq;
        this.field[8][2] = 'currencyId2' + this.headingSeq;
        this.field[9] = new Array(3);
        this.field[9][0] = 'totalexpeditureinBVIid' + this.headingSeq;
        this.field[9][2] = 'totalexpeditureinBVIname' + this.headingSeq;
        this.field[10] = new Array(3);
        this.field[10][0] = 'totalnofulltimeemployeeid' + this.headingSeq;
        this.field[10][2] = 'totalnofulltimeemployeename' + this.headingSeq;
        this.field[11] = new Array(3);
        this.field[11][0] = 'totalnofulltimeBVIid' + this.headingSeq;
        this.field[11][2] = 'totalnofulltimeBVIname' + this.headingSeq;
        this.field[12] = new Array(3);
        this.field[12][0] = 'isLegalEntityHighRiskT' + this.headingSeq;
        this.field[12][1] = 'isLegalEntityHighRiskF' + this.headingSeq;
        this.field[12][2] = 'isLegalEntityHighRisk' + this.headingSeq;
        this.field[13] = new Array(3);
        this.field[13][0] = 'doesEntityProvideEvidenceT' + this.headingSeq;
        this.field[13][1] = 'doesEntityProvideEvidenceF' + this.headingSeq;
        this.field[13][2] = 'doesEntityProvideEvidence' + this.headingSeq;
        this.field[14] = new Array(3);
        this.field[14][0] = 'currencyID3' + this.headingSeq;
        this.field[14][2] = 'currencyId3' + this.headingSeq;
        this.field[15] = new Array(3);
        this.field[15][0] = 'totalGrossAnnualIncomeid' + this.headingSeq;
        this.field[15][2] = 'totalGrossAnnualIncomename' + this.headingSeq;
        this.field[16] = new Array(3);
        this.field[16][0] = 'currencyID4' + this.headingSeq;
        this.field[16][2] = 'currencyId4' + this.headingSeq;
        this.field[17] = new Array(3);
        this.field[17][0] = 'grossIncomeRoyalitiesid' + this.headingSeq;
        this.field[17][2] = 'grossIncomeRoyalitiesname' + this.headingSeq;
        this.field[18] = new Array(3);
        this.field[18][0] = 'currencyID5' + this.headingSeq;
        this.field[18][2] = 'currencyId5' + this.headingSeq;
        this.field[19] = new Array(3);
        this.field[19][0] = 'grossIncomeGainsid' + this.headingSeq;
        this.field[19][2] = 'grossIncomeGainsname' + this.headingSeq;
        this.field[20] = new Array(3);
        this.field[20][0] = 'doesLegalEntityConductCIGAT' + this.headingSeq;
        this.field[20][1] = 'doesLegalEntityConductCIGAF' + this.headingSeq;
        this.field[20][2] = 'doesLegalEntityConductCIGAname' + this.headingSeq;
        this.field[21] = new Array(3);
        this.field[21][0] = 'doesEntityProvideEvidenceroRebutT' + this.headingSeq;
        this.field[21][1] = 'doesEntityProvideEvidenceroRebutF' + this.headingSeq;
        this.field[21][2] = 'doesEntityProvideEvidenceroRebut' + this.headingSeq;
        this.field[22] = new Array(3);
        this.field[22][0] = 'hasanyincomeT' + this.headingSeq;
        this.field[22][1] = 'hasanyincomeF' + this.headingSeq;
        this.field[22][2] = 'hasanyincomename' + this.headingSeq;
        this.field[23] = new Array(3);
        this.field[23][0] = 'wascigaoutsourcedT' + this.headingSeq;
        this.field[23][1] = 'wascigaoutsourcedF' + this.headingSeq;
        this.field[23][2] = 'wascigaoutsourcedname' + this.headingSeq;
        this.field[24] = new Array(3);
        this.field[24][0] = 'totalexpenditureid1' + this.headingSeq;
        this.field[24][2] = 'totalexpenditurename1' + this.headingSeq;
        this.field[25] = new Array(3);
        this.field[25][0] = 'doesBusinessRequireT' + this.headingSeq;
        this.field[25][1] = 'doesBusinessRequireF' + this.headingSeq;
        this.field[25][2] = 'doesBusinessRequireEquipment' + this.headingSeq;
        this.field[26] = new Array(3);
        this.field[26][0] = 'equipmentInJurisdictionT' + this.headingSeq;
        this.field[26][1] = 'equipmentInJurisdictionF' + this.headingSeq;
        this.field[26][2] = 'equipmentInJurisdiction' + this.headingSeq;
        this.field[27] = new Array(3);
        this.field[27][0] = 'doesEntityComplyItsStatutorT' + this.headingSeq;
        this.field[27][1] = 'doesEntityComplyItsStatutorF' + this.headingSeq;
        this.field[27][2] = 'doesEntityComplyItsStatutor' + this.headingSeq;
        this.field[28] = new Array(3);
        this.field[28][0] = 'doesEntityManageEquityT' + this.headingSeq;
        this.field[28][1] = 'doesEntityManageEquityF' + this.headingSeq;
        this.field[28][2] = 'doesEntityManageEquity' + this.headingSeq;
        this.field[29] = new Array(3);
        this.field[29][0] = 'doesEntityHaveAdequateT' + this.headingSeq;
        this.field[29][1] = 'doesEntityHaveAdequateF' + this.headingSeq;
        this.field[29][2] = 'doesEntityHaveAdequate' + this.headingSeq;
        this.field[30] = new Array(3);
        this.field[30][0] = 'equipmentDescriptionid' + this.headingSeq;
        this.field[30][2] = 'equipmentDescriptionname' + this.headingSeq;
        this.field[31] = new Array(3);
        this.field[31][0] = 'equipmentDescriptionredid' + this.headingSeq;
        this.field[31][2] = 'equipmentDescriptionnredame' + this.headingSeq;


        this.field[32] = new Array(3);
        this.field[32][0] = 'currencyID6' + this.headingSeq;
        this.field[32][2] = 'currencyId6' + this.headingSeq;


        this.field[33] = new Array(3);
        this.field[33][0] = 'cigaOtherDetailid' + this.headingSeq;
        this.field[33][2] = 'cigaOtherDetailname' + this.headingSeq;               
    }
  
    convertFromsStringtoInteger(number: string) {

        return Number(number);
    }


    ShowHideResult(index) {

        this.ShowHideConditions[0] = !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].isActivityDirectedInBVI) && this.economicsubstance.relevantActivities[this.index].isActivityDirectedInBVI;

        if (!this.importOnlyMode) {

            this.ShowHideConditions[1] = this.economicsubstance.relevantActivities[this.index].noofMeetingHeldInBVI && this.economicsubstance.relevantActivities[this.index].noofMeetingHeldInBVI > 0;
        }
        else {

            this.ShowHideConditions[1] = this.economicsubstance.relevantActivities[this.index].noofMeetingHeldInBVIString &&
                this.convertFromsStringtoInteger(this.economicsubstance.relevantActivities[this.index].noofMeetingHeldInBVIString) > 0;
        }

        if (!this.importOnlyMode) {

            this.ShowHideConditions[2] = this.economicsubstance.relevantActivities[this.index].totalFullTimeEmployeeInBVI && this.economicsubstance.relevantActivities[this.index].totalFullTimeEmployeeInBVI > 0;
        }
        else {

            this.ShowHideConditions[2] = this.economicsubstance.relevantActivities[this.index].totalFullTimeEmployeeInBVIString &&
                this.convertFromsStringtoInteger(this.economicsubstance.relevantActivities[this.index].totalFullTimeEmployeeInBVIString) > 0;
        }


        

        // remove the condition to show outsourcing for cor Income -- change request 
        this.ShowHideConditions[3] = true;

            //this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity && this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity.length > 0 ?
            //!(this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity.findIndex(x => x.cigaActivity.activityDetails === "None" && !x.isDeleted) !== -1): true;

        this.ShowHideConditions[4] = this.ShowHideConditions[3] && (typeof this.economicsubstance.relevantActivities[this.index].hasAnyIncomeBeenOutsourced !== "undefined" && this.economicsubstance.relevantActivities[this.index].hasAnyIncomeBeenOutsourced); 

        this.ShowHideConditions[5] = this.ShowHideConditions[4] && (typeof this.economicsubstance.relevantActivities[this.index].wasCIGAOutsourcedInBVI !== "undefined" && this.economicsubstance.relevantActivities[this.index].wasCIGAOutsourcedInBVI)


        // only apply for holding bussines
       
        // Intellectual High risk rules A is true condition
        this.ShowHideConditions[8] = !this.importOnlyMode ? (!this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].isLegalEntityHighRisk) && this.economicsubstance.relevantActivities[this.index].isLegalEntityHighRisk) : false;
        // Intellectual rules A& B is true condition
        this.ShowHideConditions[9] = this.ShowHideConditions[8] && !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesEntityProvideEvidence) && this.economicsubstance.relevantActivities[this.index].doesEntityProvideEvidence;

        // Other core A is true
        this.ShowHideConditions[10] = !this.importOnlyMode ? (!this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesLegalEntityConductCIGA) && this.economicsubstance.relevantActivities[this.index].doesLegalEntityConductCIGA) : false;

        this.ShowHideConditions[11] = this.ShowHideConditions[10] && !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesEntityProvideEvidenceroRebut) && this.economicsubstance.relevantActivities[this.index].doesEntityProvideEvidenceroRebut;

        // equipment consitions

        this.ShowHideConditions[12] = !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesBusinessRequireEquipment) && this.economicsubstance.relevantActivities[this.index].doesBusinessRequireEquipment;
       
        this.ShowHideConditions[13] = this.ShowHideConditions[12] && !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].equipmentInJurisdiction) && this.economicsubstance.relevantActivities[this.index].equipmentInJurisdiction;

        // display 8 coreIncome & outsourcing if both are false
        // condition apply only for Intellectual
        let subrule1 = !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].isLegalEntityHighRisk) && !this.economicsubstance.relevantActivities[this.index].isLegalEntityHighRisk;
        let subrule2 = !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesLegalEntityConductCIGA) && !this.economicsubstance.relevantActivities[this.index].doesLegalEntityConductCIGA;
        this.ShowHideConditions[14] = this.economicsubstance.relevantActivities[this.index].releventActivityValue === RelevantActivity.IntelBA ? subrule1 && subrule2:true;


        // yes either High Risk or Other CIGA

        //BVI Declaration Changes 2.0       
        this.ShowHideConditions[15] = moment(this.economicsubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day'); 
        this.ShowHideConditions[16] = this.ShowHideConditions[15] && !this.ShowHideConditions[8];

        this.ShowHideConditions[17] = this.ShowHideConditions[15] && this.ShowHideConditions[8];
        this.ShowHideConditions[18] = this.ShowHideConditions[15] && this.ShowHideConditions[10];

        let isHoldBA = this.economicsubstance.relevantActivities[this.index].releventActivityValue === RelevantActivity.HoldBA;
        this.ShowHideConditions[19] = isHoldBA ? false : this.ShowHideConditions[15];
        this.ShowHideConditions[20] = isHoldBA && this.ShowHideConditions[15] ? !this.IsNullorUndefined(this.economicsubstance.relevantActivities[this.index].doesEntityManageEquity) && this.economicsubstance.relevantActivities[this.index].doesEntityManageEquity : true;            
        this.ShowHideConditions[21] = this.ShowHideConditions[15] ? this.ShowHideConditions[20] : false;
        
        let isIP = this.economicsubstance.relevantActivities[this.index].releventActivityValue === RelevantActivity.IntelBA;
        this.ShowHideConditions[22] = isIP;  
        
        this.ShowHideConditions[23] = this.ShowHideConditions[15] ? false : this.ShowHideConditions[8];

        return this.ShowHideConditions[index];
    }


    isOtherActivitySelected(readOnly = false): boolean
    {
        if (this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity && this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity.length > 0)
        {
            let filteredActivity = this.economicsubstance.relevantActivities[this.index].conductedCIGAActivity.filter(x => !x.isDeleted);
            if (!readOnly)
            {
               
                let result = filteredActivity.findIndex(x => x.cigaActivity.activityOrder == this.otherCode);
                return result != -1;
            }
            else {
                let result = filteredActivity.findIndex(x => x.cigaActivity.activityDetails.includes("please specify"));
                return result != -1;
            }
        }
    }   

}
