<div class="row-flex-space">
    <div class="ui-g-12" *ngFor="let fieldsGroup of fields">
        <div *ngFor="let field of fieldsGroup">
            <div *ngIf="!field.disabled" class="row-flex-space margin-top">
                <p class="label-text">{{l(field.name)}}:</p>
                <div [ngSwitch]="field.type" class="content-text width-60 margin-left">
                    <p type="text" class="word-space" *ngSwitchCase="'string'">{{field.value}}</p>

                    <a id="entityDisplayBasicDialog" href="javascript:;" *ngSwitchCase="'stringwithtooltip'" (click)="showBasicDialog(field.ctspId)">{{field.value}}</a>
                    <p type="text" *ngSwitchCase="'number'">{{field.value}}</p>
                    <p type="text" *ngSwitchCase="'date'">{{field.value | date: "dd/MM/yyyy"}}</p>
                    <p type="text" *ngSwitchCase="'address'">{{field.value}}</p>
                    <p type="text" *ngSwitchCase="'notes'">{{field.value}}</p>

                    <p type="text" *ngSwitchCase="'entityType'">{{field.value | entityDescription }}</p>

                    <p type="text" *ngSwitchCase="'bool'">
                        {{field.value != null ? (field.value ? l("Yes") : l("No")) : ""}}
                    </p>

                </div>
            </div>

        </div>
    </div>
</div>

<p-dialog header="CTSP Detail" [(visible)]="displayBasic" position="left" [modal]="true" [style]="{width: '20vw'}" [baseZIndex]="10000">
    <li *ngFor="let item of items">
        {{ item }}
    </li>
    <p-footer>
       <button id="displayBasicClose" type="button" pButton icon="pi pi-times" (click)="displayBasic=false" label="Close" class="ui-button-secondary"></button>
    </p-footer>
</p-dialog>

