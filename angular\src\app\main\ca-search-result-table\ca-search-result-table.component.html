<p-table [value]="resultItems.entities" (onLazyLoad)="getItems($event)" [paginator]="false" [rows]="25" [lazy]="true"
    [totalRecords]="resultItems.totalRecords" selectionMode="single" [selection]="resultItems.selectedEntityObservable | async" 
    (selectionChange)="resultItems.selectedEntityObservable.next($event)" dataKey="id" scrollHeight="calc(100vh - 21em)" [scrollable]="true" #resultTable>
    <ng-template pTemplate="header">
        <tr>
            <th class="my-cell" [pSortableColumn]="'name'" >
                {{l("Entity Name")}}
                <p-sortIcon id="sortEnitiyName" [field]="'name'"></p-sortIcon>
            </th>
            <th class="my-cell" [pSortableColumn]="'ctspNumber'">
                {{l("CSP")}}
                <p-sortIcon id="sortCTSPNumber" [field]="'ctspNumber'"></p-sortIcon>
            </th>
            <th class="my-cell" [pSortableColumn]="'incorFormationNumber'" >
                {{l("Incorp.#/Formation #")}}
                <p-sortIcon id="sortIncorFormationNumber" [field]="'incorFormationNumber'"></p-sortIcon>
            </th>
            <th class="my-cell" [pSortableColumn]="'submittedDate'">
                {{l("Submission time")}}
                <p-sortIcon id="sortSubmittedDate" [field]="'submittedDate'"></p-sortIcon>
            </th>
            <th class="my-cell" [pSortableColumn]="'reviewStatus'" >
                {{l("ES Assessment Status")}}
                <p-sortIcon id="sortReviewStatus" [field]="'reviewStatus'"></p-sortIcon>
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-item>
        <tr [pSelectableRow]="item">
            <td class="my-cell word-wrapping word-space" [ngClass]="{ 'struck-off': item.isDeleted }">{{item.name}}</td>
            <td class="my-cell" [ngClass]="{ 'struck-off': item.isDeleted }">{{item.ctspName}}</td>
            <td class="my-cell" [ngClass]="{ 'struck-off': item.isDeleted }">{{item.incorFormationNumber}}</td>
            <td class="my-cell" [ngClass]="{ 'struck-off': item.isDeleted }">
                <div *ngIf="needTodisplaySubmissionTime(item.reviewStatus)">
                    {{item.submissionDateLocal| date: "dd/MM/yyyy HH:mm" }}
                </div>
            </td>

            <td class="my-cell">
                <div [style.color]="getReviewStatusColor(item)" [ngClass]="{ 'struck-off': item.isDeleted }">
                    {{getReviewStatusName(item.reviewStatus)}}
                </div>
            </td>
        </tr>
    </ng-template>

    <ng-template pTemplate="paginatorleft">
        No. of Records = {{resultItems.totalRecords}}
    </ng-template>
</p-table>
<div class="primeng-paging-container">
   
    <app-custom-paginator #customPagination id="essSearchPagination"
            [totalRecordsCount]="resultItems.totalRecords"
            [defaultRecordsCountPerPage]="25"
            [predefinedRecordsCountPerPage]="primengTableHelper.predefinedRecordsCountPerPage"
            (pageChange)="getEntitiesRecords($event)"
            (pageSizeChange)="onPageSizeChange($event)">
    </app-custom-paginator>
</div>
