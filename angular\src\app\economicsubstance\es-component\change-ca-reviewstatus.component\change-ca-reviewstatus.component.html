
<div bsModal #changestatusmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="changestatusmodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    {{l("CaAssessmentAction")}}
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #changestatusform="ngForm">
                    <ngFormValidation #changestatusFormValidation [extraValidation]="extraValidation"></ngFormValidation>
                    <div width="90%">
                        <div class="col-flex">
                            <label>{{l("CaSelectAssessmentAction")}} </label>
                            <p-dropdown [options]="esReviewStatus"
                                        [(ngModel)]="esassessment.esReviewStatus"
                                        (onChange)="ChangeAssessment($event)"
                                        id="reviewlistid"
                                        name="reviewlistid"
                                        optionLabel="name"

                                        [style]="{'width':'95%'}">
                            </p-dropdown>

                        </div>
                        <div *ngIf="isInforamtionRequired">
                            <div class="col-flex-wmargin" [ngClass]="{ 'input-error-box': dueDate }">
                                <div>
                                    <label>{{l("CaEvdenceDueDate")}} <span class="required">*</span> </label>
                                </div>
                                <div>
                                    <div>
                                        <span>
                                            <p-calendar class="p-calender-properties" [utc]="true"
                                                        name="EvidenceDueDate"
                                                        id="infoDueDate"
                                                        dataType="string"
                                                        dateFormat="{{dateFormatString}}"
                                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                                        [showIcon]="true"
                                                        [minDate]="minimumDate"                                                        
                                                        [(ngModel)]="esassessment.inRequestedDueDateString"
                                                        placeholder="dd/MM/yyyy">
                                            </p-calendar>


                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-flex-wmargin" >
                                <div>
                                    <label>{{l("CTSPCommentsForTitle")}} <span class="required">*</span> </label>
                                    <textarea pInputTextarea rows="3"
                                              [(ngModel)]="esassessment.informationRequestedCommentsCA"
                                              required emptyspace
                                              id="commentsInformationReqid"
                                              maxlength="4000"
                                              name="CommentsForCSP">

                                    </textarea>
                                    <div class="charCounter">{{esassessment.informationRequestedCommentsCA?.length}}/4000</div>
                                </div>

                            </div>
                            <label>{{l("CTSPAttachmentsForTitle")}} </label>
                            <app-upload-files [Documents]="esCspDoc"
                                                  [DocumentTypeName]="InformationRequestedDocumentDoc"
                                                  [IsEconomicDocument]="false"
                                                  [readOnlyMode]="false"
                                                  [displayFromCa]="true"
                                                  [ctspId]="ctspId">
                           </app-upload-files>
                        </div>
                        <div *ngIf="isFail">
                            <div class="col-flex-wmargin">
                                <label>{{l("ActionEnforced")}} <span class="required">*</span> </label>
                                <textarea pInputTextarea rows="3"
                                          [(ngModel)]="esassessment.actionEnforced"
                                          required emptyspace
                                          id="ActionEnforcedid"
                                          name="ActionEnforced">
                                    </textarea>
                            </div>
                            <label>{{l("CaAssessmentAttachments")}} </label>
                            <app-upload-files [Documents]="esEnforcmentDoc"
                                                  [DocumentTypeName]="EnforcmentDocumentDoc"
                                                  [IsEconomicDocument]="false"
                                                  [readOnlyMode]="false"
                                                  [displayFromCa]="true"
                                                  [ctspId]="ctspId">
                           </app-upload-files>
                        </div>
                        <div class="col-flex-wmargin" >
                            <label>{{l("CaAssessmentComments")}}<span class="required">*</span> </label>
                            <textarea pInputTextarea rows="3"
                            [(ngModel)]="esassessment.comments"
                             required emptyspace
                             id="commentsAssessmentChangeid" name="CaAssessmentComments">

                            </textarea>
                        </div>
                        <div class="col-flex-wmargin">
                            <label>{{l("CaAssessmentAttachments")}} </label>

                            <app-upload-files [Documents]="currentComments.escaAssessmentDocuments"
                                              [DocumentTypeName]="ESCAAssessmentDocumentsDoc"
                                              [IsEconomicDocument]="false"
                                              [readOnlyMode]="false"
                                              [displayFromCa]="true"
                                              [ctspId]="ctspId">
                            </app-upload-files>

                            <!--<upload-document [CtspId]="ctspId" (itemUploaded)="AddEvidanceDocument($event)"></upload-document>
    <p-table [paginator]="false" [lazy]="true" scrollable="true" ScrollWidth="100%"
             [value]="currentComments.escaAssessmentDocuments"
             *ngIf="currentComments.escaAssessmentDocuments && currentComments.escaAssessmentDocuments.length > 0">
        <ng-template pTemplate="header">
            <tr>
                <th>File name</th>
                <th>File type</th>
                <th>Action</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-file>
            <tr>
                <td>
                    {{file.fileName}}
                </td>
                <td>
                    {{file.contetntType}}
                </td>
                <td>
                    <button pButton type="button" (click)="RemoveDoc(file.documentsId)" icon="pi pi-trash" iconPos="left">
                    </button>
                    <button pButton type="button" (click)="PreviewDoc(file.documentsId)" icon="pi pi-search" class="margin-left" iconPos="left"></button>
                </td>
            </tr>
        </ng-template>
    </p-table>-->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="complete()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
