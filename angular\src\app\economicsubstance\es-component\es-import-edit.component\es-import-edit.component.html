<div class="declaration-form-container">
    <div class="es-header-title">
        <p class="ess-title">{{l('ECONOMIC SUBSTANCE EDIT IMPORTED DECLARATION')}}</p>
    </div>

    <div class="es-header-container">
        <p-toast></p-toast>
        <div class="row-flex-space">
            <div class="width-69">
                <p-steps [model]="steps" [(activeIndex)]="activeIndex"></p-steps>
            </div>
            <div class="row-flex-align-center width-31">
                <button pButton type="button" [disabled]="disableButtons || disableIfImported" class="ui-button-rounded ui-button-secondary margin-left" (click)="onPreviousStep()" label="Previous"> </button>
                <button pButton type="button" [disabled]="disableButtons || disableIfImported" class="ui-button-rounded ui-button-warning margin-left" (click)="onNextStep()" label="Next"></button>
                <button pButton type="button" [disabled]="disableButtons || disableIfImported" icon="pi pi-trash" class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left" (click)="onDelete()" tooltip="Delete"></button>
                <button pButton type="button" icon="pi pi-times" class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left" (click)="onClose()" tooltip="Close"> </button>
                <button pButton type="button" [disabled]="disableNoError || disableIfImported"  class="ui-button-rounded ui-button-warning margin-left" (click)="onSaveDraft()" label="Save as Draft"></button>
                <button pButton type="button" [disabled]="disableNoError || disableIfImported" class="ui-button-rounded ui-button-warning margin-left"  (click)="onSubmitForReview()" label="Submit"></button>
            </div>
        </div>
    </div>


    <div class="es-header es-header-spacing">
        <div class="es-header-item-small">
            <h5>{{l("ESCE1")}}</h5>
            <p class="word-wrapping">{{corporateEntity.name}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE1b")}}</h5>
            <p class="word-wrapping">{{corporateEntity.clientNumber}}</p>
        </div>
        <div class="es-header-item-medsmall">
            <h5>{{l("ESCE2")}}</h5>
            <p>{{corporateEntity |entityIncoperationFormation}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE3")}}</h5>
            <p>{{corporateEntity.incorporationDate | date: "dd/MM/yyyy"}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE4")}}</h5>
            <p>{{ corporateEntity.entityType | entityDescription}}</p>
        </div>
        <div class="es-header-item-med">
            <h5>{{l("ESCE5")}}</h5>
            <div>
                {{corporateEntity.listedOnStockExchange?'Yes':'No'}}
            </div>

        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE5A")}}</h5>
            <p class="word-wrapping">  {{corporateEntity.stockExchange}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE6")}}</h5>
            <p class="word-wrapping">  {{corporateEntity.statusText}}</p>

        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE6D")}}</h5>
            <p class="word-wrapping"> {{corporateEntity?.addressLine1 || '' }} {{ corporateEntity?.addressLine2 || '' }} {{ corporateEntity?.countryName || '' }}</p>    
        </div>
    </div>

    <div [ngStyle]="getDeclarationFormStyle(false)" class="declaration-form-steps margin-top">

        <p-card *ngIf="_economicSubstanceService.errorList && _economicSubstanceService.errorList.length > 0 "
                class="p-card-error">
            <p-header>
                <div>
                    <i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Validation Errors")}}</label>
                </div>
            </p-header>
            <div class="col-flex">
                <div *ngFor="let error of _economicSubstanceService.errorList">
                    <p>{{error}}</p>
                </div>
            </div>
        </p-card>
        <p-card *ngIf="_economicSubstanceService.warningList && _economicSubstanceService.warningList.length > 0 " class="p-card-warning">
            <p-header>

                <div><i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Warnings")}}</label></div>
            </p-header>
            <div class="col-flex">
                <div *ngFor="let warning of _economicSubstanceService.warningList">
                    <p>{{warning}}</p>
                </div>
            </div>
        </p-card>




        <div>
            <div *ngIf="activeIndex == 0">
                <app-step1-es [economicsubstance]="currentEconomicSubstance"
                              [readOnlyMode]="false"
                              [localFiscalStartDate]="localFiscalStartDate"
                              [localFiscalEndDate]="localFiscalEndDate"
                              [importOnlyMode]="true"
                              [displayFromCa]="false">
                </app-step1-es>
            </div>

            <div *ngIf="activeIndex == 1">
                <app-es-entity-details [economicsubstance]="currentEconomicSubstance"  
                              [esEntityDetail]="esEntityDetail"   
                              [listOfCountry]="countries"     
                              [corporateEntity]="corporateEntity"
                              [currency]="currencies"
                              [readOnlyMode]="false"
                              [importOnlyMode]="true"
                              [displayFromCa]="false">
    
                </app-es-entity-details>
            </div>  

            <div *ngIf="activeIndex == 2">
                <app-step2-es [economicsubstance]="currentEconomicSubstance"
                              [selectedActivity]="selectedActivity"
                              [readOnlyMode]="false"
                              [importOnlyMode]="true"
                              [displayFromCa]="false">

                </app-step2-es>
                
                </div>
                <div *ngIf="activeIndex == 3">
                <app-step3-es [economicsubstance]="currentEconomicSubstance"
                              [listOfCountry]="countries"
                              [readOnlyMode]="false"
                              [euListcountries]="euListcountries"
                              [importOnlyMode]="true"
                              [displayFromCa]="false"></app-step3-es>
            </div>

            <div *ngIf="activeIndex == 4">
                <app-step4-es [economicsubstance]="currentEconomicSubstance"
                              [corporateEntity]="corporateEntity"
                              [country]="country"
                              [CIGALookup]="cigaLookup"
                              [currency]="currencies"
                              [readOnlyMode]="false"
                              [importOnlyMode]="true"
                              [displayFromCa]="false"></app-step4-es>
            </div>

            <div *ngIf="activeIndex == 5">
                <app-step5-es [economicsubstance]="currentEconomicSubstance"
                              [readOnlyMode]="false"
                              [displayFromCa]="false">
                </app-step5-es>
            </div>

            <div *ngIf="activeIndex == 6">
                <app-main-economics-reo [corporateEntity]="corporateEntity"
                                        [currentEconomicSubstance]="currentEconomicSubstance"
                                        [displayHeader]="false"
                                        [importOnlyMode]="true"
                                        [displayFromCa]="false">
                </app-main-economics-reo>
            </div>

        </div>



        <!--<app-main-economics-reo [corporateEntity]="corporateEntity"
                                [currentEconomicSubstance]="currentEconomicSubstance"
                                [displayHeader]="true"
                                [displayFromCa]="false">
        </app-main-economics-reo>-->



    </div>
</div>
