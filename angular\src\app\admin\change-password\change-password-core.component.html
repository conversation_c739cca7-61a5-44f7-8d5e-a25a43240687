<div>
    <ngFormValidation #passwordFormValidation displayMode="2"></ngFormValidation>

    <form #passwordForm="ngForm">
        <div>
            <label for="CurrentPassword">{{"CurrentPassword" | localize}}</label>
            <div [ngClass]="{ 'input-error': passwordHasError('CurrentPassword') }">
                <input id="CurrentPassword" #currentPasswordInput="ngModel" type="password" name="CurrentPassword" class="form-control"
                       [(ngModel)]="passwordInput.currentPassword" [disabled]="passwordInput.hasTwoFactorCode" required>
            </div>
        </div>
        <div class="margin-top-5">
            <label for="NewPassword">{{"NewPassword" | localize}}</label>
            <div [ngClass]="{ 'input-error': passwordHasError('NewPassword') }">
                <input id="NewPassword" type="password" name="NewPassword" class="form-control" [(ngModel)]="passwordInput.newPassword"
                       #NewPassword="ngModel" required validateEqual="NewPasswordRepeat" [disabled]="passwordInput.hasTwoFactorCode" reverse="true" passwordComplexity>
            </div>
        </div>
        <div class="margin-top-5">
            <label for="NewPasswordRepeat">{{"NewPasswordRepeat" | localize}}</label>
            <div [ngClass]="{ 'input-error': passwordHasError('NewPasswordRepeat') }">
                <input id="NewPasswordRepeat" type="password" name="NewPasswordRepeat" class="form-control" [ngModel]="passwordConfirm"
                       #NewPasswordRepeat="ngModel" validateEqual="NewPassword" [disabled]="passwordInput.hasTwoFactorCode" required>
            </div>
        </div>

        <div>
            <a id="passwordRequirements" style="cursor:pointer" title="{{l('PasswordRequirementsTooltip')}}">Password Requirements</a>
        </div>
        
        <div class="margin-top-5">
            <label for="TwoFactorProvider">{{"Send the Verification Code to" | localize}}</label>
            <div>
                <p-dropdown id="twoFactorProviders" [options]="twoFactorProviders" [disabled]="passwordInput.hasTwoFactorCode"
                            [(ngModel)]="passwordInput.twoFactorProvider" name="TwoFactorProvider">
                </p-dropdown>
            </div>
        </div>
        <div *ngIf="passwordInput.hasTwoFactorCode" class="margin-top-5">
            <label for="TwoFactorCode">{{"Verification Code" | localize}}</label>
            <div>
                <input id="TwoFactorCode" name="TwoFactorCode" class="form-control" [(ngModel)]="passwordInput.twoFactorCode"
                       #twoFactorCodeInput="ngModel" type="text">
            </div>
            <span class="remaining-time-counter">
                {{"RemainingTime" | localize}}: {{passwordDisplayMinutes | number : '1.0-0'}}:{{passwordDisplaySeconds | number : '2.0-0'}}.
            </span>
        </div>
    </form>
    <div class="row-flex-space margin-top">
        <button id="changePasswordCancel" pButton type="button" (click)="resetPasswordForm()" [disabled]="savingPassword" label="{{l('Cancel')}}"></button>
        <button id="changePasswordSave" pButton type="button" (click)="savePassword()" [disabled]="savingPassword" label="{{l('Save')}}"></button>
    </div>
</div>
