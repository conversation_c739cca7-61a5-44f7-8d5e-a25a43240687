.updateTime {
    font-size: 1rem;
    font-weight: 400;
}

.dashboardLink {
    color:rgb(0, 140, 255); 
    cursor: pointer;
}

.ui-tabview-title
{
    color:black;
}

.clickable {
    cursor: pointer;
}
.statsUpdate {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
    margin-right: 0 !important;
    margin-left: auto !important;
}
.es-dashboard {
    height: calc(100vh - 6em);
    padding: 5px;
}
.pagination-controls, .top-right {
    display: flex !important;
    align-items: center !important;
  }
  
  .top-right {
    justify-content: flex-end; /* Align to the right */
    align-items: center !important;
    padding-left: 4px;
  }
  
  .page-size-label {
    display: flex;
    align-items: center;
    margin-right: 10px;
    font-weight: bold;
  }
  .page-size-select select {
    padding: 5px;
    padding-right: 5px;
  }