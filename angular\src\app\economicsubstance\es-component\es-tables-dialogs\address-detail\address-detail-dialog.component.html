<div bsModal #addressmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="outsourcemodal"
    (onShown)="shown()" aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Address Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #addressform="ngForm">
                    <ngFormValidation #addressFormValidation></ngFormValidation>
                    <p-card>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified">
                            <div class="col-flex full-width">
                                <p-checkbox [id]="sameRegisteredAddressdId" [name]="sameRegisteredAddressName"
                                    [ngModelOptions]="{standalone: true}" [(ngModel)]="isSelected"
                                    (onChange)="onSelectCheckbox($event)" binary="true" label="{{l('SameRegisteredAddress')}}">
                                </p-checkbox>                               
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Address Line 1<span class="required">*</span> </label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="details.addressLine1"
                                    required maxlength="255" name="AddressLine1" id="addressLine1id">
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Address Line 2</label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="details.addressLine2"
                                    maxlength="255" name="addressLine2name" id="addressLine2id">
                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Country <span class="required">*</span> </label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="country.countryName"
                                    readonly name="countryaddressname" id="countryaddressid">
                            </div>
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()"
                    label="Cancel"></button>
            </div>
        </div>
    </div>
</div>