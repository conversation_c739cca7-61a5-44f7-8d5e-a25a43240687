import { Component, EventEmitter, Injector, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AuditEntity, RedFlagParameterType, RedFlagReportServiceProxy, RedFlagSettingDto } from '@shared/service-proxies/service-proxies';
import { EntityChangeHistoryModalComponent } from 'app/shared/common/audit/entity-change-history-modal.component';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';

@Component({
    selector: 'redFlagsEdit',
    templateUrl: './red-flags-edit.component.html'
})
export class RedFlagsEditComponent extends AppComponentBase {
    @ViewChild('settingFormValidation', { static: true }) settingFormValidation: NgFormValidationComponent;
    @ViewChild('settingForm', { static: true }) settingForm: FormGroup;
    @ViewChild('entityChangeHistoryModal', { static: true }) entityChangeHistoryModal: EntityChangeHistoryModalComponent;

    @Output() onSave: EventEmitter<any> = new EventEmitter<any>();

    saving = false;
    noneParameterType = RedFlagParameterType.None;
    numberParameterType = RedFlagParameterType.Number;

    redFlagSettingId: string = null;
    redFlagSetting: RedFlagSettingDto = new RedFlagSettingDto();

    constructor(
        injector: Injector,
        private _redFlagReportServiceProxy: RedFlagReportServiceProxy
    ) {
        super(injector);
    }

    ngOnInit() {
        this.settingFormValidation.formGroup = this.settingForm;
    }

    get(id: string): void {
        this.settingForm.reset();

        this.redFlagSettingId = id;
        this._redFlagReportServiceProxy.getRedFlagSetting(id).subscribe(result => {
            this.redFlagSetting = result;
        });
    }

    hasError(fieldName: string): boolean {
        return this.settingFormValidation.fieldHasErrors(fieldName);
    }

    save(): void {
        if (!this.settingFormValidation.isFormValid() || this.redFlagSettingId == null) {
            return;
        }

        this.saving = true;
        this._redFlagReportServiceProxy.updateRedFlagSetting(this.redFlagSetting)
            .pipe(finalize(() => { this.saving = false; }))
            .subscribe(() => {
                this.notify.info(this.l('SavedSuccessfully'));
                this.onSave.emit(null);
                this.clear();
            });
    }

    clear(): void {
        this.settingForm.reset();
        this.redFlagSettingId = null;
        this.redFlagSetting = new RedFlagSettingDto();
    }

    changeHistory(): void {
        this.entityChangeHistoryModal.show(AuditEntity.RedFlagSettings, this.redFlagSettingId);
    }
}
