import { Component, Injector, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { Table } from 'primeng/components/table/table';
import { ModalDirective } from 'ngx-bootstrap';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Paginator } from 'primeng/components/paginator/paginator';
import {  Router } from '@angular/router';
import { finalize } from 'rxjs/operators';
import { DialogService } from 'primeng/api';
import {InformationExchangeServiceProxy} from '@shared/service-proxies/service-proxies';
@Component({
  selector: 'informationHistoryModel',
  templateUrl: './ca-information-history.component.html',
  styleUrls: ['./ca-information-history.component.css']
})
export class CaInformationHistoryComponent extends AppComponentBase {

    @ViewChild('eshistoryDataTable', { static: true }) dataTable: Table;
    @ViewChild('eshistoryPaginator', { static: true }) paginator: Paginator;
    @ViewChild('informationExchageHistoryModal', { static: true }) modal: ModalDirective;

    constructor(injector: Injector,
        private _router: Router,
        private _informationExchangeServiceProxy: InformationExchangeServiceProxy) { super(injector); }
    id: any;
 
    getRecords(event?: LazyLoadEvent) {
        if (this.primengTableHelper.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            return;
        }

        this.primengTableHelper.showLoadingIndicator();
      //  GetInformationHistory
     
        this._informationExchangeServiceProxy.getInformationHistoryList(
            this.id, this.primengTableHelper.getSorting(this.dataTable),
            this.primengTableHelper.getMaxResultCount(this.paginator, event),
            this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }
    show(item:any): void {

        this.id = item.id;
        this.paginator.changePage(0);
        this.modal.show();
    }

    close(): void {
      
        this.modal.hide();
    }
    viewHistrory(item:any)
    {
        let action = 2;
        this._router.navigate([`app/main/cainfoexchage/${item.id}` + '/' +action])
        
    }

    returnDate(obj) {
        if(obj)
        return new Date(obj.format('YYYY-MM-DD HH:mm:ss') + ' UTC');
    }
}
