<div class="row-flex-justified-largefont">
    <label> 9{{headingSeq}}.{{sequenceNo}}a. {{l("PremisesA")}} <label tooltip="Please enter ‘None’ as Address Line 1 if no premises are available."> <u>{{l("PremisesA1")}}</u> </label> {{l("PremisesA2")}} <span class="required">*</span></label>
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="model.show(null,country,true)">Add row</a>
    </div>
</div>

<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="details">
    <ng-template pTemplate="header">
        <tr>
            <th style="width:75%" class="table-text-header">Address</th>
            <th *ngIf="!readOnlyMode" style="width:25%" class="table-text-header">Action</th>

        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
           <td style="width:75%" >
               <label  class="word-wrapping"   [ngClass]="{'p-readonly-label':readOnlyMode}"> {{formatAddress(source)}} </label>
            </td>
              <td style="width:25%" *ngIf="!readOnlyMode" >
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="model.show(source,country,false)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-address-detail-dialog [corporateEntity]="corporateEntity" [readOnlyMode]="readOnlyMode" #model (submitted)="updateSource($event)" ></app-address-detail-dialog>


