{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"Bdo.Ess.Microservice.Reporting.Services/1.0.0": {"dependencies": {"Bdo.Ess.Microservice.Core.EntityFramework.Ca": "1.0.0", "Bdo.Ess.Microservice.Core.EntityFramework.Ctsp": "1.0.0", "Bdo.Ess.Microservice.Core.Services": "1.0.0"}, "runtime": {"Bdo.Ess.Microservice.Reporting.Services.dll": {}}}, "Abp/4.9.0": {"dependencies": {"Castle.Core": "4.4.0", "Castle.LoggingFacility": "5.0.0", "JetBrains.Annotations": "2019.1.1", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.Options": "3.1.8", "Newtonsoft.Json": "12.0.2", "Nito.AsyncEx.Context": "5.0.0", "Nito.AsyncEx.Coordination": "5.0.0", "System.Collections.Immutable": "1.7.1", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.Common": "4.3.0", "System.Linq.Dynamic.Core": "1.0.16", "System.Linq.Queryable": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Claims": "4.3.0", "System.Threading": "4.3.0", "System.Xml.XPath.XmlDocument": "4.3.0", "TimeZoneConverter": "3.2.0"}, "runtime": {"lib/netstandard2.0/Abp.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.AspNetZeroCore/1.2.4": {"dependencies": {"Abp.Web.Common": "4.9.0", "Abp.ZeroCore": "4.9.0"}, "runtime": {"lib/netcoreapp2.2/Abp.AspNetZeroCore.dll": {"assemblyVersion": "1.2.4.0", "fileVersion": "1.2.4.0"}}}, "Abp.AutoMapper/4.9.0": {"dependencies": {"Abp": "4.9.0", "AutoMapper": "9.0.0", "AutoMapper.Collection": "5.0.0"}, "runtime": {"lib/netstandard2.0/Abp.AutoMapper.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.EntityFramework.Common/4.9.0": {"dependencies": {"Abp": "4.9.0"}, "runtime": {"lib/netstandard2.0/Abp.EntityFramework.Common.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.EntityFrameworkCore/4.9.0": {"dependencies": {"Abp.EntityFramework.Common": "4.9.0", "Microsoft.EntityFrameworkCore": "3.1.8", "Microsoft.EntityFrameworkCore.Relational": "3.1.8"}, "runtime": {"lib/netstandard2.0/Abp.EntityFrameworkCore.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.MailKit/4.9.0": {"dependencies": {"Abp": "4.9.0", "MailKit": "2.2.0", "System.Runtime": "4.3.1"}, "runtime": {"lib/netstandard2.0/Abp.MailKit.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.Web.Common/4.9.0": {"dependencies": {"Abp": "4.9.0", "NUglify": "1.5.13"}, "runtime": {"lib/netstandard2.0/Abp.Web.Common.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.Zero.Common/4.9.0": {"dependencies": {"Abp": "4.9.0"}, "runtime": {"lib/netstandard2.0/Abp.Zero.Common.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.Zero.Ldap/4.9.0": {"dependencies": {"Abp.Zero.Common": "4.9.0", "System.DirectoryServices.AccountManagement": "4.5.0", "System.DirectoryServices.Protocols": "4.5.0"}, "runtime": {"lib/netstandard2.0/Abp.Zero.Ldap.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.ZeroCore/4.9.0": {"dependencies": {"Abp.Zero.Common": "4.9.0", "Microsoft.AspNetCore.Identity": "2.2.0"}, "runtime": {"lib/netstandard2.0/Abp.ZeroCore.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.ZeroCore.EntityFrameworkCore/4.9.0": {"dependencies": {"Abp.EntityFrameworkCore": "4.9.0", "Abp.ZeroCore": "4.9.0"}, "runtime": {"lib/netstandard2.0/Abp.ZeroCore.EntityFrameworkCore.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.ZeroCore.IdentityServer4/4.9.0": {"dependencies": {"Abp.AutoMapper": "4.9.0", "Abp.ZeroCore": "4.9.0", "IdentityServer4": "2.5.0", "IdentityServer4.AspNetIdentity": "2.5.0"}, "runtime": {"lib/netstandard2.0/Abp.ZeroCore.IdentityServer4.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Abp.ZeroCore.IdentityServer4.EntityFrameworkCore/4.9.0": {"dependencies": {"Abp.ZeroCore.EntityFrameworkCore": "4.9.0", "Abp.ZeroCore.IdentityServer4": "4.9.0"}, "runtime": {"lib/netstandard2.0/Abp.ZeroCore.IdentityServer4.EntityFrameworkCore.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "AutoMapper/9.0.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "System.Reflection.Emit": "4.3.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "AutoMapper.Collection/5.0.0": {"dependencies": {"AutoMapper": "9.0.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.Collection.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp2.1/Azure.Core.dll": {"assemblyVersion": "1.25.0.0", "fileVersion": "1.2500.22.33004"}}}, "Azure.Core.Amqp/1.2.0": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.200.21.35604"}}}, "Azure.Identity/1.4.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.30.1", "Microsoft.Identity.Client.Extensions.Msal": "2.18.4", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.400.21.26202"}}}, "Azure.Messaging.ServiceBus/7.11.1": {"dependencies": {"Azure.Core": "1.25.0", "Azure.Core.Amqp": "1.2.0", "Microsoft.Azure.Amqp": "2.5.12", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.11.1.0", "fileVersion": "7.1100.122.55803"}}}, "BraintreeHttp-Dotnet/0.3.0": {"runtime": {"lib/netstandard2.0/BraintreeHttp-Dotnet.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Castle.Core/4.4.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.4.0.0"}}}, "Castle.LoggingFacility/5.0.0": {"dependencies": {"Castle.Windsor": "5.0.0", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.6/Castle.Facilities.Logging.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Castle.Windsor/5.0.0": {"dependencies": {"Castle.Core": "4.4.0", "Microsoft.Extensions.DependencyModel": "3.1.6", "NETStandard.Library": "1.6.1", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard1.6/Castle.Windsor.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Castle.Windsor.MsDependencyInjection/3.3.1": {"dependencies": {"Castle.Core": "4.4.0", "Castle.Windsor": "5.0.0", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}, "runtime": {"lib/netstandard2.0/Castle.Windsor.MsDependencyInjection.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}}, "DocumentFormat.OpenXml/2.12.1": {"dependencies": {"System.IO.Packaging": "4.7.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.12.1.0", "fileVersion": "2.12.1.0"}}}, "EFCore.BulkExtensions/3.2.5": {"dependencies": {"Microsoft.Data.SqlClient": "2.0.1", "Microsoft.EntityFrameworkCore": "3.1.8", "Microsoft.EntityFrameworkCore.Relational": "3.1.8", "Microsoft.EntityFrameworkCore.Sqlite": "3.1.8"}, "runtime": {"lib/netstandard2.0/EFCore.BulkExtensions.dll": {"assemblyVersion": "3.2.5.0", "fileVersion": "3.2.5.0"}}}, "EPPlus/*******": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0", "System.Drawing.Common": "4.7.0", "System.Security.Cryptography.Pkcs": "4.5.2", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/EPPlus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IdentityModel/3.10.10": {"dependencies": {"Newtonsoft.Json": "12.0.2", "System.Text.Encodings.Web": "4.7.2"}, "runtime": {"lib/netstandard2.0/IdentityModel.dll": {"assemblyVersion": "3.10.10.0", "fileVersion": "3.10.10.0"}}}, "IdentityServer4/2.5.0": {"dependencies": {"IdentityModel": "3.10.10", "IdentityServer4.Storage": "2.5.0", "Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "2.1.0", "Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.Http": "2.2.0", "Microsoft.Extensions.Logging": "3.1.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.0", "System.IdentityModel.Tokens.Jwt": "5.6.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/IdentityServer4.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.0.0"}}}, "IdentityServer4.AspNetIdentity/2.5.0": {"dependencies": {"IdentityServer4": "2.5.0", "Microsoft.AspNetCore.Identity": "2.2.0"}, "runtime": {"lib/netstandard2.0/IdentityServer4.AspNetIdentity.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.0.0"}}}, "IdentityServer4.Storage/2.5.0": {"dependencies": {"IdentityModel": "3.10.10", "Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/IdentityServer4.Storage.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.0.0"}}}, "JetBrains.Annotations/2019.1.1": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "2019.1.1.0", "fileVersion": "2019.1.1.0"}}}, "MailKit/2.2.0": {"dependencies": {"MimeKit": "2.2.0", "System.Net.NameResolution": "4.3.0", "System.Net.Security": "4.3.2", "System.Runtime.Serialization.Primitives": "4.3.0"}, "runtime": {"lib/netstandard2.0/MailKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0"}}, "Microsoft.AspNetCore.Authentication/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.OAuth/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0", "Newtonsoft.Json": "12.0.2"}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.1.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.18136"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Connections.Abstractions/3.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "3.0.0", "System.IO.Pipelines": "4.6.0"}}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0"}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8", "Microsoft.Win32.Registry": "4.7.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "3.0.0", "Microsoft.Extensions.Configuration.Abstractions": "3.1.8"}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.8", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "3.0.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Connections/1.0.15": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Connections.Common": "3.0.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.WebSockets": "2.1.7", "Newtonsoft.Json": "12.0.2", "System.Net.WebSockets.WebSocketProtocol": "4.5.3"}}, "Microsoft.AspNetCore.Http.Connections.Client/3.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46502"}}}, "Microsoft.AspNetCore.Http.Connections.Common/3.0.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "3.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/3.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8", "System.IO.Pipelines": "4.6.0"}}, "Microsoft.AspNetCore.Identity/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Identity.Core": "2.2.0"}}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.AspNetCore.Mvc/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.DependencyInjection": "3.1.8"}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.DependencyModel": "3.1.6", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0", "System.ComponentModel.Annotations": "4.7.0"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Localization": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.FileProviders.Composite": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0", "Microsoft.Extensions.Primitives": "3.1.8"}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Newtonsoft.Json.Bson": "1.0.1"}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.1.8", "Newtonsoft.Json": "12.0.2", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.2.0", "Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "System.Reflection.Emit": "4.3.0", "System.Threading.Channels": "4.5.0"}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.1.0", "Newtonsoft.Json": "12.0.2"}}, "Microsoft.AspNetCore.WebSockets/2.1.7": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Options": "3.1.8", "System.Net.WebSockets.WebSocketProtocol": "4.5.3"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Azure.Amqp/2.5.12": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "2.4.0.0", "fileVersion": "*******"}}}, "Microsoft.Azure.KeyVault/3.0.4": {"dependencies": {"Microsoft.Azure.KeyVault.WebKey": "3.0.4", "Microsoft.Rest.ClientRuntime": "2.3.21", "Microsoft.Rest.ClientRuntime.Azure": "3.3.19", "Newtonsoft.Json": "12.0.2", "System.Net.Http": "4.3.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.KeyVault.dll": {"assemblyVersion": "3.0.4.0", "fileVersion": "3.0.419.36903"}}}, "Microsoft.Azure.KeyVault.WebKey/3.0.4": {"dependencies": {"Microsoft.Rest.ClientRuntime": "2.3.21", "Microsoft.Rest.ClientRuntime.Azure": "3.3.19", "Newtonsoft.Json": "12.0.2", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Net.Http": "4.3.4", "System.Runtime": "4.3.1", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.KeyVault.WebKey.dll": {"assemblyVersion": "3.0.4.0", "fileVersion": "3.0.419.36903"}}}, "Microsoft.Azure.SignalR/1.18.0": {"dependencies": {"Azure.Identity": "1.4.0", "Microsoft.AspNetCore.Connections.Abstractions": "3.0.0", "Microsoft.AspNetCore.Http.Connections.Client": "3.0.0", "Microsoft.AspNetCore.Http.Connections.Common": "3.0.0", "Microsoft.Azure.SignalR.Protocols": "1.18.0", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Http": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Rest.ClientRuntime": "2.3.21", "Newtonsoft.Json": "12.0.2", "System.IO.Pipelines": "4.6.0", "System.IdentityModel.Tokens.Jwt": "5.6.0"}, "runtime": {"lib/netcoreapp3.0/Microsoft.Azure.SignalR.Common.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.0/Microsoft.Azure.SignalR.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Azure.SignalR.Protocols/1.18.0": {"dependencies": {"Azure.Identity": "1.4.0", "Microsoft.AspNetCore.Connections.Abstractions": "3.0.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Connections": "1.0.15", "Microsoft.AspNetCore.Http.Connections.Common": "3.0.0", "Microsoft.AspNetCore.WebSockets": "2.1.7", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Http": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Primitives": "3.1.8", "Microsoft.Rest.ClientRuntime": "2.3.21", "Newtonsoft.Json": "12.0.2", "System.Buffers": "4.5.1", "System.IO.Pipelines": "4.6.0", "System.IdentityModel.Tokens.Jwt": "5.6.0", "System.Memory": "4.5.4", "System.Net.WebSockets.WebSocketProtocol": "4.5.3", "System.Runtime.CompilerServices.Unsafe": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.SignalR.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Bcl.TimeProvider/8.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.7.1", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "4.7.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/2.0.1": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.0.1", "Microsoft.Identity.Client": "4.30.1", "Microsoft.IdentityModel.JsonWebTokens": "5.6.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider/1.1.1": {"dependencies": {"Microsoft.Azure.KeyVault": "3.0.4", "Microsoft.Azure.KeyVault.WebKey": "3.0.4", "Microsoft.Data.SqlClient": "2.0.1", "Microsoft.Rest.ClientRuntime": "2.3.21", "Microsoft.Rest.ClientRuntime.Azure": "3.3.19"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider.dll": {"assemblyVersion": "1.11.20063.2", "fileVersion": "1.11.20063.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "1.11.20063.2", "fileVersion": "1.11.20063.2"}, "runtimes/win/lib/netcoreapp2.1/Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "1.11.20063.2", "fileVersion": "1.11.20063.2"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.0.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "2.0.1.0"}, "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.0.1.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.0.1.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.0.1.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.Data.Sqlite.Core/3.1.8": {"dependencies": {"SQLitePCLRaw.core": "2.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42012"}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}}, "Microsoft.EntityFrameworkCore/3.1.8": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.Bcl.HashCode": "1.1.0", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.8", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.8", "Microsoft.Extensions.Caching.Memory": "3.1.8", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Logging": "3.1.8", "System.Collections.Immutable": "1.7.1", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42012"}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.8": {"runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42012"}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.8": {}, "Microsoft.EntityFrameworkCore.Design/2.2.6": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "3.1.8"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "2.2.6.0", "fileVersion": "2.2.6.19169"}}}, "Microsoft.EntityFrameworkCore.Relational/3.1.8": {"dependencies": {"Microsoft.EntityFrameworkCore": "3.1.8"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42012"}}}, "Microsoft.EntityFrameworkCore.Sqlite/3.1.8": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "3.1.8", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/3.1.8": {"dependencies": {"Microsoft.Data.Sqlite.Core": "3.1.8", "Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.EntityFrameworkCore.Relational": "3.1.8", "Microsoft.Extensions.DependencyModel": "3.1.6"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42012"}}}, "Microsoft.EntityFrameworkCore.SqlServer/3.1.1": {"dependencies": {"Microsoft.Data.SqlClient": "2.0.1", "Microsoft.EntityFrameworkCore.Relational": "3.1.8"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.100.119.61403"}}}, "Microsoft.Extensions.Caching.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Caching.Memory/3.1.8": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Configuration/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.4": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.8"}}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.8", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.8", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Newtonsoft.Json": "12.0.2"}}, "Microsoft.Extensions.Configuration.KeyPerFile/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.8", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0"}}, "Microsoft.Extensions.DependencyInjection/3.1.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.8": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.DependencyModel/3.1.6": {"dependencies": {"System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8"}}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0"}}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0"}}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8"}}, "Microsoft.Extensions.Http/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.Extensions.Http.Polly/2.2.0": {"dependencies": {"Microsoft.Extensions.Http": "2.2.0", "Polly": "8.2.0", "Polly.Extensions.Http": "2.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Identity.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.2.0", "Microsoft.Extensions.Logging": "3.1.8", "Microsoft.Extensions.Options": "3.1.8", "System.ComponentModel.Annotations": "4.7.0"}}, "Microsoft.Extensions.Localization/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {}, "Microsoft.Extensions.Logging/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.8", "Microsoft.Extensions.DependencyInjection": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.8": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Logging.Debug/3.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.100.119.61404"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/3.1.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Primitives": "3.1.8"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.Configuration.Binder": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}}, "Microsoft.Extensions.Primitives/3.1.8": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "3.1.8.0", "fileVersion": "3.100.820.42004"}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Options": "3.1.8", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Identity.Client/4.30.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.30.1.0", "fileVersion": "4.30.1.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.18.4": {"dependencies": {"Microsoft.Identity.Client": "4.30.1", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.18.4.0", "fileVersion": "2.18.4.0"}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.8": {"dependencies": {"Microsoft.CSharp": "4.5.0", "NETStandard.Library": "1.6.1", "System.ComponentModel.TypeConverter": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Net.Http": "4.3.4", "System.Private.Uri": "4.3.2", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Runtime.Serialization.Json": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Security.SecureString": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {"assemblyVersion": "5.2.8.0", "fileVersion": "5.2.8.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Logging/5.6.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Protocols/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.6.0", "Microsoft.IdentityModel.Tokens": "5.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.6.0", "Newtonsoft.Json": "12.0.2", "System.IdentityModel.Tokens.Jwt": "5.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Tokens/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.6.0", "Newtonsoft.Json": "12.0.2", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.8", "System.Buffers": "4.5.1"}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Rest.ClientRuntime/2.3.21": {"dependencies": {"Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Rest.ClientRuntime.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.21.0"}}}, "Microsoft.Rest.ClientRuntime.Azure/3.3.19": {"dependencies": {"Microsoft.Rest.ClientRuntime": "2.3.21", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Rest.ClientRuntime.Azure.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "MimeKit/2.2.0": {"dependencies": {"Portable.BouncyCastle": "1.8.5", "System.Data.Common": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netstandard2.0/MimeKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.2.23222"}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"NETStandard.Library": "1.6.1", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.1.20722"}}}, "Nito.AsyncEx.Context/5.0.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.AsyncEx.Coordination/5.0.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.0.0", "Nito.Collections.Deque": "1.0.4", "Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.AsyncEx.Tasks/5.0.0": {"dependencies": {"Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.Collections.Deque/1.0.4": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "1.0.4.0", "fileVersion": "1.0.4.0"}}}, "Nito.Disposables/2.0.0": {"dependencies": {"System.Collections.Immutable": "1.7.1"}, "runtime": {"lib/netstandard2.0/Nito.Disposables.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "NUglify/1.5.13": {"runtime": {"lib/netstandard2.0/NUglify.dll": {"assemblyVersion": "1.5.13.0", "fileVersion": "1.5.13.0"}}}, "OfficeOpenXml.Core.ExcelPackage/1.0.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.IO.Packaging": "4.7.0", "System.Xml.XPath": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0"}, "runtime": {"lib/netstandard1.6/OfficeOpenXml.Core.ExcelPackage.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PayPalCheckoutSdk/1.0.2": {"dependencies": {"BraintreeHttp-Dotnet": "0.3.0"}, "runtime": {"lib/netstandard2.0/PayPalCheckoutSdk.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly/8.2.0": {"dependencies": {"Polly.Core": "8.2.0"}, "runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.0.2702"}}}, "Polly.Contrib.WaitAndRetry/1.1.1": {"runtime": {"lib/netstandard2.0/Polly.Contrib.WaitAndRetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.1.0"}}}, "Polly.Core/8.2.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.Bcl.TimeProvider": "8.0.0", "System.ComponentModel.Annotations": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.0.2702"}}}, "Polly.Extensions.Http/2.0.1": {"dependencies": {"Polly": "8.2.0"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.1.0"}}}, "Portable.BouncyCastle/1.8.5": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.5.0", "fileVersion": "1.8.5.50"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.2": {"dependencies": {"SQLitePCLRaw.core": "2.0.2", "SQLitePCLRaw.lib.e_sqlite3": "2.0.2", "SQLitePCLRaw.provider.dynamic_cdecl": "2.0.2"}, "runtime": {"lib/netcoreapp3.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.2.669", "fileVersion": "2.0.2.669"}, "lib/netcoreapp3.0/SQLitePCLRaw.nativelibrary.dll": {"assemblyVersion": "2.0.2.669", "fileVersion": "2.0.2.669"}}}, "SQLitePCLRaw.core/2.0.2": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.2.669", "fileVersion": "2.0.2.669"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.2": {"runtimeTargets": {"runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.2": {"dependencies": {"SQLitePCLRaw.core": "2.0.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {"assemblyVersion": "2.0.2.669", "fileVersion": "2.0.2.669"}}}, "Stripe.net/27.16.1": {"dependencies": {"Newtonsoft.Json": "12.0.2", "System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/Stripe.net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.1": {"runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.700.19.56404"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/4.7.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Diagnostics.FileVersionInfo/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.2", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO.FileSystem.AccessControl": "4.5.0", "System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.DirectoryServices.AccountManagement/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.DirectoryServices": "4.5.0", "System.DirectoryServices.Protocols": "4.5.0", "System.IO.FileSystem.AccessControl": "4.5.0", "System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.DirectoryServices.Protocols/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/5.6.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "5.6.0", "Microsoft.IdentityModel.Tokens": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "5.6.0.0", "fileVersion": "5.6.0.61018"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Packaging/4.7.0": {"runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IO.Pipelines/4.6.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.0.16": {"runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.7.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Security/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Security": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Net.WebSockets.WebSocketProtocol/4.5.3": {"runtime": {"lib/netcoreapp2.1/System.Net.WebSockets.WebSocketProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.27129.4"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Private.Uri/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/1.4.2": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Immutable": "1.7.1", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Json/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/4.5.2": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.2", "System.Security.Permissions": "4.7.0"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Security.SecureString/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {"runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.21.11602"}}}, "System.Text.Json/4.7.2": {"runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"assemblyVersion": "4.0.1.2", "fileVersion": "4.700.20.21406"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/4.5.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.ValueTuple/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "TimeZoneConverter/3.2.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Twilio/5.31.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.6.0", "Newtonsoft.Json": "12.0.2", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.6.0"}, "runtime": {"lib/netstandard2.0/Twilio.dll": {"assemblyVersion": "5.31.0.0", "fileVersion": "5.31.0.0"}}}, "Bdo.Ess.Application.Shared/7.2.0": {"dependencies": {"Abp.Web.Common": "4.9.0", "Bdo.Ess.Core.Shared": "7.2.0", "Bdo.Ess.Dtos": "1.0.0", "Bdo.Ess.Services": "1.0.0", "System.ComponentModel.Annotations": "4.7.0"}, "runtime": {"Bdo.Ess.Application.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Common/1.0.0": {"dependencies": {"Abp": "4.9.0", "Microsoft.AspNetCore.SignalR.Core": "1.1.0", "Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider": "1.1.1", "Microsoft.Extensions.Configuration": "3.1.8", "Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.KeyPerFile": "2.2.0", "Microsoft.Extensions.Http.Polly": "2.2.0", "Microsoft.IdentityModel.Clients.ActiveDirectory": "5.2.8", "Polly": "8.2.0", "Polly.Contrib.WaitAndRetry": "1.1.1"}, "runtime": {"Bdo.Ess.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Core/7.2.0": {"dependencies": {"Abp.AspNetZeroCore": "1.2.4", "Abp.AutoMapper": "4.9.0", "Abp.MailKit": "4.9.0", "Abp.Zero.Ldap": "4.9.0", "Abp.ZeroCore.IdentityServer4.EntityFrameworkCore": "4.9.0", "Azure.Messaging.ServiceBus": "7.11.1", "Bdo.Ess.Application.Shared": "7.2.0", "Bdo.Ess.Common": "1.0.0", "Bdo.Ess.Core.Shared": "7.2.0", "Bdo.Ess.Security.RateLimiter": "1.0.0", "Bdo.Ess.Shared": "1.0.0", "Castle.Windsor.MsDependencyInjection": "3.3.1", "DocumentFormat.OpenXml": "2.12.1", "EPPlus": "*******", "Microsoft.AspNetCore.SignalR.Core": "1.1.0", "Microsoft.Azure.SignalR": "1.18.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.4", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "OfficeOpenXml.Core.ExcelPackage": "1.0.0", "PayPalCheckoutSdk": "1.0.2", "Stripe.net": "27.16.1", "System.ComponentModel.Annotations": "4.7.0", "TimeZoneConverter": "3.2.0", "Twilio": "5.31.0"}, "runtime": {"Bdo.Ess.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Core.Shared/7.2.0": {"dependencies": {"Abp": "4.9.0", "Abp.Zero.Common": "4.9.0", "Abp.ZeroCore": "4.9.0"}, "runtime": {"Bdo.Ess.Core.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Dtos/1.0.0": {"dependencies": {"Bdo.Ess.Common": "1.0.0"}, "runtime": {"Bdo.Ess.Dtos.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.EntityFrameworkCore/7.2.0": {"dependencies": {"Bdo.Ess.Core": "7.2.0", "Microsoft.EntityFrameworkCore.Design": "2.2.6", "Microsoft.EntityFrameworkCore.SqlServer": "3.1.1"}, "runtime": {"Bdo.Ess.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Microservice.Core/1.0.0": {"dependencies": {"Bdo.Ess.Common": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8"}, "runtime": {"Bdo.Ess.Microservice.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Microservice.Core.EntityFramework/1.0.0": {"dependencies": {"Bdo.Ess.Microservice.Core": "1.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "3.1.1", "Microsoft.Extensions.Logging.Debug": "3.1.1"}, "runtime": {"Bdo.Ess.Microservice.Core.EntityFramework.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Microservice.Core.EntityFramework.Ca/1.0.0": {"dependencies": {"Bdo.Ess.EntityFrameworkCore": "7.2.0", "Bdo.Ess.Microservice.Core.EntityFramework": "1.0.0"}, "runtime": {"Bdo.Ess.Microservice.Core.EntityFramework.Ca.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Microservice.Core.EntityFramework.Ctsp/1.0.0": {"dependencies": {"Bdo.Ess.Microservice.Core.EntityFramework": "1.0.0", "EFCore.BulkExtensions": "3.2.5"}, "runtime": {"Bdo.Ess.Microservice.Core.EntityFramework.Ctsp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Microservice.Core.Services/1.0.0": {"dependencies": {"AutoMapper": "9.0.0", "Bdo.Ess.Microservice.Core.EntityFramework": "1.0.0", "Bdo.Ess.Services": "1.0.0"}, "runtime": {"Bdo.Ess.Microservice.Core.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Security.RateLimiter/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.Extensions.Caching.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Logging": "3.1.8", "Microsoft.Extensions.Options": "3.1.8"}, "runtime": {"Bdo.Ess.Security.RateLimiter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Services/1.0.0": {"dependencies": {"Bdo.Ess.Dtos": "1.0.0", "Bdo.Ess.Shared": "1.0.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0"}, "runtime": {"Bdo.Ess.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Bdo.Ess.Shared/1.0.0": {"dependencies": {"Bdo.Ess.Dtos": "1.0.0"}, "runtime": {"Bdo.Ess.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Bdo.Ess.Microservice.Reporting.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Abp/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-KCZeDwJjPIWqrKJHY36OH+A2Z49fhJGQVgkPyrdzX4gcc12O5vQwlDq0OH4vpGaUAt8dbR5nfEKzS2PheKbmZg==", "path": "abp/4.9.0", "hashPath": "abp.4.9.0.nupkg.sha512"}, "Abp.AspNetZeroCore/1.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-OmkAhu5HqekJAM+MBo5uiy6iTUDDLOXN7YVx9KRmFZ3GkprQ1Bb9nJMGv51BHcCbJGu14GRh/h7mbNk8MvqYsg==", "path": "abp.aspnetzerocore/1.2.4", "hashPath": "abp.aspnetzerocore.1.2.4.nupkg.sha512"}, "Abp.AutoMapper/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wwz2tsgcew/qPps3tiV7suLpMZ7/h5vgGGxSTUiCV7btK/2mssM7vsx8glGe4CBPs5juFJ6oBHmRN5AJJAAHzQ==", "path": "abp.automapper/4.9.0", "hashPath": "abp.automapper.4.9.0.nupkg.sha512"}, "Abp.EntityFramework.Common/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-A2QB/ENPwzEVxNCsWHD/bT3NM+yy4ol2Hpk754HgQ0YPHIW3t/2VmJRsgPp+PWGZ4smeIsPlUCH7LVqUrIsPRw==", "path": "abp.entityframework.common/4.9.0", "hashPath": "abp.entityframework.common.4.9.0.nupkg.sha512"}, "Abp.EntityFrameworkCore/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-CGKlz3LOOJqoxsw7yZ3b0e4vfzSz9GfE3K8WssY+NruF2Sid55VjETinXm3iLeoeUJM58itMGuBwnNqG8ZrjLA==", "path": "abp.entityframeworkcore/4.9.0", "hashPath": "abp.entityframeworkcore.4.9.0.nupkg.sha512"}, "Abp.MailKit/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-/z8gA9zyN0S2zqqc9UHwJVtw/dOdL79VVhVClgrry8N84QM276+9xbY1XnrFf7meG0LcJvvzsbGPhyz5Xwpj5Q==", "path": "abp.mailkit/4.9.0", "hashPath": "abp.mailkit.4.9.0.nupkg.sha512"}, "Abp.Web.Common/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-C4RyhJ18kc2324mKz6OsUFPsUkhehesAI554AqkIaJYHXLdQrV+gVe8sWBUwDD/NUu3p0qTYcK4mwQZgBHLRjA==", "path": "abp.web.common/4.9.0", "hashPath": "abp.web.common.4.9.0.nupkg.sha512"}, "Abp.Zero.Common/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-gzoCbBV1vhpjL3fnRCb9C6kgkEctYWE3M4FYVARQCh51bWy1gNOaqHkG3JlVLZaAdz2LWNS/y91lw2RAoS+9xw==", "path": "abp.zero.common/4.9.0", "hashPath": "abp.zero.common.4.9.0.nupkg.sha512"}, "Abp.Zero.Ldap/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-bxwrVmCoowuCNKocuiqUFfSU7sBV8U7eRs2/FM8VG07QGZ1WyPtECp9lcO5fZmXDbLZK4t5vO4RWWyoqt5A90Q==", "path": "abp.zero.ldap/4.9.0", "hashPath": "abp.zero.ldap.4.9.0.nupkg.sha512"}, "Abp.ZeroCore/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-xyIJXYLddJVqeSkqubHA9dOCrySIa+MrE3XpOd88NzTjOO+qXp2Ya/tcLjCVM1rUf6iAOaH57SXWo3aLotYFcA==", "path": "abp.zerocore/4.9.0", "hashPath": "abp.zerocore.4.9.0.nupkg.sha512"}, "Abp.ZeroCore.EntityFrameworkCore/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-l0zt5EnPL5xyoo+gGtJc4uQS6xTTRIN9aXnA8kHozd24BRKBmvxtUePnVJ6zB5EbtmJDMFAYXaZ7T3Ryb2gOrA==", "path": "abp.zerocore.entityframeworkcore/4.9.0", "hashPath": "abp.zerocore.entityframeworkcore.4.9.0.nupkg.sha512"}, "Abp.ZeroCore.IdentityServer4/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-RSJVgD9XL/msozw/N98F7fO23SJdvjip446XghV+L+7e3t2ndmwIfn6zPLO79OV2GCy5meBs/Nv+/CZGDsfsqg==", "path": "abp.zerocore.identityserver4/4.9.0", "hashPath": "abp.zerocore.identityserver4.4.9.0.nupkg.sha512"}, "Abp.ZeroCore.IdentityServer4.EntityFrameworkCore/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-hePHUur6AbEv9J5GONOoCEAVcN1Bs78bRctccxdE4imL4GpTN7RaidPUoedhuOOBvUTtHo2mwJuGKauYWR6zfg==", "path": "abp.zerocore.identityserver4.entityframeworkcore/4.9.0", "hashPath": "abp.zerocore.identityserver4.entityframeworkcore.4.9.0.nupkg.sha512"}, "AutoMapper/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xCqvoxT4HIrNY/xlXG9W+BA/awdrhWvMTKTK/igkGSRbhOhpl3Q8O8Gxlhzjc9JsYqE7sS6AxgyuUUvZ6R5/Bw==", "path": "automapper/9.0.0", "hashPath": "automapper.9.0.0.nupkg.sha512"}, "AutoMapper.Collection/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PTcaaXG1UpWtVlgjdskYA6AJqxvakNvfNS78jEkrBqqkOrM/AoeAO1/aajHqbyQ90qQcsqE3EUar73E6Ri4paA==", "path": "automapper.collection/5.0.0", "hashPath": "automapper.collection.5.0.0.nupkg.sha512"}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Core.Amqp/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vrF4isvhwdZspzorLwYhukXz3DA8/ONSnZUIBAqBtOCzsDNUgAsuILbCzvtDrn2oDxyq7DZx5Nh81pe0BeWmDQ==", "path": "azure.core.amqp/1.2.0", "hashPath": "azure.core.amqp.1.2.0.nupkg.sha512"}, "Azure.Identity/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-vvjdoDQb9WQyLkD1Uo5KFbwlW7xIsDMihz3yofskym2SimXswbSXuK7QSR1oHnBLBRMdamnVHLpSKQZhJUDejg==", "path": "azure.identity/1.4.0", "hashPath": "azure.identity.1.4.0.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-ioGedXeH8KK4HdTDEyOzdgNbEXGstGItVljI1EKYsz08sgwej6LpODCZmwPR2ui1fjXBWt8Zea0RJT4d9LwlMg==", "path": "azure.messaging.servicebus/7.11.1", "hashPath": "azure.messaging.servicebus.7.11.1.nupkg.sha512"}, "BraintreeHttp-Dotnet/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ARHcstlJ2uRxqJVAxhJ7PJbzsDD9us+docpKHuay6psUVpQJo4cH0negfo8rv/iEakGhx0Rxgp/HUu6ZLa+lsA==", "path": "braintreehttp-dotnet/0.3.0", "hashPath": "braintreehttp-dotnet.0.3.0.nupkg.sha512"}, "Castle.Core/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b5rRL5zeaau1y/5hIbI+6mGw3cwun16YjkHZnV9RRT5UyUIFsgLmNXJ0YnIN9p8Hw7K7AbG1q1UclQVU3DinAQ==", "path": "castle.core/4.4.0", "hashPath": "castle.core.4.4.0.nupkg.sha512"}, "Castle.LoggingFacility/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tVAGQQMnrvg+JZ3isghCOg7aDlt1VMgBgegb7jBwSJ6yfbAwK4Qdrw/kezcFnmgZey6dQDQZo5wNEZpok52J8A==", "path": "castle.loggingfacility/5.0.0", "hashPath": "castle.loggingfacility.5.0.0.nupkg.sha512"}, "Castle.Windsor/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kfaIV95ZnYQj6gjq1G9o81gBuxo4o2/yCwgaylIeytcK3jtKJe4RGN6KxSV6viwvKKn+XQvblrBHk1qQyD/YtQ==", "path": "castle.windsor/5.0.0", "hashPath": "castle.windsor.5.0.0.nupkg.sha512"}, "Castle.Windsor.MsDependencyInjection/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-AK5ClaDRtP6YxSdCZTngnfMYstRkglxKrYrfWdq6H9QPQguB5Fdq300euqsNMd3CO4qn6tp2Ln4AdrQdmEkL0w==", "path": "castle.windsor.msdependencyinjection/3.3.1", "hashPath": "castle.windsor.msdependencyinjection.3.3.1.nupkg.sha512"}, "DocumentFormat.OpenXml/2.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-9nqz+/djR7Upcve7JSrxli+djUh5NRT5GxzvTe43dQqcGdLQzUBTd6fXZtKlaEbsfkswHtbQTEXwnAgGbmRbOA==", "path": "documentformat.openxml/2.12.1", "hashPath": "documentformat.openxml.2.12.1.nupkg.sha512"}, "EFCore.BulkExtensions/3.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-SyByXzYqkjgKReX/Qe2iP5VNQ2PBNcdHttICSIQ5nZoC5Rih6qzbOwaGqwo3fHax4uaWh+JkKohCBXtW6lkv2g==", "path": "efcore.bulkextensions/3.2.5", "hashPath": "efcore.bulkextensions.3.2.5.nupkg.sha512"}, "EPPlus/*******": {"type": "package", "serviceable": true, "sha512": "sha512-JoEDkHEcYio3uV8T9+anjfB6x6hirEW9zdO4Ibyd8BDrct0tCp6vMqvKFpAcPNE6TVRG03y1bskFlJXbXiCJxg==", "path": "epplus/*******", "hashPath": "epplus.*******.nupkg.sha512"}, "IdentityModel/3.10.10": {"type": "package", "serviceable": true, "sha512": "sha512-1CZDkFEZC5hU615GrTXLoamLlLlZVNURnjbdjELw3keSRcPufSIwP5NI0f8quUBV/beuKy9yIqYemmMIDIY87w==", "path": "identitymodel/3.10.10", "hashPath": "identitymodel.3.10.10.nupkg.sha512"}, "IdentityServer4/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-IgXtZJnyrdC+zv7DSk0e0teDbNurid1yYOj/DWf5JitN7qUz+xmUMPIh3mXy7IUyLofGnmFfvbOfoUKZY5sy6g==", "path": "identityserver4/2.5.0", "hashPath": "identityserver4.2.5.0.nupkg.sha512"}, "IdentityServer4.AspNetIdentity/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-go54gqRZuRCuP5edwM8vS3DsFW40i9E1OC+TZI9OA3c4jrJQwSBs3h+FAwDijyY/Kj9I4timN92Q5lyKBb16Eg==", "path": "identityserver4.aspnetidentity/2.5.0", "hashPath": "identityserver4.aspnetidentity.2.5.0.nupkg.sha512"}, "IdentityServer4.Storage/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-phhkhDcGfxLquNZA2biFt5S5XIUy0QUeD1c0dMfsdW3Lf4OdW47eO/YbpR3rxhBXKem25NmS6W3OAOXo94TIyw==", "path": "identityserver4.storage/2.5.0", "hashPath": "identityserver4.storage.2.5.0.nupkg.sha512"}, "JetBrains.Annotations/2019.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y4RFn+HZ7BekqIx+uXuVAvh80quYNiu86oRG2QztmFXQ2mm1lprb7xNawZdFfLxBAh8BQUkSQdO0Itanal8DbQ==", "path": "jetbrains.annotations/2019.1.1", "hashPath": "jetbrains.annotations.2019.1.1.nupkg.sha512"}, "MailKit/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TRmBG3fcBDnRsvbxmlQ7S9NqpMm0flAD5MGQCmzIsXiJvK0EYOsGBS+/buWjCSpAKqddBsUf7OVS5a6FyGuR4A==", "path": "mailkit/2.2.0", "hashPath": "mailkit.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fVQsSXNZz38Ysx8iKwwqfOLHhLrAeKEMBS5Ia3Lh7BJjOC2vPV28/yk08AovOMsB3SNQPGnE7bv+lsIBTmAkvw==", "path": "microsoft.aspnetcore.antiforgery/2.2.0", "hashPath": "microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "path": "microsoft.aspnetcore.authentication/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iar9VFlBHkZGdSG9ZUTmn6Q8Qg+6CtW5G/TyJI2F8B432TOH+nZlkU7O0W0byow6xsxqOYeTviSHz4cCJ3amfQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OAuth/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4CCwKg9LVEyqiJHuVshmqtcsMuE1Rqz5kMB+FV13BL0PO8RawT1pwV3HSOWP3+hNL/vYviHxolL0xOEdjcc1dw==", "path": "microsoft.aspnetcore.authentication.oauth/2.1.0", "hashPath": "microsoft.aspnetcore.authentication.oauth.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8bRZDDv1mKFtdcJC/wrAytAYqSYxte1HXI/ce03trIYh5ODYZpL6FejGqPYN1M8gJUJ2louoK0UnhMPq9lm69g==", "path": "microsoft.aspnetcore.authentication.openidconnect/2.1.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GaqeOcVVMTf4X/joSuNku+g23M+Y5Tx0xN4vOS7A6OwNujXJXetMnDBC+u6CAaf0CECB4PII2+t+WAQUlJGFTw==", "path": "microsoft.aspnetcore.connections.abstractions/3.0.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "path": "microsoft.aspnetcore.cors/2.2.0", "hashPath": "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-NCY0PH3nrFYbhqiq72rwWsUXlV4OAE0MOukvGvIBOTnEPMC1yVL42k1DXLnaIu+c0yfMAxIIG9Iuaykp9BQQQw==", "path": "microsoft.aspnetcore.cryptography.keyderivation/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pva9ggfUDtnJIKzv0+wxwTX7LduDx6xLSpMqWwdOJkW52L0t31PI78+v+WqqMpUtMzcKug24jGs3nTFpAmA/2g==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-jq2OPhIdCCjkcdZGVd4NMRwDG+A/3yUx2mgR3Bd4z+HXNZMbQzOQnL4hw6YFLsyBjHNnJc17fyeKSyV3DlKLEg==", "path": "microsoft.aspnetcore.http.connections/1.0.15", "hashPath": "microsoft.aspnetcore.http.connections.1.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-866ROb19nLihGEhWFeGznfLz28scbc2mM/ICOxU2mJsqGJ8I0XRkSzveRBCHrnoiiDI9g9OTCer8j0w0X+O/gQ==", "path": "microsoft.aspnetcore.http.connections.client/3.0.0", "hashPath": "microsoft.aspnetcore.http.connections.client.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X38Amqn+5qOzLWXGVNiAjOODmlWsVeM6qrL3btJBCdH9Xmc1+lwiTmp11iG8XKCAZxsYGkIgXcBKEduTPUOoTA==", "path": "microsoft.aspnetcore.http.connections.common/3.0.0", "hashPath": "microsoft.aspnetcore.http.connections.common.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BMdL2GrpKp0wvDjIvbdaTMDlGW/LFAzMKf9vovMKRxJFhvJimvvw+cMDefeMjMqQcGYyI78N2iR4Ouiv2IZ/aw==", "path": "microsoft.aspnetcore.http.features/3.0.0", "hashPath": "microsoft.aspnetcore.http.features.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F16BKeS96wKhyIyhaFR7m8kRIwIvPUW9Dx7IlGWmu2IIwnUDCdo+2z7IrWKA8r77pZQ1UE9kYcBPg5456YdAIA==", "path": "microsoft.aspnetcore.identity/2.2.0", "hashPath": "microsoft.aspnetcore.identity.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9BB9hftnCsyJalz9IT0DUFxz8Xvgh3TOfGWolpuf19duxB4FySq7c25XDYBmBMS+sun5/PsEUAi58ra4iJAoA==", "path": "microsoft.aspnetcore.jsonpatch/2.2.0", "hashPath": "microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+PGX1mEfq19EVvskBBb9XBQrXZpZrh6hYhX0x3FkPTEqr+rDM2ZmsEwAAMRmzcidmlDM1/7cyDSU/WhkecU8tA==", "path": "microsoft.aspnetcore.localization/2.2.0", "hashPath": "microsoft.aspnetcore.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-noun9xcrEvOs/ubczt2OluY9/bOOM2erv1D/gyyYtfS2sfyx2uGknUIAWoqmqc401TvQDysyx8S4M9j5zPIVBw==", "path": "microsoft.aspnetcore.mvc/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wxxt1rFVHITp4MDaGQP/wyl+ROVVVeQCTWI6C8hxI8X66C4u6gcxvelqgnmsn+dISMCdE/7FQOwgiMx1HxuZqA==", "path": "microsoft.aspnetcore.mvc.analyzers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iSREQct43Xg2t3KiQ2648e064al/HSLPXpI5yO9VPeTGDspWKHW23XFHRKPN1YjIQHHfBj8ytXbiF0XcSxp5pg==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ALiY4a6BYsghw8PT5+VU593Kqp911U3w9f/dH9/ZoI3ezDsDAGiObqPu/HP1oXK80Ceu0XdQ3F0bx5AXBeuN/Q==", "path": "microsoft.aspnetcore.mvc.core/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oINjMqhU7yzT2T9AMuvktlWlMd40i0do8E1aYslJS+c5fof+EMhjnwTh6cHN1dfrgjkoXJ/gutxn5Qaqf/81Kg==", "path": "microsoft.aspnetcore.mvc.cors/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WOw4SA3oT47aiU7ZjN/88j+b79YU6VftmHmxK29Km3PTI7WZdmw675QTcgWfsjEX4joCB82v7TvarO3D0oqOyw==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1L4pP124mrN6duwOtNVIJUqy4CczC2/ah4MXarRt9ZRpJd2zNp1j3tJCgyEQpqai6zNVP6Vp2ZRMQcNDcNAKA==", "path": "microsoft.aspnetcore.mvc.localization/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TXvEOjp3r6qDEjmDtv3pXjQr/Zia9PpoGkl1MyTEqKqrUehBTpAdCjA8APXFwun19lH20OuyU+e4zDYv9g134w==", "path": "microsoft.aspnetcore.mvc.razor/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sei/0moqBDQKaAYT9PtOeRtvYgHQQLyw/jm3exHw2w9VdzejiMEqCQrN2d63Dk4y7IY0Irr/P9JUFkoVURRcNw==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsMs4QKCf5VgdGZq9/nfAVkMJ/8uE4ie0Iugv4FtxbHBmMdpPQQBfTFKoUpwMbgIRw7hzV8xy2HPPU5o58PsdQ==", "path": "microsoft.aspnetcore.mvc.razorpages/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsrm/dLx7ztfWV+WEE7O8YqEePW7TmUwFwR7JsOUSTKaV9uSeghdmoOsYuk0HeoTiMhRxH8InQVE9/BgBj+jog==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt7MGkzCFVTAD5oesI8UeVVeiSgaZ0tPdFstQjG6YLJSCiq1koOUSHMpf0PASGdOW/H9hxXkolIBhT5dWqJi7g==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VLWK+ZtMMNukY6XjxYHc7mz33vkquoEzQJHm/LCF5REVxIaexLr+UTImljRRJBdUDJluDAQwU+59IX0rFDfURA==", "path": "microsoft.aspnetcore.razor.design/2.2.0", "hashPath": "microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeyzVFXZdpUAnWKWoNYE0SsP1Eu7JLjZaC94jaI1VfGtK57QykROz/iGMc8D0VcqC8i02qYTPQN/wPKm6PfidA==", "path": "microsoft.aspnetcore.razor.language/2.2.0", "hashPath": "microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TyLgQ4y4RVUIxiYFnHT181/rJ33/tL/NcBWC9BwLpulDt5/yGCG4EvsToZ49EBQ7256zj+R6OGw6JF+jj6MdPQ==", "path": "microsoft.aspnetcore.signalr.common/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.common.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mk69z50oFk2e89d3F/AfKeAvP3kvGG7MHG4ErydZiUd3ncSRq0kl0czq/COn/QVKYua9yGr2LIDwuR1C6/pu8Q==", "path": "microsoft.aspnetcore.signalr.core/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BOsjatDJnvnnXCMajOlC0ISmiFnJi/EyJzMo0i//5fZJVCLrQ4fyV/HzrhhAhSJuwJOQDdDozKQ9MB9jHq84pg==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.1.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-OgfFIdZpINWU7X+DLbzEYamnSaQmo6oax6nr9qDWLqFJuoVDTRjndV9QwvOMF4r6oOyi2VkZJuJs6WgcmVreWQ==", "path": "microsoft.aspnetcore.websockets/2.1.7", "hashPath": "microsoft.aspnetcore.websockets.2.1.7.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.5.12": {"type": "package", "serviceable": true, "sha512": "sha512-0SlEl+TSQdpjXWf9/37dXWAa0zk6R1EJKmGtGZeKUAH7WEQpJOWMxJ9I43igcBCnTkFwa28CdPnpSCjFZVQlkw==", "path": "microsoft.azure.amqp/2.5.12", "hashPath": "microsoft.azure.amqp.2.5.12.nupkg.sha512"}, "Microsoft.Azure.KeyVault/3.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-WyNaquOQ1Czl7FsB2rxXoCq+Hywmlc8lTmiu6jtGfyS6NszKzV0uGRcAF1KC75YRB0tD8Yx6WXdwlGPpbB73QQ==", "path": "microsoft.azure.keyvault/3.0.4", "hashPath": "microsoft.azure.keyvault.3.0.4.nupkg.sha512"}, "Microsoft.Azure.KeyVault.WebKey/3.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-2D4+DRdmGYsfsE+n8pY7NVKnMG60HRsinjQN62KpAa6qNeJ66daVGJhnhVcSqT1l78+u8SduaDMiS7OgYOS0tQ==", "path": "microsoft.azure.keyvault.webkey/3.0.4", "hashPath": "microsoft.azure.keyvault.webkey.3.0.4.nupkg.sha512"}, "Microsoft.Azure.SignalR/1.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-4H1CaFmCLBJTzC7xQtTGYciU+EZHumdipKVRoU8e14qBXtWgJmbim5TG9l5YnD/XWoEqz4/1QHxTei/7d7qUEA==", "path": "microsoft.azure.signalr/1.18.0", "hashPath": "microsoft.azure.signalr.1.18.0.nupkg.sha512"}, "Microsoft.Azure.SignalR.Protocols/1.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-u3ifjeX07xs03kpT9kyj0XbNOrbGZbGSNrWGJOQ4NtnbJE/7SzkRtHqj7pXO4Ryb3TT8FdCnM2Kdd9ivKESzEA==", "path": "microsoft.azure.signalr.protocols/1.18.0", "hashPath": "microsoft.azure.signalr.protocols.1.18.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f5Kr5JepAbiGo7uDmhgvMqhntwxqXNn6/IpTBSSI4cuHhgnJGrLxFRhMjVpRkLPp6zJXO0/G0l3j9p9zSJxa+w==", "path": "microsoft.bcl.timeprovider/8.0.0", "hashPath": "microsoft.bcl.timeprovider.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2qL0Qyu5qHzg6/JzF80mLgsqn9NP/Q0mQwjH+Z+DiqcuODJx8segjN4un2Tnz6bEAWv8FCRFNXR/s5wzlxqA8A==", "path": "microsoft.codeanalysis.razor/2.2.0", "hashPath": "microsoft.codeanalysis.razor.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-cff+ug/XZnGmX6DFgLY92t7G9W3i8r23w5Qnuby41l9rS+X+f7Y51hV5glvIrmsu3tIcnxbR+Z4CQ2zGhksIJw==", "path": "microsoft.data.sqlclient/2.0.1", "hashPath": "microsoft.data.sqlclient.2.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+AcpLQHWF04aypnUrv62RL63a1DqHffMGkejhNI+fFgR/p86CaP/Dum8iDaqfKXgoY9wuzA9mDUOCPGwrr3CXg==", "path": "microsoft.data.sqlclient.alwaysencrypted.azurekeyvaultprovider/1.1.1", "hashPath": "microsoft.data.sqlclient.alwaysencrypted.azurekeyvaultprovider.1.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalWSIMdwLZoNXxjmFmeRrFgaUXbEADkYNGm6HM33pculFv8gKt53s1Frs+kTfVPWMYjocd4gqwz92KrkcLfXA==", "path": "microsoft.data.sqlclient.sni.runtime/2.0.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.0.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-7zA23EkPltGmtobG/X1JviLEnylQ27zoupMfTs4Prif9WQpGdyrajLQP1Emfxq8nzimB99omKO2piM2g8IhqfA==", "path": "microsoft.data.sqlite.core/3.1.8", "hashPath": "microsoft.data.sqlite.core.3.1.8.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "path": "microsoft.dotnet.platformabstractions/3.1.6", "hashPath": "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-bI+yxA329qf+8efR6A35/3L2NLekcsWJOfXakA37ILUiWcX1qp/XsXmEi6SYMpMikioy0a5p0IU8gHoqSvtLaA==", "path": "microsoft.entityframeworkcore/3.1.8", "hashPath": "microsoft.entityframeworkcore.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-bJ6Crbz3FP2Cze1DXg+FiE5l0AFK8y6j32LP+6tMFrpdJc0XB8XBGXEX6w9baulxXC8U3OYUq1yxFVwgNdVyJw==", "path": "microsoft.entityframeworkcore.abstractions/3.1.8", "hashPath": "microsoft.entityframeworkcore.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-Cm1+PV53O/xN4P8fMkSZq9aqyXRjEZ5kmuWs7W4yE4V4GLgrqTCRmtooM5tUPM3R7VI47hAa8Aab+UuSRvpU+w==", "path": "microsoft.entityframeworkcore.analyzers/3.1.8", "hashPath": "microsoft.entityframeworkcore.analyzers.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/2.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-ouDKXTXY+ItF+PAK/jkp+Cp6Ve8Pd9sblyqOErjaiK7CzeN28peFsv3NfDeoqTAXXCCKEyuv/iXYtLs4r3wI0w==", "path": "microsoft.entityframeworkcore.design/2.2.6", "hashPath": "microsoft.entityframeworkcore.design.2.2.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-n8c2Nyfyl0AKXdM++tiKZmX/jeiu1Uv+uxZAHGgKuiQJbpOCPAhjRB4tHLdo1jOvfkhJT9K6wekf9VaXjtL1tw==", "path": "microsoft.entityframeworkcore.relational/3.1.8", "hashPath": "microsoft.entityframeworkcore.relational.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-AgEZiUoylSiox7On1nLJb1Ao0dx5LpGiLBLtYnzQeZzlZIHcjjZEQhgF7V4+4cwXF2DRuT4Ekg+akXe25P84+g==", "path": "microsoft.entityframeworkcore.sqlite/3.1.8", "hashPath": "microsoft.entityframeworkcore.sqlite.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-YBMXMuN/VVYVVE7XIr4ZMtIjnv6kZk/oZit1DSapw/ZssUaHpjgHSol/aa3Fdc64SpfStzdWmleXAzIs2CYBfA==", "path": "microsoft.entityframeworkcore.sqlite.core/3.1.8", "hashPath": "microsoft.entityframeworkcore.sqlite.core.3.1.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ws6yIIpX4w3odZqILfZCWVWvBu7j7mGLLPkdg+GMdorwTSz4/NOQ0ngxnaj/E2on0EKXylv0jHP8OFqvycBM2A==", "path": "microsoft.entityframeworkcore.sqlserver/3.1.1", "hashPath": "microsoft.entityframeworkcore.sqlserver.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-iBIdKjKa2nR4LdknV2JMSRpJVM5TOca25EckPm6SZQT6HfH8RoHrn9m14GUAkvzE+uOziXRoAwr8YIC6ZOpyXg==", "path": "microsoft.extensions.caching.abstractions/3.1.8", "hashPath": "microsoft.extensions.caching.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-u04q7+tgc8l6pQ5HOcr6scgapkQQHnrhpGoCaaAZd24R36/NxGsGxuhSmhHOrQx9CsBLe2CVBN/4CkLlxtnnXw==", "path": "microsoft.extensions.caching.memory/3.1.8", "hashPath": "microsoft.extensions.caching.memory.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-xWvtu/ra8xDOy62ZXzQj1ElmmH3GpZBSKvw4LbfNXKCy+PaziS5Uh0gQ47D4H4w3u+PJfhNWCCGCp9ORNEzkRw==", "path": "microsoft.extensions.configuration/3.1.8", "hashPath": "microsoft.extensions.configuration.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-0qbNyxGpuNP/fuQ3FLHesm1Vn/83qYcAgVsi1UQCQN1peY4YH1uiizOh4xbYkQyxiVMD/c/zhiYYv94G0DXSSA==", "path": "microsoft.extensions.configuration.abstractions/3.1.8", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-l/oqIWRM4YF62mlCOrIKGUOCemsaID/lngK2SZEtpYI8LrktpjPd4QzvENWj5GebbLbqOtsFhF6Ko6dgzmUnBw==", "path": "microsoft.extensions.configuration.binder/3.1.8", "hashPath": "microsoft.extensions.configuration.binder.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-Os7uRhp9xwawY5w2tgXw/86YrmAJZl6aHiQVdS9boWybTWPkJOvXXrQ3AGwuldN1W/r+cfnwRe2ePGeFO4zlzg==", "path": "microsoft.extensions.configuration.environmentvariables/2.2.4", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.2.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1qCpWBC8Ed4tguTR/qYkbb3F6DI5Su3t8xyFo3/5MzAd8PwPpHzgX8X04KbBxKmk173Pb64x7xMHarczVFQUA==", "path": "microsoft.extensions.configuration.fileextensions/2.2.0", "hashPath": "microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jUDdmLyFmLf9V3mqnMzSAzAv4QigJ67tZh5Q7HBXeBnESL2UyeesNG6jSBti+b63JpxZf+EDyn+anx3gyrNxug==", "path": "microsoft.extensions.configuration.json/2.2.0", "hashPath": "microsoft.extensions.configuration.json.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.KeyPerFile/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qK7vVxtUrpxdQPhvjF3RVYkcV86q/QfMBWqvvXAKYYkQ+H/4GXxk5cbPaSWdMZB5YU1GBEFBuZg9MZxDRvPJkg==", "path": "microsoft.extensions.configuration.keyperfile/2.2.0", "hashPath": "microsoft.extensions.configuration.keyperfile.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/N2xo6/sNbVshnKktmq5lwaQbsAR2SrzCVrJEeMP8OKZVI7SzT8P6/WXZF8/YC7dTYsMe3nrHzgl1cF9i5ZKQ==", "path": "microsoft.extensions.configuration.usersecrets/2.2.0", "hashPath": "microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-tUpYcVxFqwh8wVD8O+6A8gJnVtl6L4N1Vd9bLJgQSJ0gjBTUQ/eKwJn0LglkkaDU7GAxODDv4eexgZn3QSE0NQ==", "path": "microsoft.extensions.dependencyinjection/3.1.8", "hashPath": "microsoft.extensions.dependencyinjection.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-YP0kEBkSLTVl3znqZEux+xyJpz5iVNwFZf0OPS7nupdKbojSlO7Fa+JuQjLYpWfpAshaMcznu27tjWzfXRJnOA==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-/UlDKULIVkLQYn1BaHcy/rc91ApDxJb7T75HcCbGdqwvxhnRQRKM2di1E70iCPMF9zsr6f4EgQTotBGxFIfXmw==", "path": "microsoft.extensions.dependencymodel/3.1.6", "hashPath": "microsoft.extensions.dependencymodel.3.1.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Az/RxWB+UlyVN/TvQFaGXx8XAXVZN5WQnnuJOsjwBzghSJc1i8zqNjIypPHOedcuIXs2XSWgOSL6YQ3BlCnoJA==", "path": "microsoft.extensions.fileproviders.composite/2.2.0", "hashPath": "microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tbDHZnBJkjYd9NjlRZ9ondDiv1Te3KYCTW2RWpR1B0e1Z8+EnFRo7qNnHkkSCixLdlPZzhjlX24d/PixQ7w2dA==", "path": "microsoft.extensions.fileproviders.physical/2.2.0", "hashPath": "microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZSsHZp3PyW6vk37tDEdypjgGlNtpJ0EixBMOfUod2Thx7GtwfFSAQXUQx8a8BN8vfWKGGMbp7jPWdoHx/At4wQ==", "path": "microsoft.extensions.filesystemglobbing/2.2.0", "hashPath": "microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hZ8mz6FgxSeFtkHzw+Ad0QOt2yjjpq4WaG9itnkyChtXYTrDlbkw3af2WJ9wdEAAyYqOlQaVDB6MJSEo8dd/vw==", "path": "microsoft.extensions.http/2.2.0", "hashPath": "microsoft.extensions.http.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-HIk+xQ0wQHPHaOGa/mm1NMx4aDO5IosO/C7SVmbwFb/XQgRHnGOnA8xNcUqzwerWHhw0vVzuIRJuLrKlipDsyA==", "path": "microsoft.extensions.http.polly/2.2.0", "hashPath": "microsoft.extensions.http.polly.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/C+Valwg8IeUwDIunusittHivA9iyf82Jr1yeUFWO2zH2mDMMeYgjRyDLZqfL/7Vq94PEQsgv1XAaDfAX8msMw==", "path": "microsoft.extensions.identity.core/2.2.0", "hashPath": "microsoft.extensions.identity.core.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nBQLeBrcd4Rgd9vQi4gF5NgAWxnQrHekjjwlgww4wyLNfJDizjiex2resOLoAuAgy3y2IIAWjOpbr0UKR2ykw==", "path": "microsoft.extensions.localization/2.2.0", "hashPath": "microsoft.extensions.localization.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FQzXG/lYR9UOM2zHpqsjTRpp3EghIYo3FCsQpfmtbp+glPaU0WXZfNmMjyqBRmMj1Sq93fPnC+G9zzYRauuRQA==", "path": "microsoft.extensions.localization.abstractions/2.2.0", "hashPath": "microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-Bch88WGwrgJUabSOiTbPgne/jkCcWTyP97db8GWzQH9RcGi6TThiRm8ggsD+OXBW2UBwAYx1Zb1ns1elsMiomQ==", "path": "microsoft.extensions.logging/3.1.8", "hashPath": "microsoft.extensions.logging.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-LxQPR/KE4P9nx304VcFipWPcW8ZOZOGHuiYlG0ncAQJItogDzR9nyYUNvziLObx2MfX2Z9iCTdAqEtoImaQOYg==", "path": "microsoft.extensions.logging.abstractions/3.1.8", "hashPath": "microsoft.extensions.logging.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-L50f1F9ZdvvBmGvdoUONsh6ScUtG+mP8TEYMHMKR3a0cSPYMHv28GnNPhbXY5zN0fr53So0I8SR2M02utn2zNQ==", "path": "microsoft.extensions.logging.debug/3.1.1", "hashPath": "microsoft.extensions.logging.debug.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-mpkwjNg5sr1XHEJwVS8G1w6dsh5/72vQOOe4aqhg012j93m8OOmfyIBwoQN4SE0KRRS+fatdW3qqUrHbRwlWOA==", "path": "microsoft.extensions.options/3.1.8", "hashPath": "microsoft.extensions.options.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/MP147fSqlIcCymaNpLbjdJsFVkSJM9Sz+jbWMr1gKMDVxoOS8AuFjJkVyKU/eydYxHIR/K1Hn3wisJBW5gSg==", "path": "microsoft.extensions.options.configurationextensions/2.1.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-XcIoXQhT0kwnEhOKv/LmpWR6yF6QWmBTy9Fcsz4aHuCOgTJ7Zd23ELtUA4BfwlYoFlSedavS+vURz9tNekd44g==", "path": "microsoft.extensions.primitives/3.1.8", "hashPath": "microsoft.extensions.primitives.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-xk8tJeGfB2yD3+d7a0DXyV7/HYyEG10IofUHYHoPYKmDbroi/j9t1BqSHgbq1nARDjg7m8Ki6e21AyNU7e/R4Q==", "path": "microsoft.identity.client/4.30.1", "hashPath": "microsoft.identity.client.4.30.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.18.4": {"type": "package", "serviceable": true, "sha512": "sha512-HpG4oLwhQsy0ce7OWq9iDdLtJKOvKRStIKoSEOeBMKuohfuOWNDyhg8fMAJkpG/kFeoe4J329fiMHcJmmB+FPw==", "path": "microsoft.identity.client.extensions.msal/2.18.4", "hashPath": "microsoft.identity.client.extensions.msal.2.18.4.nupkg.sha512"}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-VIi6Bzt0UWEeannuEjtRFr/LgUXXw70a9LlIVfoCGorztZhzHgKiof6UJE1ETRYlf/L+kepsUZeekYZwqb6xPg==", "path": "microsoft.identitymodel.clients.activedirectory/5.2.8", "hashPath": "microsoft.identitymodel.clients.activedirectory.5.2.8.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-0q0U1W+gX1jmfmv7uU7GXFGB518atmSwucxsVwPGpuaGS3jwd2tUi+Gau+ezxR6oAFEBFKG9lz/fxRZzGMeDXg==", "path": "microsoft.identitymodel.jsonwebtokens/5.6.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-zEDrfEVW5x5w2hbTV94WwAcWvtue5hNTXYqoPh3ypF6U8csm09JazEYy+VPp2RtczkyMfcsvWY9Fea17e+isYQ==", "path": "microsoft.identitymodel.logging/5.6.0", "hashPath": "microsoft.identitymodel.logging.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ei7YqYx0pIFL6JjK8ZnPK0MXZRWUNHtJPUl3KqSvj9+2f5CMa6GRSEC+BMDHr17tP6yujYUg0IQOcKzmC7qN5g==", "path": "microsoft.identitymodel.protocols/5.6.0", "hashPath": "microsoft.identitymodel.protocols.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-yh3n+uXiwpBy/5+t67tYcmRxb9kwQdaKRyG/DNipRMF37bg5Jr0vENOo1BQz6OySMl5WIK544SzPjtr7/KkucA==", "path": "microsoft.identitymodel.protocols.openidconnect/5.6.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-C3OqR3QfBQ7wcC7yAsdMQqay87OsV6yWPYG/Ai3n7dvmWIGkouQhXoVxRP0xz3cAFL4hxZBXyw4aLTC421PaMg==", "path": "microsoft.identitymodel.tokens/5.6.0", "hashPath": "microsoft.identitymodel.tokens.5.6.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.21": {"type": "package", "serviceable": true, "sha512": "sha512-KDYlgTyO693V6pi6SGk9eg+dDvKjuOgmkapbHdpnB1SmTPKpvWxVLIMyARJsCFLfB6axyURUJHOfvxBQ0yJKeg==", "path": "microsoft.rest.clientruntime/2.3.21", "hashPath": "microsoft.rest.clientruntime.2.3.21.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime.Azure/3.3.19": {"type": "package", "serviceable": true, "sha512": "sha512-+NVBWvRXNwaAPTZUxjUlQggsrf3X0GbiRoxYfgc3kG9E55ZxZxvZPT3nIfC4DNqzGSXUEvmLbckdXgBBzGdUaA==", "path": "microsoft.rest.clientruntime.azure/3.3.19", "hashPath": "microsoft.rest.clientruntime.azure.3.3.19.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "MimeKit/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-S7X9/8vlLHcDpvIJvqUxgM+dCkLuClTeGBUtargPLGqhNPk4F8VdWH1xzOTXD1OVYwB/HaJYy3Gg1jP20Ordcw==", "path": "mimekit/2.2.0", "hashPath": "mimekit.2.2.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/12.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "path": "newtonsoft.json/12.0.2", "hashPath": "newtonsoft.json.12.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "Nito.AsyncEx.Context/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qnth1Ye+QSLg8P3fSFYzk7ue6oUUHQcKpLitgAig8xRFqTK5W1KTlfxF/Z8Eo0BuqZ17a5fAGtXrdKJsLqivZw==", "path": "nito.asyncex.context/5.0.0", "hashPath": "nito.asyncex.context.5.0.0.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kjauyO8UMo/FGZO/M8TdjXB8ZlBPFOiRN8yakThaGQbYOywazQ0kGZ39SNr2gNNzsTxbZOUudBMYNo+IrtscbA==", "path": "nito.asyncex.coordination/5.0.0", "hashPath": "nito.asyncex.coordination.5.0.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZtvotignafOLteP4oEjVcF3k2L8h73QUCaFpVKWbU+EOlW/I+JGkpMoXIl0rlwPcDmR84RxzggLRUNMaWlOosA==", "path": "nito.asyncex.tasks/5.0.0", "hashPath": "nito.asyncex.tasks.5.0.0.nupkg.sha512"}, "Nito.Collections.Deque/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-yGDKqCQ61i97MyfEUYG6+ln5vxpx11uA5M9+VV9B7stticbFm19YMI/G9w4AFYVBj5PbPi138P8IovkMFAL0Aw==", "path": "nito.collections.deque/1.0.4", "hashPath": "nito.collections.deque.1.0.4.nupkg.sha512"}, "Nito.Disposables/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ExJl/jTjegSLHGcwnmaYaI5xIlrefAsVdeLft7VLtXI2+W5irihiu36LizWvlaUpzY1/llo+YSh09uSHMu2VFw==", "path": "nito.disposables/2.0.0", "hashPath": "nito.disposables.2.0.0.nupkg.sha512"}, "NUglify/1.5.13": {"type": "package", "serviceable": true, "sha512": "sha512-EgvMq0u3PEeKv6uypBvau999iNSOSYdayZcxsgS9TBWIqs63WIWI/syl7cOs82J54xJnS3STpKZAW4SZElGIqg==", "path": "nuglify/1.5.13", "hashPath": "nuglify.1.5.13.nupkg.sha512"}, "OfficeOpenXml.Core.ExcelPackage/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDhZ7K/zd7Zg3qndOacpdoYvyMbrnOm590g6fhS7NhR3zWkg90sz4NyOo1LGwq/9zianqjwV4gyJjtzLZ5JKww==", "path": "officeopenxml.core.excelpackage/1.0.0", "hashPath": "officeopenxml.core.excelpackage.1.0.0.nupkg.sha512"}, "PayPalCheckoutSdk/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-NYJvc7hvbyKe3heKuBTQemY6X0YfTDavkNlbs6iA92QorfRGgUpsPPqyAMwN+4ugQL8oL2+r+GE5qBkc2i97RQ==", "path": "paypalcheckoutsdk/1.0.2", "hashPath": "paypalcheckoutsdk.1.0.2.nupkg.sha512"}, "Polly/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-KZm8iG29y6Mse7YntYYJSf5fGWuhYLliWgZaG/8NcuXS4gN7SPdtPYpjCxQlHqxvMGubkWVrGp3MvUaI7SkyKA==", "path": "polly/8.2.0", "hashPath": "polly.8.2.0.nupkg.sha512"}, "Polly.Contrib.WaitAndRetry/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-1MUQLiSo4KDkQe6nzQRhIU05lm9jlexX5BVsbuw0SL82ynZ+GzAHQxJVDPVBboxV37Po3SG077aX8DuSy8TkaA==", "path": "polly.contrib.waitandretry/1.1.1", "hashPath": "polly.contrib.waitandretry.1.1.1.nupkg.sha512"}, "Polly.Core/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gnKp3+mxGFmkFs4eHcD9aex0JOF8zS1Y18c2A5ckXXTVqbs6XLcDyLKgSa/mUFqAnH3mn9+uVIM0RhAec/d3kA==", "path": "polly.core/8.2.0", "hashPath": "polly.core.8.2.0.nupkg.sha512"}, "Polly.Extensions.Http/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9NPDm98bmtlzLHnMDJ4YHno88yZmBOzvks34coP73/1GJzabSUi217xISa4z+b7YO5TncZMQVM4O5ThlZwg8mQ==", "path": "polly.extensions.http/2.0.1", "hashPath": "polly.extensions.http.2.0.1.nupkg.sha512"}, "Portable.BouncyCastle/1.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-EaCgmntbH1sOzemRTqyXSqYjB6pLH7VCYHhhDYZ59guHSD5qPwhIYa7kfy0QUlmTRt9IXhaXdFhNuBUArp70Ng==", "path": "portable.bouncycastle/1.8.5", "hashPath": "portable.bouncycastle.1.8.5.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M2nN92ePS8BgQ2oi6Jj3PlTUzadYSIWLdZrHY1n1ZcW9o4wAQQ6W+aQ2lfq1ysZQfVCgDwY58alUdowrzezztg==", "path": "runtime.native.system.net.security/4.3.0", "hashPath": "runtime.native.system.net.security.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-OVPI/nh5AqfLCIKhAYqjCa6AHhc7oKApGcGM3UhMRSerFiBx58nSpGwxVFdMgjOCWZR+fA49nzsnKlWp5hFo8w==", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.0.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-TFSBX426OelS1tkaVC254NVVlrJIe9YLhWPkEvuqJj2104QpmDmEYOhfdfDJD1E/2SmqDhoRw1ek5cQHj8olcQ==", "path": "sqlitepclraw.core/2.0.2", "hashPath": "sqlitepclraw.core.2.0.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-S+Tsqe/M7wsc+9HeediI6UHtBKf2X586aRwhi1aBVLGe0WxkAo52O9ZxwEy/v8XMLefcrEMupd2e9CDlIT6QCw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.2.nupkg.sha512"}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZSwacbKJUsxJEZxwT23uZVrGbaIvXcADZDz5Sr66fikO5eehdcceDncjzwzTzWfW13di8gpTpstx3WJSt/Ci5Q==", "path": "sqlitepclraw.provider.dynamic_cdecl/2.0.2", "hashPath": "sqlitepclraw.provider.dynamic_cdecl.2.0.2.nupkg.sha512"}, "Stripe.net/27.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-f8Uq9lLBKlgt/7/VCdvoAdy0tAdLge2t0059qX5MyHL1UfZ53ri95bY3DLaoEGZMcl4B9S44xBfYl4FSJoh6tQ==", "path": "stripe.net/27.16.1", "hashPath": "stripe.net.27.16.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-B43<PERSON>sz5EfMwyEbnObwRxW5u85fzJma3lrDeGcSAV1qkhSRTNY5uXAByTn9h9ddNdhM+4/YoLc/CI43umjwIl9Q==", "path": "system.collections.immutable/1.7.1", "hashPath": "system.collections.immutable.1.7.1.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-j81Lovt90PDAq8kLpaJfJKV/rWdWuEk6jfV+MBkee33vzYLEUsy4gXK8laa9V2nZlLM9VM9yA/OOQxxPEJKAMw==", "path": "system.diagnostics.diagnosticsource/4.7.1", "hashPath": "system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Uty9sMaeBG0/GIRTW4+DUTvuB/od5E6rY45JbEz1c2xMoPSA9GLov4KdiBEpjsEcsgORa6sC3vfh70tEJJaOw==", "path": "system.directoryservices/4.5.0", "hashPath": "system.directoryservices.4.5.0.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-m9Blc7UXAVMG60+CWhC9KC4y+4azuiHu3QOvII27BYfV8TDmaay6smZrrx1u7DhfJ4Gjt14eKzj8VO2aR9/aBw==", "path": "system.directoryservices.accountmanagement/4.5.0", "hashPath": "system.directoryservices.accountmanagement.4.5.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-EpWC9CW2GdFiWzhWEIcXX8tMPGRdrpL+1BI4WyBYG4G1TlOuMXO3bpyLydEo5/3ThDFPzWfp0Yp0u360dGwvJQ==", "path": "system.directoryservices.protocols/4.5.0", "hashPath": "system.directoryservices.protocols.4.5.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMvPpX4exs2fe7Upq5zHMSR4yupc+jy8WG8yjucZL0XvT+r/T0hRvLIe9fP/SeN8/UVxFYBRAkRI5k1zbRGqmA==", "path": "system.identitymodel.tokens.jwt/5.6.0", "hashPath": "system.identitymodel.tokens.jwt.5.6.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TYe6xstoqT5MlTly0OtPU6u9zWuNScLVMEx6sTCjjx+Hqdp0wCXoG6fnzMpTPMQACXQzi9pd2N5Tloow+5jQdQ==", "path": "system.io.filesystem.accesscontrol/4.5.0", "hashPath": "system.io.filesystem.accesscontrol.4.5.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9VV4KAbgRQZ79iEoG40KIeZy38O30oWwewScAST879+oki8g/Wa2HXZQgrhDDxQM4GkP1PnRJll05NMiVPbYAw==", "path": "system.io.packaging/4.7.0", "hashPath": "system.io.packaging.4.7.0.nupkg.sha512"}, "System.IO.Pipelines/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-xOqFH7Uiemd/QzUl2Di23cZCrc2CmgijeTDyFKDT8+jjpH+ynf7WoLUExY2AYGyGBjGL5Kn6DZScnqeg91zQOQ==", "path": "system.io.pipelines/4.6.0", "hashPath": "system.io.pipelines.4.6.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-hi<PERSON>+wiOv78inG/gMTaHF3wKu6hleYr8yzrhKPkir/8fLT1WD8ISIk4y5prLHjySEN960rICaZO67MVnu2Hfw==", "path": "system.linq.dynamic.core/1.0.16", "hashPath": "system.linq.dynamic.core.1.0.16.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Security/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xT2jbYpbBo3ha87rViHoTA6WdvqOAW37drmqyx/6LD8p7HEPT2qgdxoimRzWtPg8Jh4X5G9BV2seeTv4x6FYlA==", "path": "system.net.security/4.3.2", "hashPath": "system.net.security.4.3.2.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-O8RIMEVeOFzT8fscu6MDc4y+0LfnlXdqGovb0rJHBNVKhwn6BsNJmTY1oYUZPPsMfcc5OP20WpX4vj/aK8n98g==", "path": "system.net.websockets.websocketprotocol/4.5.3", "hashPath": "system.net.websockets.websocketprotocol.4.5.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "path": "system.private.uri/4.3.2", "hashPath": "system.private.uri.4.3.2.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-KYPNMDrLB2R+G5JJiJ2fjBpihtktKVIjsirmyyv+VDo5rQkIR9BWeCYM1wDSzbQatWNZ/NQfPsQyTB1Ui3qBfQ==", "path": "system.reflection.metadata/1.4.2", "hashPath": "system.reflection.metadata.1.4.2.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-HxozeSlipUK7dAroTYwIcGwKDeOVpQnJlpVaOkBz7CM4TsE5b/tKlQBZecTjh6FzcSbxndYaxxpsBMz+wMJeyw==", "path": "system.runtime.compilerservices.unsafe/4.6.0", "hashPath": "system.runtime.compilerservices.unsafe.4.6.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CpVfOH0M/uZ5PH+M9+Gu56K0j9lJw3M+PKRegTkcrY/stOIvRUeonggxNrfBYLA5WOHL2j15KNJuTuld3x4o9w==", "path": "system.runtime.serialization.json/4.3.0", "hashPath": "system.runtime.serialization.json.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-lIo52x0AAsZs8r1L58lPXaqN6PP51Z/XJts0kZtbZRNYcMguupxqRGjvc/GoqSKTbYa+aBwbkT4xoqQ7EsfN0A==", "path": "system.security.cryptography.pkcs/4.5.2", "hashPath": "system.security.cryptography.pkcs.4.5.2.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "path": "system.security.cryptography.x509certificates/4.3.2", "hashPath": "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Security.SecureString/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PnXp38O9q/2Oe4iZHMH60kinScv6QiiL2XH54Pj2t0Y6c2zKPEiAZsM/M3wBOHLNTBDFP0zfy13WN2M0qFz5jg==", "path": "system.security.securestring/4.3.0", "hashPath": "system.security.securestring.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEH06N0rIGmRT4LOKQ2BmUO0IxfvmIY/PaouSq+DFQku72OL8cxfw8W99uGpTCFf2vx2QHLRSh374iSM3asdTA==", "path": "system.threading.channels/4.5.0", "hashPath": "system.threading.channels.4.5.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cNLEvBX3d6MMQRZe3SMFNukVbitDAEpVZO17qa0/2FHxZ7Y7PpFRpr6m2615XYM/tYYYf0B+WyHNujqIw8Luwg==", "path": "system.valuetuple/4.3.0", "hashPath": "system.valuetuple.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-f0UpF9H+ylj3qjO/l2+Yt0R77FFR327efJwsoXAys6J1fSYFcQPrPVhaGVVWoN79+PVutj0qzj79kuhSPN70gA==", "path": "timezoneconverter/3.2.0", "hashPath": "timezoneconverter.3.2.0.nupkg.sha512"}, "Twilio/5.31.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ZKOuPvAporabY4vC5RU2WYs9hZ7yK6f73HqMxa0PgTNUqgeElIArUv9OOA1EvxVaAAXfKggIxGw0AJ7C36eqQ==", "path": "twilio/5.31.0", "hashPath": "twilio.5.31.0.nupkg.sha512"}, "Bdo.Ess.Application.Shared/7.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Core/7.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Core.Shared/7.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Dtos/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.EntityFrameworkCore/7.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Microservice.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Microservice.Core.EntityFramework/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Microservice.Core.EntityFramework.Ca/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Microservice.Core.EntityFramework.Ctsp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Microservice.Core.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Security.RateLimiter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Bdo.Ess.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}