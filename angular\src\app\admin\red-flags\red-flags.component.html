<div class="row-flex-space" [@routerTransition]>
    <div style="width:75%">
        <p-card class="p-card-properties_form">
            <p-header class="row-flex-space">
                <p>{{l("Red Flag Settings")}}</p>
            </p-header>
            <div class="margin-top">
                <div class="primeng-datatable-container" [busyIf]="primengTableHelper.isLoading">
                    <p-table #dataTable (onLazyLoad)="getRedFlags($event)" [value]="primengTableHelper.records"
                             rows="{{primengTableHelper.defaultRecordsCountPerPage}}" [paginator]="false"
                             [lazy]="true" [scrollable]="true" ScrollWidth="100%"
                             selectionMode="single" (onRowSelect)="onRowSelect($event)">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 10%" pSortableColumn="Category">
                                    {{'Category' | localize}}
                                    <p-sortIcon field="Category"></p-sortIcon>
                                </th>
                                <th style="width: 15%" pSortableColumn="Title">
                                    {{'Event Type' | localize}}
                                    <p-sortIcon field="Title"></p-sortIcon>
                                </th>
                                <th style="width: 13%" pSortableColumn="DisplayOrder">
                                    {{'Display Priority' | localize}}
                                    <p-sortIcon field="DisplayOrder"></p-sortIcon>
                                </th>
                                <th pSortableColumn="Description">
                                    {{'Description' | localize}}
                                    <p-sortIcon field="Description"></p-sortIcon>
                                </th>
                                <th style="width: 15%" pSortableColumn="Parameter">
                                    {{'Value' | localize}}
                                    <p-sortIcon field="Parameter"></p-sortIcon>
                                </th>
                                <th style="width: 10%" pSortableColumn="IsActive">
                                    {{'Status' | localize}}
                                    <p-sortIcon field="IsActive"></p-sortIcon>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit">
                            <tr [pSelectableRow]="record">
                                <td style="width: 10%">
                                    {{record.category}}
                                </td>
                                <td style="width: 15%">
                                    {{record.title}}
                                </td>
                                <td style="width: 13%">
                                    {{record.displayOrder}}
                                </td>
                                <td>
                                    {{record.description}}
                                </td>
                                <td style="width: 15%">
                                    {{record.parameter}}
                                </td>
                                <td style="width: 10%">
                                    <span *ngIf="record.isActive">{{'Active' | localize}}</span>
                                    <span *ngIf="!record.isActive">{{'Inactive' | localize}}</span>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                        {{'NoData' | localize}}
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="primengTableHelper.defaultRecordsCountPerPage" #paginator
                                     (onPageChange)="getRedFlags($event)"
                                     [totalRecords]="primengTableHelper.totalRecordsCount">
                        </p-paginator>
                        <span class="total-records-count">
                            {{'TotalRecordsCount' | localize:primengTableHelper.totalRecordsCount}}
                        </span>
                    </div>
                </div>
            </div>
        </p-card>
    </div>
    <div style="width:25%">
        <redFlagsEdit #redFlagsEdit (onSave)="getRedFlags()"></redFlagsEdit>
    </div>
</div>
