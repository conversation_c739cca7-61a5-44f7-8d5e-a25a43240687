<div class="row-flex-space" [@routerTransition]>
    <div class="ess-csp-grid col-flex">
        <div class="row-flex-space">
            <p class="ess-title">{{l("CSPS")}}</p>

            <div class="row-flex-align-center">
                <div class="ess-csp-form-control">
                    <input id="cspForm" [(ngModel)]="filterText" name="filterText"
                           class="form-control" placeholder="Name / CSP Code" type="text">
                </div>
                <div class="ess-csp-form-control">
                    <p-dropdown id="cspStatus" [options]="statuses"
                                [(ngModel)]="status" name="Statuses">
                    </p-dropdown>
                </div>
                <div class="ess-csp-form-item">
                    <button id="cspApply" pButton type="button" (click)="getCtsps()" label="Apply"></button>
                </div>
            </div>
        </div>

        <div class="margin-top">
            <p-table #dataTable (onLazyLoad)="getCtsps($event)" [value]="primengTableHelper.records"
                     rows="{{primengTableHelper.defaultRecordsCountPerPage}}" [paginator]="false"
                     [lazy]="true" [scrollable]="true" scrollHeight="calc(100vh - 195px)"
                     selectionMode="single" (onRowSelect)="onRowSelect($event)">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 50px" pSortableColumn="number">
                            {{'Code' | localize}}
                            <p-sortIcon id="cspSortCode" field="number"></p-sortIcon>
                        </th>
                        <th style="width: 50px" pSortableColumn="name">
                            {{'Name' | localize}}
                            <p-sortIcon id="cspSortName" field="name"></p-sortIcon>
                        </th>
                        <th style="width: 50px" pSortableColumn="emailAddress">
                            {{'Email Address' | localize}}
                            <p-sortIcon id="cspSortEmailAddress" field="emailAddress"></p-sortIcon>
                        </th>

                        <th style="width: 50px" pSortableColumn="PhoneNumber">
                            {{'CSPs Phone' | localize}}
                            <p-sortIcon id="cspSortPhoneNumber" field="PhoneNumber"></p-sortIcon>
                        </th>

                        <th style="width: 50px" pSortableColumn="ITPhoneNumber">
                            {{'IT Phone' | localize}}
                            <p-sortIcon id="cspSortITPhone" field="ITPhoneNumber"></p-sortIcon>
                        </th>

                        <th style="width: 50px" pSortableColumn="Address">
                            {{'Address' | localize}}
                            <p-sortIcon id="cspSortAddress" field="Address"></p-sortIcon>
                        </th>

                        <th style="width: 50px" pSortableColumn="Active">
                            {{'Status' | localize}}
                            <p-sortIcon id="cspSortStatus" field="Active"></p-sortIcon>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-record="$implicit">
                    <tr [pSelectableRow]="record">
                        <td style="width: 50px">
                            {{record.number}}
                        </td>
                        <td style="width: 50px">
                            {{record.name}}
                        </td>

                        <td style="width: 50px">
                            {{record.emailAddress}}
                        </td>

                        <td style="width: 50px">
                            {{record.phoneNumber}}
                        </td>
                        <td style="width: 50px">
                            {{record.itPhoneNumber}}
                        </td>
                        <td style="width: 50px">
                            {{record.address}}
                        </td>

                        <td style="width: 50px">
                            <span *ngIf="record.active">{{'Active' | localize}}</span>
                            <span *ngIf="!record.active">{{'Inactive' | localize}}</span>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                {{'NoData' | localize}}
            </div>
            <div class="ess-csp-pager row-flex-space">
                <span class="total-records-count">
                    No. of Records = {{primengTableHelper.totalRecordsCount}}
                </span>
                <p-paginator id="paginator" [rows]="15" #paginator
                             (onPageChange)="getCtsps($event)"
                             [totalRecords]="primengTableHelper.totalRecordsCount">
                </p-paginator>
            </div>
        </div>
    </div>

    <app-edit-ctsp #EditCtsp (onSave)="getCtsps()"></app-edit-ctsp>
</div>
