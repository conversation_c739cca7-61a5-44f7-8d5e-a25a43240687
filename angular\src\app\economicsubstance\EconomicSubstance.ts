import {
    CountryDto, RelativeActivityStatusList, ResidentCompanyCriteriaDto, RelevantActivityDetailDto,
    EconomicSubstanceDeclarationDto, ESReviewStatusDto, UserListDto, ESCAAssessmentDto, RelativeActivitySelection, EntityType} from '@shared/service-proxies/service-proxies';


// Step 1 classes
export class ES_Step1 {
    economicSubstanceDeclarationDto: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();
 
    listOfCountry: CountryDto[]
    listOfOtherCriteria: ResidentCompanyCriteriaDto[];
    selectedActivity: RelevantActivityDetailDto[];
    lookup: any;
    otherCode: number;
    ctspNumber: string;
}


export interface FieldDetail {
    enablment: boolean;
    number: number;
}

export class ActivityType
{
    id: number
    activity: string
    iscarriedforfanicialperiod: boolean
    startdate: Date 
    enddate: Date 
    hascheckboxfield:boolean=false
}

export class UploadFile {
    id: string
    name: string
}

export class Address
{
    addressLine1: string
    addressLine2: string
    country:string
}

export class ServiceProvider {
    id:string
    serviceName: string
    address: string
    phoneNo: string
    email:string
}

export interface LookUp {
    name: string,
    code: string
}


export enum RelaventActivity
{
    BankBA = 1,
    InsuBA = 2,
    FundBA =3,
    FinaBA = 4,
    HeadBA = 5,
    ShipBA = 6,
    HoldBA = 7,
    IntelBA = 8,
    DisrBA = 9,
}

export enum ActivityName
{
    BankingBusiness = 'Banking business',
    DistributionBusiness = 'Distribution and service centre business',
    FinanceBusiness = 'Finance and leasing business',
    FundBusiness = 'Fund management business',
    HeadQuarterBusiness = 'Headquarters business',
    InsuranceBusiness = 'Insurance business',
    ShippingBusiness = 'Shipping business',
    HoldingBusiness = 'Holding company business',
    IntellBusiness ='Intellectual property business'

}

export enum EconomicDocumentsName {
    EvidenceNonResidency = 'EvidenceNonResidency',
    EvidenceProvisionalTreatment = 'EvidenceProvisionalTreatment',
    SupportingDocument = 'SupportingDocument',
    ProvisionalTreatmentDocumentDoc = 'ProvisionalTreatmentDocumentDoc',
    InformationRequestedDocumentDoc = 'InformationRequestedDocument',
    EnforcmentDocumentDoc ='EnforcmentDocumentDoc',
    ESCAAssessmentDocumentsDoc ='ESCAAssessmentDocumentsDoc'
}

export enum RelevantDocumentsName {
    OtherCIGADocuments = 'OtherCIGADocuments',
    HighRiskDocuments = 'HighRiskDocuments',
    TangibleAssetIncomeDocuments ='TangibleAssetIncomeDocuments',    
    TangibleAssetEmployeeResponsibilityDocuments = 'TangibleAssetEmployeeResponsibilityDocuments',
    HistoryofStrategicDecisionsInBVIDocuments = 'HistoryofStrategicDecisionsInBVIDocuments',
    HistoryofTradingActivityIncomeDocuments = 'HistoryofTradingActivityIncomeDocuments',
    IPAssetsInBVIDocuments = 'IPAssetsInBVIDocuments',
    IPAssetsEmployeeResponsibilityDocuments = 'IPAssetsEmployeeResponsibilityDocuments',
    ConcreteEvidenceDecisionInBVIDocuments = 'ConcreteEvidenceDecisionInBVIDocuments'
}



export enum ESActionStatus {
    Add = 1,
    Edit = 2,
    Import=3
}

export enum SubmittedLocation {
    FromDraft = 1,
    FromDisplay = 2,
    FromNew = 3,
    FromImport=4
}

 export const EntityDescription = new Map<number, string>([
    [EntityType.NA, 'None'],
    [EntityType.BusinessCompany, 'Business Company'],
    [EntityType.Trust, 'Trust'],
    [EntityType.LimitedPartnership, 'Limited Partnership'],
    [EntityType.MutualFund, 'Mutual Fund'],
    [EntityType.Licensee, 'Licensee'],
    [EntityType.Other, 'Other'],
    [EntityType.ForeignLimitedPartnership, 'Foreign Limited Partnership'],
    [EntityType.ForeignCompany, 'Foreign Company'],
    [EntityType.MicroBusinessCompany, 'Micro Business Company'],


]);



export class ESRouting {
    id: string
    action: ESActionStatus
}

export class ReviewAssessmentMain
{
    esReviewStatus: ESReviewStatusDto[]=[];
    esUserlist: UserListDto[]=[];
    escaassessmnet: ESCAAssessmentDto;
    oldescaassessmnet: ESCAAssessmentDto;
    ctspNumber: string;
    filesNotUploaded: boolean
    userNotAssigned: boolean;
}
export class ReviewChangeStatus
{
    assessmentlevel: number;
    id: any;
    ctspId: any;
    currentStatus: ESReviewStatusDto = new ESReviewStatusDto();
    comments: string;
    economicSubstanceDeclarationId: any;

}

export class RelaventActivityReviewStatus
{
    label: string;
    value: RelativeActivitySelection;
}
export class RelativeActivityChange
{
    ctspId: any;
    assessmentId: any;
    activityId; any;
    activityName: string;
    selectionValue: RelativeActivitySelection;
    relativeActivityStatusList: RelativeActivityStatusList;
    isEditable: boolean;
    canChangeStatus: boolean;

}
