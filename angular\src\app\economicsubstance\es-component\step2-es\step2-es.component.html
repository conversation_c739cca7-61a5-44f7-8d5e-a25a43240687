<div class="col-flex">
    <p-card [ngClass]="{'p-card-properties_form':!readOnlyMode,'p-card-properties_form_readonly':readOnlyMode}">
        <p-header>
            <label> {{l('ESSelectActivity')}} <span class="required">*</span> </label>
        </p-header>
        <div *ngIf="!readOnlyMode" [ngClass]="{ 'input-error-box': _economicSubstanceService.step2Error.RelevantActivities }">
            <p-table [columns]="cols" [value]="economicsubstance.relevantActivities"
                     [(selection)]="selectedActivity"
                     class="relevant-activities-table"
                     (onRowUnselect)="onUnselection($event)" (onHeaderCheckboxToggle)="onHeaderCheckboxToggle($event)"
                     (onRowSelect)="onSelection($event)">
                <ng-template pTemplate="header" let-columns>
                    <tr>
                        <th style="width: 3em">

                        </th>
                        <th *ngFor="let col of columns">
                            <label style="font-size:14px">{{col.header}}</label>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-activity>
                    <tr [pSelectableRow]="rowData">
                        <td>
                            <p-tableCheckbox id="step2ESComponentrRelevantActivity" [value]="rowData"
                                             [disabled]="checkvalue() && rowData.relevantActivityName!=='None'">

                            </p-tableCheckbox>
                        </td>

                        <td>

                            {{rowData['relevantActivityName']}}
                        </td>
                        <td>
                            <p-checkbox id="step2ESComponentFinancialPeriod" *ngIf="IfItemBeingSelected(activity)" [(ngModel)]="activity.isCarriedForPartFinancialPeriod"
                                        [ngModelOptions]="{standalone: true}" binary="false"></p-checkbox>
                        </td>
                        <td>

                            <p-calendar id="step2ESComponentStartDate" [utc]="true" [showIcon]="true" class="p-calender-properties" [ngModelOptions]="{standalone: true}"
                                        dataType="string"
                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                        *ngIf="activity.isCarriedForPartFinancialPeriod" dateFormat="{{dateFormatString}}" placeholder="dd/MM/yyyy"
                                        [(ngModel)]="activity.startDateString"></p-calendar>
                        </td>
                        <td>
                            <p-calendar id="step2ESComponentEndDate" [utc]="true" [showIcon]="true" class="p-calender-properties" [ngModelOptions]="{standalone: true}"
                                        dataType="string"
                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                        *ngIf="activity.isCarriedForPartFinancialPeriod" dateFormat="{{dateFormatString}}" placeholder="dd/MM/yyyy"
                                        [(ngModel)]="activity.endDateString"></p-calendar>
                        </td>

                    </tr>
                </ng-template>
                <ng-template pTemplate="summary">
                    <ul>
                        <li *ngFor="let item of selectedActivity" style="text-align: left">
                            {{item.relevantActivityName}}

                        </li>
                    </ul>
                </ng-template>
            </p-table>
        </div>
        <div *ngIf="readOnlyMode">
            <p-table [columns]="cols" [value]="economicsubstance.relevantActivities" class="relevant-activities-table">
                <ng-template pTemplate="header" let-columns>
                    <tr>
                        <th style="width: 3em">
                        </th>
                        <th *ngFor="let col of columns">
                            {{col.header}}
                        </th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-rowData let-activity>
                    <tr [pSelectableRow]="rowData">
                        <td>
                            <p-checkbox id="step2ESComponentActivityName" [(ngModel)]="activity.isChecked" binary="false" readonly="true"></p-checkbox>
                        </td>
                        <td>
                            {{activity.relevantActivityName}}
                        </td>

                        <td>
                            <p-checkbox id="step2ESComponentPeriod" [(ngModel)]="activity.isCarriedForPartFinancialPeriod" binary="false" readonly="true"></p-checkbox>
                        </td>
                        <td>

                            <div *ngIf="activity.isCarriedForPartFinancialPeriod && !importOnlyMode">
                                <label>{{activity.startDate | date: "dd/MM/yyyy"}} </label>
                            </div>

                            <div *ngIf="activity.isCarriedForPartFinancialPeriod && importOnlyMode">
                                <label>{{activity.startDateString}} </label>
                            </div>
                        </td>
                        <td>
                            <div *ngIf="activity.isCarriedForPartFinancialPeriod && !importOnlyMode ">
                                <label>{{activity.endDate | date: "dd/MM/yyyy "}} </label>
                            </div>

                            <div *ngIf="activity.isCarriedForPartFinancialPeriod && importOnlyMode">
                                <label>{{activity.endDateString}} </label>
                            </div>

                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>


    


    </p-card>
</div>
    
