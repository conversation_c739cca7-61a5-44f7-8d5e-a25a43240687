<div [ngClass]="{'col-flex p-card-properties_form':!readOnlyMode,'col-flex p-card-properties_form_readonly':readOnlyMode}">
    <form #step1="ngForm">
        <p-card [ngClass]="{'p-card-properties_form':!readOnlyMode,'p-card-properties_form_readonly':readOnlyMode}">
            <p-header>
                {{l('ESFiscalPerios')}}
            </p-header>
            <div class="row-flex-justified-largefont">
                <div>{{l('ESFiscalYearConfirmQuestion')}}<span class="required">*</span></div>
            </div>
            <div class="row-flex-justified" >
                <div *ngIf="!readOnlyMode" [ngClass]="{ 'input-error-box': _economicSubstanceService.step1Error.IsFinaincialCanChange }" >
                    <div class="radio-input">
                        <div>
                            <p-radioButton name="isFinaincialCanChange"
                                           [value]="true"
                                           [(ngModel)]="economicsubstance.isFinaincialCanChange" id="isFinaincialCanChangeT"
                                           (onClick)="ChoseFinaicialPeriod($event)">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton name="isFinaincialCanChange" [value]="false"
                                           [(ngModel)]="economicsubstance.isFinaincialCanChange"
                                           id="isFinaincialCanChangef"
                                           (onClick)="ChoseFinaicialPeriod($event)">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                </div>
                <div *ngIf="readOnlyMode">
                    <label class="p-readonly-label">{{economicsubstance.isFinaincialCanChange!==null?(economicsubstance.isFinaincialCanChange?'Yes':'No'):'' }} </label>
                </div>
            </div>

            <div *ngIf="ShowHideResult(0)">

                <div class="row-flex-justified-largefont">
                    <label>{{l("ESFiscalPeriosStartDate")}} <span class="required">*</span> </label>
                </div>
                <div class="row-flex-justified">
                    <div *ngIf="!readOnlyMode" [ngClass]="{ 'input-error-box': _economicSubstanceService.step1Error.FiscalStartDate }">
                        <span>
                            <p-calendar class="p-calender-properties" [utc]="true" name="fiscalStartDate"
                                        dataType="string"
                                        appendTo="body" dateFormat="{{dateFormatString}}" id="fiscalStartDate"
                                        [yearNavigator]="true" [monthNavigator]="true" [yearRange]="getYearRange()"
                                        (ngModelChange)="setWarningFirst()"
                                        [(ngModel)]="economicsubstance.fiscalStartDateString"
                                        [showIcon]="true" placeholder="dd/MM/yyyy">
                            </p-calendar>
                        </span>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.fiscalStartDate| date: "dd/MM/yyyy"}} </label>
                    </div>
                    
                </div>


                <div class="row-flex-justified-largefont">
                    <label>{{l("ESFiscalPeriosEndDate")}} <span class="required">*</span> </label>
                </div>
                    <div class="row-flex-justified" >
                        <div *ngIf="!readOnlyMode" [ngClass]="{ 'input-error-box': _economicSubstanceService.step1Error.FiscalEndDate }">
                            <div [ngClass]="{ 'input-warnning-box': _economicSubstanceService.step1Error.FiscalEndDateWarning }">
                                <span>
                                    <p-calendar class="p-calender-properties" [utc]="true" name="fiscalEndDate" appendTo="body"
                                                dataType="string"
                                                dateFormat="{{dateFormatString}}" id="fiscalEndDate"
                                                [yearNavigator]="true"  (ngModelChange)="setWarningFirst()" [monthNavigator]="true" [yearRange]="getYearRange()"
                                                [(ngModel)]="economicsubstance.fiscalEndDateString" [showIcon]="true" placeholder="dd/MM/yyyy">
                                    </p-calendar>
                                </span>
                            </div>
                        </div>

                        <div *ngIf="readOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.fiscalEndDate| date: "dd/MM/yyyy"}} </label>
                        </div>
                    </div>
                
            </div>
            </p-card>

    </form>
</div>
