<!--only display the current one if available-->


<!--display all the other history one-->

<div class="col-flex">
    <p-card
        [ngClass]="{'p-card-properties_form margin-top':!readOnlyMode,'p-card-properties_form_readonly margin-top':readOnlyMode}">


        <!--Display the history exclue the current one-->
        <div *ngFor="let item of informationRequiredsHistory">
            <div class="row-flex-justified ">
                <label class="ac-pheader">{{l('RequestedInformations')}}</label>
            </div>
            <hr>
            <div class="row-flex-justified-largefont">
                <label>{{l('UploadInformationToCA')}}</label>
            </div>
            <div class="row-flex-justified">
                <app-upload-files [Documents]="item.economicSubstanceInformationRequiredDocuments"
                    [DocumentTypeName]="InformationRequestedDocumentDoc" [IsEconomicDocument]="true"
                    [readOnlyMode]="readOnlyMode" [displayFromCa]="displayFromCa" [ctspId]="ctspId">
                </app-upload-files>
            </div>           
            <div class="row-flex-justified-largefont">
                <label>{{l('CTSPCommentsDateTime')}}</label>
            </div>
            <div class="row-flex-justified">
                <label class="p-readonly-label">{{returnDate(item.informationRequestedSubmissionDate) | date: "dd/MM/yyyy HH:mm"}} </label>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CTSPCommentsTitle')}}</label>
            </div>
            <div class="row-flex-justified">
                <textarea pInputTextarea readonly="true" [(ngModel)]="item.informationRequestedCommentsCTSP"
                    style="background-color : #f0f0f0 !important" rows="3"></textarea>
            </div>
            <hr> 
            <div class="row-flex-justified-largefont">
                <label>{{l('DueDateInformation')}}</label>
            </div>
            <div class="row-flex-justified">
                <label class="p-readonly-label">{{item.informationRequestedDueDate| date: "dd/MM/yyyy"}} </label>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CACommentsDateTime')}}</label>
            </div>
            <div class="row-flex-justified">
                <label class="p-readonly-label">{{returnDate(item.createdAt) | date: "dd/MM/yyyy HH:mm"}} </label>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CAComments')}}</label>
            </div>
            <div class="row-flex-justified">
                <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true"
                    [(ngModel)]="item.informationRequestedCommentsCA" style="background-color : #f0f0f0 !important"
                    rows="3"></textarea>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CAAttachments')}}</label>
            </div>
            <div class="row-flex-justified">
                <app-upload-files [Documents]="item.informationRequestDocumentCas"
                    [DocumentTypeName]="InformationRequestedDocumentDoc" [IsEconomicDocument]="false"
                    [readOnlyMode]="readOnlyMode" [displayFromCa]="displayFromCa" [ctspId]="ctspId">
                </app-upload-files>
            </div> 
        </div>

        <!--only display the active one and should be in non read mode-->
        <div *ngIf="informationRequired">
            <div class="row-flex-justified ">
                <label class="ac-pheader"> {{l('RequestedInformations')}}</label>
            </div>            
            <hr>            
            <div class="row-flex-justified-largefont">
                <label>{{l('UploadInformationToCA')}}</label>
            </div>
            <div *ngIf="!displayFromCa" class="row-flex-justified-rows"
                [ngClass]="{ 'input-error-box': _economicSubstanceService.InformationRequired }">
                <div class="row-flex-justified">
                    <app-upload-files [Documents]="informationRequired.economicSubstanceInformationRequiredDocuments"
                        [DocumentTypeName]="InformationRequestedDocumentDoc" [IsEconomicDocument]="true"
                        [readOnlyMode]="!readOnlyMode" [displayFromCa]="displayFromCa" [ctspId]="ctspId">
                    </app-upload-files>
                </div>
                <div class="row-flex-justified-largefont">
                    <label>{{l('CTSPComments')}}</label>
                </div>
                <div class="row-flex-justified">
                    <textarea pInputTextarea rows="10" maxlength="4000"
                        [(ngModel)]="informationRequired.informationRequestedCommentsCTSP"></textarea>
                </div>
                <div class="charCounter">{{informationRequired.informationRequestedCommentsCTSP?.length || 0}}/4000
                </div>
            </div>           
            <hr>
            <div class="row-flex-justified-largefont">
                <label>{{l('DueDateInformation')}}</label>
            </div>
            <div class="row-flex-justified">
                <label class="p-readonly-label">{{informationRequired.informationRequestedDueDate| date: "dd/MM/yyyy"}}
                </label>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CACommentsDateTime')}}</label>
            </div>
            <div class="row-flex-justified">
                <label class="p-readonly-label">{{returnDate(informationRequired.createdAt) | date: "dd/MM/yyyy HH:mm"}}
                </label>
            </div>
            <div class="row-flex-justified">
                <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true"
                    [(ngModel)]="informationRequired.informationRequestedCommentsCA"
                    style="background-color : #f0f0f0 !important" rows="3"></textarea>
            </div>
            <div class="row-flex-justified-largefont">
                <label>{{l('CAAttachments')}}</label>
            </div>
            <div class="row-flex-justified">
                <app-upload-files [Documents]="informationRequired.informationRequestDocumentCas"
                    [DocumentTypeName]="InformationRequestedDocumentDoc" [IsEconomicDocument]="false"
                    [readOnlyMode]="readOnlyMode" [displayFromCa]="displayFromCa" [ctspId]="ctspId">
                </app-upload-files>
            </div>            
        </div>
    </p-card>
</div>