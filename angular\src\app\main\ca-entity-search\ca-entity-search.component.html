<div class="row-flex-space" [@routerTransition]>
    <p-card class="p-card-properties_form">
        <p-header class="row-flex-space">
            <p>{{title}}</p>
            <div class="row-flex-justified">
                <button pButton type="button" icon="pi pi-arrow-left" class="margin-left bigger-icons" iconPos="left"
                    (click)="goToNonResidentDashboard($event)" tooltip="{{'Back' | localize}}"></button>
            </div>
        </p-header>
        <div class="margin-top">
            <div class="primeng-datatable-container" [busyIf]="primengTableHelper.isLoading">
                <p-table #dataTable (onLazyLoad)="getResults($event)" [value]="primengTableHelper.records"
                    rows="{{primengTableHelper.defaultRecordsCountPerPage}}" [paginator]="false" [lazy]="true"
                    [scrollable]="true" ScrollWidth="100%" selectionMode="single" (onRowSelect)="onRowSelect($event)">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 50px" pSortableColumn="name">
                                {{'Corporate Entity Name' | localize}}
                                <p-sortIcon field="name"></p-sortIcon>
                            </th>
                            <th style="width: 50px" pSortableColumn="companyNumber">
                                {{'Corporate Entity Number' | localize}}
                                <p-sortIcon field="companyNumber"></p-sortIcon>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-record="$implicit">
                        <tr [pSelectableRow]="record">
                            <td style="width: 50px">
                                {{record.name}}
                            </td>
                            <td style="width: 50px">
                                {{record.companyNumber}}
                            </td>

                        </tr>
                    </ng-template>
                </p-table>
                <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                    {{'NoData' | localize}}
                </div>
                <div class="primeng-paging-container">
                    <p-paginator [rows]="primengTableHelper.defaultRecordsCountPerPage" #paginator
                        (onPageChange)="getResults($event)" [totalRecords]="primengTableHelper.totalRecordsCount">
                    </p-paginator>
                    <span class="total-records-count">
                        {{'TotalRecordsCount' | localize:primengTableHelper.totalRecordsCount}}
                    </span>
                </div>
            </div>
        </div>
    </p-card>
</div>