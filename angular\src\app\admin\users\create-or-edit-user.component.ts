import { Component, EventEmitter, Injector, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AccountServiceProxy, AuditEntity, CreateOrUpdateUserInput, SendPasswordResetCodeInput, SingleFieldInputOfString, UserEditDto, UserEntityDto, UserRoleDto, UserServiceProxy, UserValidationDto } from '@shared/service-proxies/service-proxies';
import { EntityChangeHistoryModalComponent } from 'app/shared/common/audit/entity-change-history-modal.component';
import { CountryISO, SearchCountryField, TooltipLabel } from 'ngx-intl-tel-input';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';

@Component({
    selector: 'createOrEditUser',
    templateUrl: './create-or-edit-user.component.html'
})
export class CreateOrEditUserComponent extends AppComponentBase {
    SearchCountryField = SearchCountryField;
    TooltipLabel = TooltipLabel;
    CountryISO = CountryISO;
    selectedCountryISO: string = CountryISO.BritishVirginIslands;
    preferredCountries: CountryISO[] = [CountryISO.BritishVirginIslands];

    @ViewChild('userFormValidation', { static: true }) userFormValidation: NgFormValidationComponent;
    @ViewChild('userForm', { static: true }) userForm: FormGroup;
    @ViewChild('entityChangeHistoryModal', { static: true }) entityChangeHistoryModal: EntityChangeHistoryModalComponent;

    @Output() onSave: EventEmitter<any> = new EventEmitter<any>();

    validatingEmail = false;
    validatingUsername = false;
    saving = false;

    user: UserEditDto = new UserEditDto();
    roles: UserRoleDto[];
    phoneNumber: any;

    constructor(
        injector: Injector,
        private _userService: UserServiceProxy,
        private _accountService: AccountServiceProxy
    ) {
        super(injector);
    }

    ngOnInit() {
        this.userFormValidation.formGroup = this.userForm;
    }

    get(userId?: string, userIdInMaster?: number, userName?: string): void {
        this.userForm.reset();

        this._userService.getUserForEdit(userId, userIdInMaster, userName).subscribe(userResult => {
            this.user = userResult.user;
            this.roles = userResult.roles;

            this.phoneNumber = this.user.phoneNumber;

            if (!this.user.roleId) {
                this.user.roleId = this.roles[0].roleId;
            }
        });
    }

    hasError(fieldName: string): boolean {
        return this.userFormValidation.fieldHasErrors(fieldName);
    }

    onUpdateEmail(): void {
        if (this.user.emailAddress == null || this.user.emailAddress == '') {
            return;
        }

        if (this.user.idInMaster == null && (this.user.userName == null || this.user.userName == '')) {
            this.user.userName = this.user.emailAddress;
            this.onUpdateUsername();
        }

        var control = this.userForm.controls["EmailAddress"];
        if (control) {
            control.markAsTouched();

            var input = new UserValidationDto();
            input.id = this.user.idInMaster;
            input.emailAddress = this.user.emailAddress;

            this.validatingEmail = true;
            this._userService.validateEmail(input)
                .pipe(finalize(() => { this.validatingEmail = false; }))
                .subscribe((result) => {
                    if (result.hasErrors) {
                        control.setErrors({
                            customErrors: {
                                errors: result.errors
                            }
                        });
                    }
                });
        }
    }

    onUpdateUsername(): void {
        if (this.user.userName == null || this.user.userName == '') {
            return;
        }

        var control = this.userForm.controls["Username"];
        if (control) {
            control.markAsTouched();

            var input = new UserValidationDto();
            input.id = this.user.idInMaster;
            input.userName = this.user.userName;

            this.validatingUsername = true;
            this._userService.validateUsername(input)
                .pipe(finalize(() => { this.validatingUsername = false }))
                .subscribe((result) => {
                    if (result.hasErrors) {
                        control.setErrors({
                            customErrors: {
                                errors: result.errors
                            }
                        });
                    }
                });
        }
    }

    save(): void {
        if (!this.userFormValidation.isFormValid()) {
            return;
        }

        let input = new CreateOrUpdateUserInput();

        input.user = this.user;
        input.user.phoneNumber = this.phoneNumber.internationalNumber;
        
        this.saving = true;
        this._userService.createOrUpdateUser(input)
            .pipe(finalize(() => { this.saving = false; }))
            .subscribe((result) => {
                this.notify.info(this.l('SavedSuccessfully'));
                this.onSave.emit(null);
                this.user.idInMaster = result.userId;
                this.get();
            });
    }

    remove(): void {
        this.message.confirm(
            this.l('UserRemoveWarningMessage', this.user.userName),
            this.l('AreYouSure'),
            (isConfirmed) => {
                if (isConfirmed) {
                    this._userService.deleteUser('00000000-0000-0000-0000-000000000000', this.user.idInMaster, this.user.userName)
                        .subscribe(() => {
                            this.onSave.emit(null);
                            this.notify.success(this.l('SuccessfullyRemoved'));
                            this.user.isActive = false;
                        });
                }
            }
        );
    }

    resetPassword(): void {
        this.message.confirm(
            this.l('UserResetPasswordWarningMessage', this.user.userName),
            this.l('AreYouSure'),
            (isConfirmed) => {
                if (isConfirmed) {
                    const model = new SendPasswordResetCodeInput();
                    model.userName = this.user.userName;
                    this._accountService.sendPasswordResetCode(model)
                        .subscribe(() => {
                            this.notify.success(this.l('SuccessfullyResetPassword'));
                        });
                }
            }
        );
    }

    changeHistory(): void {
        this.entityChangeHistoryModal.show(AuditEntity.User, this.user.idInMaster.toString());
    }
}
