

    <div *ngIf="readytoDisplay" class="app-display-submitted-container">
        <div [ngStyle]="getButtonBarStyle(printService.isPrintMode)" class="row-flex-end margin-top-wide">
            <div *ngIf="isCtspPoratal"  [tooltip]="currentEconomicSubstance.isEmailSentToReopen ? 'Email already sent' : 'Request Amendment'">
                <button id="SendReopenRequest" [disabled]="currentEconomicSubstance.isEmailSentToReopen" pButton type="button"icon="pi pi-envelope" class="ui-button-rounded ui-button-secondary margin-right bigger-icons" iconPos="left"
                (click)="requestAmendment()" ></button>
            </div>
            <div *ngIf="isCtspPoratal">
                <button id="generateExcel" pButton type="button" icon="pi pi-file-excel" class="ui-button-rounded ui-button-secondary margin-right bigger-icons" iconPos="left"
                [disabled]="disableButtons" (click)="generateExcel()" tooltip="Export Declaration"></button>
                
            </div>
           
            <button id="displaySubmittedOnPrint" pButton type="button" icon="pi pi-print" class="ui-button-rounded ui-button-secondary margin-right bigger-icons" iconPos="left"
                    (click)="onPrint()" tooltip="Print"></button>
            <button id="displaySubmittedOnClose" pButton type="button" class="ui-button-rounded ui-button-secondary margin-right bigger-icons"
                    (click)="onClose()" icon="pi pi-times" tooltip="Close"> </button>
        </div>


        <p-card *ngIf="_economicSubstanceService.errorList && _economicSubstanceService.errorList.length > 0 "
                class="p-card-error">
            <p-header>
                <div>
                    <i class="pi pi-exclamation-triangle margin-right margin-left"></i><label>{{l("Validation Errors")}}</label>
                </div>
            </p-header>
            <div class="col-flex">
                <div *ngFor="let error of _economicSubstanceService.errorList">
                    <p>{{error}}</p>
                </div>
            </div>
        </p-card>
        <div>
            <app-main-economics-reo [corporateEntity]="corporateEntity"
                                    [currentEconomicSubstance]="currentEconomicSubstance"
                                    [informationRequired]="informationRequired"
                                    [informationRequiredsHistory]="informationRequiredsHistory"
                                    [esassessment]="esassessment"
                                    [displayHeader]="true"
                                    [displayFromCa]="false"
                                    [importOnlyMode]="importOnlyMode">
            </app-main-economics-reo>
        </div>


        <p-card>
            <div *ngIf="(currentEconomicSubstance.approveRejectProvisionalTreatment && !checkIfProvisionalTreatmentSubmission(currentEconomicSubstance))">
                <button id="displaySubmit" pButton type="button" class="ui-button-rounded ui-button-warning margin-right"
                        (click)="onSubmitForProvisionalTreatment()" label="Submit"></button>
            </div>
        </p-card>
        <p-card>
            <div *ngIf="checkIfinformationRequired()">
                <button id="displaySubmitForRequestInformation" pButton type="button" class="ui-button-rounded ui-button-warning margin-right"
                        (click)="onSubmitForRequestInformation()" label="Submit"></button>
            </div>
        </p-card>


    </div>
    <div bsModal #reopenModal="bs-modal" class="modal fade centered-modal" tabindex="-1" role="dialog" aria-labelledby="reopenReasonModalLabel"
    aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog">
        <div class="modal-content">
            <form #reopenReasonForm="ngForm" novalidate (ngSubmit)="saveAmendmentRequest()" class="kt-form kt-form--label-right">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <span><strong>Request Reopen Declaration</strong></span>
                    </h5>
                </div>
                <div class="modal-body">
                    <p class="mb-4">Are you sure you want to reopen the declaration? Please enter the reason for reopening:</p>
                    <div class="form-group">
                        <textarea class="form-control" id="reopenReason" name="reopenReason" [(ngModel)]="reopenReason" rows="5" maxlength="250"></textarea>
                        <div *ngIf="errorMessage"  class="ra-advancedsearch-searchcriteria-errormessage ui-g-12 ui-g-nopad bdo-errormessage" role="alert">
                            {{errorMessage}}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="closeReopenReasonModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" [disabled]="!reopenReasonForm.form.valid">
                        <i class="fa fa-arrow-circle-right"></i>
                        <span>Yes</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


