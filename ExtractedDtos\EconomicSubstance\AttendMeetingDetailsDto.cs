
    public class AttendMeetingDetailsDto : EntityDtoBase
    {
        public int? MeetingNo { get; set; }
        public string MeetingNoString { get; set; }
        public string Name { get; set; }

        public bool? IsPhysicallyPresent { get; set; }

        public string IsPhysicallyPresentString { get; set; }

        public string RelationToEntity { get; set; }
        public string Qualification { get; set; }
        public bool IsNew { get; set; } = false;

    }

 
