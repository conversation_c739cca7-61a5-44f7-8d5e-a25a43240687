<div *ngIf="readytoDisplay">
    <div [ngStyle]="getButtonBarStyle(printService.isPrintMode)" class="row-flex-end margin-top-wide">
        <button pButton type="button" icon="pi pi-print" class="ui-button-rounded ui-button-secondary margin-right bigger-icons" iconPos="left"
                (click)="onPrint()" tooltip="Print"></button>
        <button pButton type="button" class="ui-button-rounded ui-button-secondary margin-right bigger-icons"
                (click)="onClose()" icon="pi pi-times" tooltip="Close"> </button>
    </div>

    <div>
        <div *ngIf="ctspIdisplay === '-1'">
            <app-main-economics-reo [corporateEntity]="corporateEntity"
                                    [currentEconomicSubstance]="currentEconomicSubstance"
                                    [displayHeader]="true"
                                    [displayFromCa]="false"
                                    [importOnlyMode]="importOnlyMode">
            </app-main-economics-reo>
        </div>


        <div *ngIf="ctspIdisplay !== '-1'">
            <app-main-economics-reo [corporateEntity]="corporateEntity"
                                    [currentEconomicSubstance]="currentEconomicSubstance"
                                    [displayHeader]="true"
                                    [displayFromCa]="false"
                                    [importOnlyMode]="importOnlyMode"
                                    [historyDisplay]="true"
                                    [ctspId]="ctspIdisplay"  >
            </app-main-economics-reo>
        </div>

    </div>
</div>
