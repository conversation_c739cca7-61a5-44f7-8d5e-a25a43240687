import { Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AddressDto, CorporateEntityDto, CountryDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../../shared/utils/validation/ng-form-validation.component';



@Component({
    selector: 'app-address-detail-dialog',
    templateUrl: './address-detail-dialog.component.html',
    styleUrls: ['./address-detail-dialog.component.css']
})
export class AddressDetailDialogComponent extends AppComponentBase implements OnInit {

    @Input() public corporateEntity: CorporateEntityDto;
    @Input() readOnlyMode: boolean;
    @Output() submitted: EventEmitter<AddressDto> = new EventEmitter<AddressDto>();
    details: AddressDto = new AddressDto();
    country: CountryDto = new CountryDto();
    @ViewChild('addressmodal', { static: true }) modal: ModalDirective;
    @ViewChild('addressform', { static: true }) addressform: FormGroup;
    @ViewChild('addressFormValidation', { static: true }) addressFormValidation: NgFormValidationComponent;
    isSelected: boolean = false;
    countryOld: CountryDto = new CountryDto();
    constructor(injector: Injector) { super(injector); }

    ngOnInit() {
        this.addressFormValidation.formGroup = this.addressform;
    }
    shown(): void {
    }
    show(item, country, isNew): void {
        this.country = _.cloneDeep(country);
        this.countryOld = _.cloneDeep(this.country);
        this.details = new AddressDto();
        if (!isNew) {
            this.details = _.cloneDeep(item);
            this.country = this.details.country;
            if (item && this.corporateEntity && item.addressLine1 == this.corporateEntity.addressLine1 && item.addressLine2 == this.corporateEntity.addressLine2 && item.countryId == this.corporateEntity.countryId) {
                this.isSelected = true;
            } else {
                this.isSelected = false;
            }
        }
        else {
            this.isSelected = false;
        }
        this.modal.show();
    }

    close(): void {
        this.addressform.reset();
        this.modal.hide();
    }

    save(): void {
        if (!this.addressFormValidation.isFormValid()) {
            return;
        }
        if (!this.details.id) { this.details.id = uuid.v4(); this.details.isNew = true; }
        this.details.country = this.country;
        this.details.countryId = this.country.id;
        this.submitted.emit(this.details);
        this.close();
    }

    onSelectCheckbox(event) {
        if (event && this.corporateEntity) {
            this.details.addressLine1 = this.corporateEntity.addressLine1;
            this.details.addressLine2 = this.corporateEntity.addressLine2;
            this.details.countryId = this.corporateEntity.countryId;
            let countryDetails = new CountryDto();
            countryDetails.id = this.corporateEntity.countryId;
            countryDetails.countryName = this.corporateEntity.countryName;
            this.country = countryDetails;
        }
        else {
            this.details.addressLine1 = "";
            this.details.addressLine2 = "";
            this.details.countryId = null;
            this.country = _.cloneDeep(this.countryOld);
        }
    }

}
