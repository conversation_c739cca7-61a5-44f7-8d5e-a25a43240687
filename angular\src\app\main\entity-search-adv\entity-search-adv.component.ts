import { Component, OnInit, Injector, Input, Output, EventEmitter } from '@angular/core';
import { Control<PERSON>ontainer, NgForm } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { LookupInfo } from './model/lookupInfo';
import { SelectItem } from 'primeng/api';
import { CASearchServiceServiceProxy, CorporateEntityServiceProxy, GetAdvEntitiesInput, SearchExpression } from '@shared/service-proxies/service-proxies';
import { DateTimeHelper } from './helper/dateimeHelper'
import { lastNumberOfYears, getRandomKey } from '../viewmodel/utils'

@Component({
  selector: 'app-entity-search-adv',
  templateUrl: './entity-search-adv.component.html',
  styleUrls: ['./entity-search-adv.component.less'],
  viewProviders: [{ provide: ControlContainer, useExisting: NgForm }]
})
export class AdvancedSearchComponent extends AppComponentBase implements OnInit {
  financialPeriodEndYears: SelectItem[] = [];
  
  @Input() hidden: boolean = false;
  @Input() getAdvEntitiesInput: GetAdvEntitiesInput = new GetAdvEntitiesInput();
  @Input() isForCASearch: boolean = false;
  @Input() supportRandomSampling: boolean = false;
  @Output() onAdvSearch: EventEmitter<any> = new EventEmitter();

  canSubmit: boolean = true;
  errorMessage: string = '';
  errorMessageSampling: string = '';
  basicErrorMessage: string = null;
  searchableProperties: {};
  searchablePropertyOptions: any[] = [];
  sampleSize: number = 50;
  showSamplingSize: boolean = false;
  defaultSelectionOption: any = { value: '', label: this.l('- Select -') };

  //search criteria objects
  criteriaArray: any = []; //used to render the criteria grid
  inGroup: number = 4;
  startGroup: any = [];
  canGroup: boolean;
  canUngroup: boolean;
  currentConnector: any;
  groupRange: any = null;
  currentgrouprange: any = null;

  //lookup tables for dropdowns
  lookupTable = {
    connector: [],
    searchTypes: [],
    historyRanges: [],
    country: [],
    boentitytype: [],
    entitytype: [],
    stockexchange: [],
    relevantActivity: [],
    jurisdiction: [],
    Status: []
  }

  submitOnEnterDisabled: boolean = false;

  constructor(private injector: Injector,
    //private router: Router,
    //private route: ActivatedRoute,
    //private searchService: SearchService,
    private cASearchServiceServiceProxy: CASearchServiceServiceProxy,
    private corporateEntityServiceProxy: CorporateEntityServiceProxy    
    ) {
    super(injector);
  }

  ngOnInit() {
    const currentYear = new Date().getFullYear();
    this.financialPeriodEndYears = lastNumberOfYears(currentYear, 10).map(x => ({ label: x.toString(), value: x }));

    this.initializeLookupTables();
    this.setCriteriaArray();
    this.getAdvEntitiesInput.includeRequiringDeclaration = true;
    this.getAdvEntitiesInput.financialPeriodEndYear = currentYear;
  }

  setSearch(): boolean {
    if (this.submitOnEnterDisabled)
      return false;

    if (!this.validateSearchCriteriaArray()) return false;

    if (this.validateBasicCriteria()) {
      try {
        this.bindSearchRequestToModel();
        return true;
      }
      catch(err){
        this.canSubmit = true;
        this.errorMessage = err.message;
      }
    }

    return false
  }

  
  initializeLookupTables() {
    this.lookupTable.connector = this.getConnectorOptions();

    if (this.isForCASearch){
      // need to load from CA services!!!
      this.cASearchServiceServiceProxy.getCaSearchableProperties(null).subscribe(result => {
        this.searchableProperties = {};
        this.searchablePropertyOptions = [];
        this.searchablePropertyOptions.push(this.defaultSelectionOption);
        result.forEach(item => {
          let numKey: Number = Number(item.key);
          this.searchableProperties[Number(item.key)] = item.value;
          this.searchablePropertyOptions.push(
            { 
              value: numKey, label: item.value.displayName
            }
          )
        });
      });
      this.cASearchServiceServiceProxy.getCaSearchLookup(null).subscribe(result => {
        result.forEach(item => {
          var options = [];
          options.push(this.defaultSelectionOption);
          (item.value||[]).forEach(element => {
            options.push({ value: element.key, label: element.value });      
          });
          this.lookupTable[item.key.toLowerCase()] = options;
        });
      });
    }
    else {
      this.corporateEntityServiceProxy.getSearchableProperties(null).subscribe(result => {
        this.searchableProperties = {};
        this.searchablePropertyOptions = [];
        this.searchablePropertyOptions.push(this.defaultSelectionOption);
        result.forEach(item => {
          let numKey: Number = Number(item.key);
          this.searchableProperties[Number(item.key)] = item.value;
          this.searchablePropertyOptions.push(
            { 
              value: numKey, label: item.value.displayName
            }
          )
        });
      });

      this.corporateEntityServiceProxy.getSearchLookup(null).subscribe(result => {
        result.forEach(item => {
          var options = [];
          options.push(this.defaultSelectionOption);
          (item.value||[]).forEach(element => {
            options.push({ value: element.key, label: element.value });      
          });
          this.lookupTable[item.key.toLowerCase()] = options;
        });
      });
    }
  }

  getDropDownList(values: LookupInfo[], includeSelectOption: boolean = false) {
    if (!values || values.length == 0)
      return [];

    var list = values.map(x => { return { value: x.value, label: x.name }; });

    if (includeSelectOption) {
      list = [this.defaultSelectionOption].concat(list);
    }

    return list;
  }

  //search criteria helpers
  //TODO: move to common helper

  setCriteriaArray() {
    this.getCriteriaItems(this.getAdvEntitiesInput.searchExpression);

    this.criteriaArray.push({
      connector: '',
      field: '',
      hideDelete: true,
      group: [],
      checked: false
    });

    this.criteriaArray[0].hideConnector = true;
  }

  getCriteriaItems(exp: any) {
    if (!exp)
      return;
    if (exp.field) {
      // it is Basic
      var newItem = {
        field: exp.field,
        method: exp.searchMethod,
        value: exp.value,
        options: this.getOptions(exp.field),
        connector: this.currentConnector,
        dataType: exp.dataType || (this.searchableProperties[exp.field] || {}).dataType,
        lookupKey: this.getLookupKey(this.searchableProperties[exp.field].dataType || ''),
        lookupValues: null,
        group: [],
        checked: false
      };
      newItem.lookupValues = this.getLookupValues(newItem.lookupKey);

      if (this.inGroup < 4) {
        newItem.group = [];
        for (var i = 3; i >= this.inGroup; i--) {
          var className = 'group';
          if (this.startGroup[i]) {
            className = 'startgroup';
            this.startGroup[i] = false
          }
          newItem.group[i] = className;
        }
      }

      this.criteriaArray.push(newItem);
    }
    else if (exp.leftExpression) {
      this.getCriteriaItems(exp.leftExpression);
      this.currentConnector = exp.connector;
      this.getCriteriaItems(exp.rightExpression);
    }
    else if (exp.expression) {
      this.inGroup--;
      this.startGroup[this.inGroup] = true;
      this.getCriteriaItems(exp.expression);
      // mark end group
      var lastitem = this.criteriaArray[this.criteriaArray.length - 1];
      if (lastitem && lastitem.group[this.inGroup]) {
        lastitem.group[this.inGroup] = 'endgroup';
      }

      this.inGroup++;
    }
  }

  getOptions(field: number) {
    var options = [];
    let optionsObject = this.searchableProperties[field].options;
    optionsObject.forEach(item => {
      options.push({
        value: item.key,
        label: item.value
      });
    });
    
    return options;
  }

  getLookupValues(lookupKey: string) {
    return this.lookupTable[lookupKey];
  }

  getLookupKey(dataType: string) {
    if (dataType && dataType.startsWith("LookUp[")) {
      var lookupType = dataType.substring(0, dataType.length - 1).substring("LookUp[".length);
      if (lookupType) {
        return lookupType.toLowerCase();
      }
    }

    return '';
  }

  onFilterChanged() {
    this.addNewCriteriaRow();
    this.validateSearchCriteriaArray();
    this.updateGui();
  }

  addNewCriteriaRow() {
    var lastItem = this.criteriaArray[this.criteriaArray.length - 1];
    if (lastItem && lastItem.field && (this.criteriaArray.length <= 1 || lastItem.connector) && (lastItem.value !== "") && lastItem.method) {
      this.criteriaArray = this.criteriaArray.concat([{
        connector: '',
        hideDelete: true,
        group: [],
        field: '',
        checked: false
      }]);
      lastItem.hideDelete = false;
    }
  }

  updateGui() {
    this.setCanGroup();
    this.setCanUngroup();
    this.criteriaArray[0].connector = '';
    this.criteriaArray[0].hideConnector = true;
  }

  setCanGroup() {
    var array = this.criteriaArray;
    var start = -1, end = -1
    this.canGroup = false;

    for (var i = 0; i < array.length; i++) {
      if (array[i].checked) {
        if (end >= 0) {
          // disconnect check
          this.canGroup = false;
          break;
        }
        if (start < 0) {
          start = i;
        }
        else if (start >= 0) {
          // more than one item checked
          this.canGroup = true;
        }
      }
      else if (!array[i].checked && start >= 0 && end < 0) {
        end = i - 1;
      }
    }

    if (this.canGroup) {
      // can't group if any select row already in 3rd level group
      for (var i = start; i <= end; i++) {
        if (array[i].group[1] && array[i].group[2] && array[i].group[3]) {
          this.canGroup = false;
          break;
        }
      }
    }
  }

  setCanUngroup() {
    var array = this.criteriaArray;
    for (var i = 0; i < array.length; i++) {
      if (array[i].checked && (array[i].group[1] || array[i].group[2] || array[i].group[3])) {
        this.canUngroup = true;
        return;
      }
    }
    this.canUngroup = false;
  }
  validateBasicCriteria() {
    return true;
  }

  validateSearchCriteriaArray() {
    this.errorMessage = null;
    var dataArray = this.criteriaArray;

    if (dataArray.length <= 1 && !(dataArray[0].field || dataArray[0].method || dataArray[0].value)) {
      this.errorMessage = this.l("At LeastOne Criteria is Required.");
      return false;
    }

    for (var i = 0; i < dataArray.length; i++) {
      var item = dataArray[i];

      if (!item.field && !item.method && !item.value) {
        // empty criteria row
        continue;
      }

      if (i !== 0 && i === dataArray.length - 1 && !item.connector) {
        // for the last row (not the first) and a connector is not chosen.
        // not considered as a criteria
        continue;
      }

      if (!item.field) {
        this.errorMessage = this.l('Search Field Required', [(i + 1)]);
        return false;
      }

      if (!item.method) {
        this.errorMessage = this.l('Search Operator Required', [(i + 1)]);
        return false;
      }

      if (!(item.value || item.value === 0) && item.dataType != "Boolean" ) {
        this.errorMessage = this.l('Search Value Required', [(i + 1)]);
        return false;
      }

      if (item.method == "Contains" && item.value.length < 2) {
        this.errorMessage = this.l('AtLeast Two Letter Required', [this.searchableProperties[item.field].DisplayName]);
        return false;
      }

      if (i !== 0 && i !== dataArray.length - 1 && !item.connector) {
        // dont check connector for the first and the last criteria
        this.errorMessage = this.l('Search Connector Required', [(i + 1)]);
        return false;
      }

      if (item.dataType == "DateTime") {
        var datestr = item.value.toString();
        var date = new Date(datestr);

        if (isNaN(date.getTime())) {
          this.errorMessage = this.l('Date Invalid');
          return false;
        }
        else {
          this.errorMessage = null;
        }
      }
    }

    return true;
  }

  onSearchFieldChanged(value, index) {
    this.updateFieldOptionsAndType(value, this.criteriaArray[index]);
  }

  updateFieldOptionsAndType(value, record) {
    if (!this.searchableProperties[value])
      return;

    record.options = this.getOptions(value);
    record.method = '';
    if (record.options && record.options.length === 2){
      record.method = record.options[1].value;
    }
    if (record.dataType !== this.searchableProperties[value].dataType) {
      record.value = '';
    }

    record.dataType = this.searchableProperties[value].dataType;

    if (record.dataType === "DateTime") {
      // need to delay a bit to let angular to render the datetimefield
      //setTimeout(function(){ $('.datepicker').datepicker({ dateFormat: 'dd/mm/yy' }); }, 100);
    }
    if (record.dataType === "Boolean") {
      record.lookupValues = this.getBooleanValues();
    }
    else {
      var lookupKey = this.getLookupKey(this.searchableProperties[value].dataType || '');
      record.lookupValues = this.getLookupValues(lookupKey);
      if (record.lookupValues && record.lookupValues .length === 2){
        record.value = record.lookupValues[1].value;
      }
    }
  }

  onFilterDelete(index) {
    var oldItems = this.criteriaArray;
    var removingItem = oldItems[index];
    var prevItem = oldItems[index - 1];
    var nextItem = oldItems[index + 1];
    for (var j = 1; j <= 3; j++) {
      if (removingItem.group[j] == 'endgroup' && prevItem) {
        if (prevItem.group[j] == 'startgroup') {
          delete prevItem.group[j];
        }
        else {
          prevItem.group[j] = 'endgroup'
        }
      }
      if (removingItem.group[j] == 'startgroup' && nextItem) {
        if (nextItem.group[j] == 'endgroup') {
          delete nextItem.group[j];
        }
        else {
          nextItem.group[j] = 'startgroup'
        }
      }
    }
    this.criteriaArray = [];
    for (var i = 0; i < oldItems.length; i++) {
      if (i != index) {
        this.criteriaArray.push(oldItems[i]);
      }
    }

    this.mergeGroups();
    this.updateGui();
  }

  mergeGroups() {
    // find and remove duplicated
    this.buildGroupMap();
    this.removeDuplicated();

    // find and move empty group slot
    this.buildGroupMap();

    for (var i = 2; i <= 3; i++) {
      this.moveEmptyGroupSlot(i);
    }
  }

  buildGroupMap() {
    this.groupRange = [[], [], [], []];
    this.currentgrouprange = [];
    for (var i = 0; i < this.criteriaArray.length; i++) {
      var row = this.criteriaArray[i];
      for (var j = 1; j <= 3; j++) {
        if (row.group[j] == 'startgroup') {
          this.currentgrouprange[j] = { start: i };
        }
        if (row.group[j] == 'endgroup') {
          this.currentgrouprange[j].end = i;
          this.groupRange[j].push(this.currentgrouprange[j]);
          this.currentgrouprange[j] = null;
        }
      }
    }
  }

  removeDuplicated() {
    for (var j = 2; j <= 3; j++) {
      for (var outrange = 0; outrange < this.groupRange[j].length; outrange++) {
        var outR = this.groupRange[j][outrange];
        for (var k = j - 1; k >= 1; k--) {
          for (var inrange = 0; inrange < this.groupRange[k].length; inrange++) {
            var inR = this.groupRange[k][inrange];
            if (outR.start == inR.start && outR.end == inR.end) {
              for (var removing = outR.start; removing <= outR.end; removing++) {
                this.criteriaArray[removing].group[j] = "";
              }
            }
          }
        }
      }
    }
  }

  moveEmptyGroupSlot(level) {
    for (var j = level; j > 1; j--) {
      var canMove = false;
      var groupStart = 0;
      for (var i = 0; i < this.criteriaArray.length; i++) {
        if (this.criteriaArray[i].group[j] == 'startgroup' && !this.criteriaArray[i].group[j - 1]) {
          groupStart = i;
          canMove = true;
        }
        else if (canMove && this.criteriaArray[i].group[j - 1]) {
          canMove = false;
        }
        else if (canMove && this.criteriaArray[i].group[j] == 'endgroup' && !this.criteriaArray[i].group[j - 1]) {
          this.criteriaArray[i].group[j] = "";
          this.criteriaArray[i].group[j - 1] = "endgroup";
          for (var l = groupStart + 1; l < i; l++) {
            this.criteriaArray[l].group[j] = "";
            this.criteriaArray[l].group[j - 1] = "group";
          }
          this.criteriaArray[groupStart].group[j] = "";
          this.criteriaArray[groupStart].group[j - 1] = "startgroup";
        }
      }
    }
  }

  onGroup() {
    var array = this.criteriaArray;
    var start = -1, end = -1;
    for (var i = 0; i < array.length; i++) {
      if (array[i].checked && start < 0) {
        start = i;
      }
      else if (array[i].checked) {
        end = i;
      }
      else if (end >= 0) {
        break;
      }
    }

    if (start < end) {
      // find group level
      var level = 1;
      for (var i = start; i <= end; i++) {
        for (var j = 3; j > 0; j--) {
          if (array[i].group[j]) {
            if (j + 1 > level) {
              level = j + 1;
            }
            break;
          }
        }
      }

      if (level <= 3) {
        if (level > 1) {
          while (array[end].group[level - 1] && end < array.length && array[end].group[level - 1] != 'endgroup') {
            end++;
          }
          while (array[start].group[level - 1] && start >= 0 && array[start].group[level - 1] != 'startgroup') {
            start--;
          }
        }
        array[start].group[level] = 'startgroup';
        array[end].group[level] = 'endgroup';
        for (var i = start + 1; i < end; i++) {
          array[i].group[level] = 'group';
        }
      }
    }

    this.mergeGroups();

    for (var i = 0; i < array.length; i++) {
      array[i].checked = false;
    }

    this.canGroup = false;
    this.canUngroup = false;
  }

  onUngroup() {
    var array = this.criteriaArray;
    for (var i = 0; i < array.length; i++) {
      if (array[i].checked) {
        for (var j = 3; j > 0; j--) {
          if (array[i].group[j]) {
            var up = -1;
            var down = 1;
            array[i].group[j] = "";
            while (array[i + up] && array[i + up].group[j]) {
              var stop = array[i + up].group[j] == 'startgroup';
              array[i + up].group[j] = "";
              up--;
              if (stop) break;
            }
            while (array[i + down] && array[i + down].group[j]) {
              var stop = array[i + down].group[j] == 'endgroup';
              array[i + down].group[j] = "";
              down++;
              if (stop) break;
            }
            array[i].checked = false;
            break;
          }
        }
      }
    }
    this.mergeGroups();
    this.canGroup = false;
    this.canUngroup = false;
    for (var i = 0; i < array.length; i++) {
      array[i].checked = false;
    }
  }

  bindSearchRequestToModel() {
    var items = this.criteriaArray;
    var currentExp = null;
    var expModel = null;
    var allExps = [];

    if (items.length > 0) {
      for (var i = 0; i < items.length - 1; i++) {
        var item = items[i];
        var exp = {
          field: item.field,
          searchMethod: item.method,
          value: item.value,
          _Connector: item.connector
        };

        //if type is DateTime, convert to format dd/MM/yyyy
        if (item.dataType == 'DateTime')
          exp.value = DateTimeHelper.getFormattedDate(exp.value);

        allExps.push(exp);
      }

      for (var level = 1; level <= 3; level++) {
        var grouping = false;
        var groupedExp = null;
        var currentGroupedExp = null;
        var lastExp = null;
        for (var i = 0; i < items.length - 1; i++) {
          var item = items[i];
          if (item.group[level]) {
            if (lastExp != allExps[i]) {
              lastExp = allExps[i];

              //start of group
              if (!grouping) {
                currentGroupedExp = {
                  leftExpression: allExps[i],
                  connector: (allExps[i + 1] && allExps[i + 1]._Connector) || allExps[i]._Connector, 
                };
                groupedExp = {
                  expression: currentGroupedExp,
                  _Connector: allExps[i]._Connector,
                };
                allExps[i] = groupedExp;
                grouping = true;
              }
              //middle of group (i.e. not startgroup and endgroup)
              else if (item.group[level] != 'endgroup' && !allExps[i].expression) {

                var newGroupedExp = {
                  leftExpression: allExps[i],
                  connector: allExps[i]._Connector
                }
                currentGroupedExp.rightExpression = newGroupedExp;
                currentGroupedExp.connector = currentGroupedExp.connector == undefined ? allExps[i]._Connector : currentGroupedExp.connector;
                currentGroupedExp = newGroupedExp;
                allExps[i] = groupedExp;
              }
              //endgroup
              else {
                currentGroupedExp.connector = allExps[i]._Connector;
                currentGroupedExp.rightExpression = allExps[i];
                allExps[i] = groupedExp;
                grouping = item.group[level] != 'endgroup';
              }
            }
            //last expression
            else {
              allExps[i] = groupedExp;
              if (grouping && item.group[level] == 'endgroup') {
                grouping = false;
              }
            }
          }
        }

      }

      var outputExps = [];
      var lastExp = null;
      for (var i = 0; i < allExps.length; i++) {
        if (outputExps.indexOf(allExps[i]) < 0) {
          outputExps.push(allExps[i]);
        }
      }
      currentExp = outputExps[0];
      if (outputExps.length > 1) {
        currentExp = {
          rightExpression: outputExps[outputExps.length - 1],
          connector: outputExps[outputExps.length - 1].connector || outputExps[outputExps.length - 1]._Connector
        }

        for (var i = outputExps.length - 2; i > 0; i--) {
          currentExp.leftExpression = outputExps[i];
          currentExp = {
            rightExpression: currentExp,
            connector: outputExps[i].connector || outputExps[i]._Connector
          }
        }

        currentExp.leftExpression = outputExps[0];
      }
      if (currentExp.expression != null) {
        currentExp = currentExp.expression;
      }

      expModel = this.convertExpressionToModel(currentExp);
    }

    this.getAdvEntitiesInput.searchExpression = expModel;
    
  }

  convertExpressionToModel(exp: any): SearchExpression {
    if (!exp) return null;

    if (exp.field) {
      var exp2 = new SearchExpression();
      exp2.__TypeName = "Basic";
      exp2.field = exp.field;
      exp2.searchMethod = exp.searchMethod;
      exp2.value = exp.value;
      return exp2;
    }
    if (exp.leftExpression) {
      var exp3 = new SearchExpression();
      exp3.__TypeName = "Advanced";
      exp3.connector = exp.connector;
      exp3.leftExpression = this.convertExpressionToModel(exp.leftExpression);
      exp3.rightExpression = this.convertExpressionToModel(exp.rightExpression);
      return exp3;
    }
    if (exp.expression) {
      var exp4 = new SearchExpression();
      exp4.__TypeName = "Group";
      exp4.expression = this.convertExpressionToModel(exp.expression);
      return exp4;
    }

    return null;
  }

  dateValid(date) {
    var d = new Date(date);
    return !isNaN(d.getTime());
  }

  getConnectorOptions() {
    //TODO: retrieve from API
    var options = [];
    options.push(this.defaultSelectionOption);
    options.push({ value: 'AndAlso', label: this.l('And') });
    options.push({ value: 'OrElse', label: this.l('Or') });

    return options;
  }

  getBooleanValues() {
    //TODO: retrieve from API
    var options = [];
    options.push(this.defaultSelectionOption);
    options.push({ value: true, label: this.l('Yes') });
    options.push({ value: false, label: this.l('No') });

    return options;
  }

  verifySampleSize():boolean {
    if (this.sampleSize<1 || this.sampleSize>999)
    {
      this.errorMessageSampling = this.l('Sample size must between 1 and 999.');
      return false;
    }

    this.errorMessageSampling = '';
    return true;
  }

  onSearch(event:any) {
    if (this.setSearch()){
      this.getAdvEntitiesInput.sampleSize = null;
      this.getAdvEntitiesInput.randomKey = getRandomKey();
      this.getAdvEntitiesInput["isForNewTab"] = false;  
      this.getAdvEntitiesInput["tabId"] = null;  
      this.onAdvSearch.emit(this.getAdvEntitiesInput);
    }
  }

  onRandomSampling(event:any) {
    if (this.setSearch()) {
      // pop dialog for sample size
      this.showSamplingSize = true;
    }
  }

  onSearchInNewTab(event:any) {
    if (this.setSearch()){
      this.getAdvEntitiesInput.sampleSize = null;
      this.getAdvEntitiesInput.randomKey = getRandomKey();  
      this.getAdvEntitiesInput["isForNewTab"] = true;  
      this.getAdvEntitiesInput["tabId"] = getRandomKey();  
      this.onAdvSearch.emit(this.getAdvEntitiesInput);
    }
  }

  onExecuteRandomSampling(event:any) {
    if (!this.verifySampleSize()){
      return;
    }
    
    this.getAdvEntitiesInput.sampleSize = this.sampleSize;
    this.getAdvEntitiesInput.randomKey = getRandomKey();
    this.showSamplingSize = false;
    this.getAdvEntitiesInput["isForNewTab"] = false;  
    this.getAdvEntitiesInput["tabId"] = null;  
    this.onAdvSearch.emit(this.getAdvEntitiesInput);
  }
  
  onCancelRandomSampling(event:any) {
    this.showSamplingSize = false;
  }

  onSampleSizeChanged(event:any) {
    this.verifySampleSize();
  }
}

