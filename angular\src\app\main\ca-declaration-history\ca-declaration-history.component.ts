import { Component, OnInit, Input, Injector, ViewChild } from '@angular/core';
import { EconomicSubstanceDeclarationDto, CaEntityListDto, ESReviewStatusDto } from '@shared/service-proxies/service-proxies';
import { AppComponentBase } from '@shared/common/app-component-base';
import { Router } from '@angular/router';
import { ESHistoryComponent } from '@app/shared/common/economicSubstanceHistory/eshistory.component'

@Component({
  selector: 'app-ca-declaration-history',
  templateUrl: './ca-declaration-history.component.html',
  styleUrls: ['./ca-declaration-history.component.css']
})
export class CaDeclarationHistoryComponent extends AppComponentBase implements OnInit {

    @ViewChild('esHistoryModal', { static: true }) esHistoryModal: ESHistoryComponent;

  @Input() declarations: EconomicSubstanceDeclarationDto[];
  @Input() entity: CaEntityListDto


  get sortedDeclarations(): EconomicSubstanceDeclarationDto[] {
    if (!this.declarations) {
      return []
    }
    return this.declarations.sort((a, b) => b.fiscalEndDate.year() - a.fiscalEndDate.year())
  }

  constructor(injector: Injector, private router: Router) {
    super(injector)
  }

  ngOnInit() {
  }


    getstatus(item: any): string
    {
        
    if (!item.esReviewStatus) {
      return this.l("Unknown")
    }
    return item.esReviewStatus.name;
  }

  viewSubmission(item: EconomicSubstanceDeclarationDto) {

    this.router.navigate([`app/economicsubstance/careview/${item.id}/${this.entity.ctspNumber}`])

  }

    showESSHistory(item: EconomicSubstanceDeclarationDto): void {
        this.esHistoryModal.show(item.id, this.entity.ctspNumber );
    }
}
