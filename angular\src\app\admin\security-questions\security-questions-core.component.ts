import { Component, Injector, Input, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ChangeSecurityQuestionsInput, ProfileServiceProxy, SecurityQuestionDto } from '@shared/service-proxies/service-proxies';
import { Subscription, timer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { AppSessionService } from '../../../shared/common/session/app-session.service';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';
import { SelectItem } from 'primeng/api';

@Component({
    selector: 'securityQuestionsCore',
    templateUrl: './security-questions-core.component.html'
})
export class SecurityQuestionsCoreComponent extends AppComponentBase {
    @Input() redirectAfterSave: boolean = false;

    @ViewChild('securityQuestionsFormValidation', { static: true }) securityQuestionsFormValidation: NgFormValidationComponent;
    @ViewChild('securityQuestionsForm', { static: true }) securityQuestionsForm: FormGroup;

    savingSecurityQuestions: boolean = false;
    securityQuestionList: SecurityQuestionDto[] = [];
    securityQuestionList1: SelectItem[] = [];
    securityQuestionList2: SelectItem[] = [];
    securityQuestionList3: SelectItem[] = [];
    securityQuestions: ChangeSecurityQuestionsInput = new ChangeSecurityQuestionsInput();

    securityQuestionsRemainingSeconds: number = 0;
    securityQuestionsDisplayMinutes: number = 0;
    securityQuestionsDisplaySeconds: number = 0;
    securityQuestionsTimerSubscription: Subscription;

    constructor(
        injector: Injector,
        private _profileService: ProfileServiceProxy,
        private _router: Router,
        private _sessionService: AppSessionService
    ) {
        super(injector);
    }

    ngOnInit() {
        this.securityQuestionsFormValidation.formGroup = this.securityQuestionsForm;


        this._profileService.getSecurityQuestions("").subscribe((result) => {
            this.securityQuestionList = result;
            this.resetSecurityQuestionsForm();
        }); 
    }

    securityQuestionHasError(fieldName: string): boolean {
        return this.securityQuestionsFormValidation.fieldHasErrors(fieldName);
    }

    resetSecurityQuestionsForm(): void {
        this.securityQuestionsForm.reset({
            SecurityQuestion1: this.securityQuestionList[0].id,
            SecurityQuestion2: this.securityQuestionList[1].id,
            SecurityQuestion3: this.securityQuestionList[2].id,
        });

        this.securityQuestions.twoFactorProvider = this.twoFactorProviders[0].value;
        this.securityQuestions.hasTwoFactorCode = false;
        this.securityQuestionsRemainingSeconds = 0;
        this.securityQuestionsDisplayMinutes = 0;
        this.securityQuestionsDisplaySeconds = 0;
        if (this.securityQuestionsTimerSubscription) {
            this.securityQuestionsTimerSubscription.unsubscribe();
        }

        this.updateSecurityQuestionLists();
    }

    updateSecurityQuestionLists(): void {
        this.securityQuestionList1 = this.securityQuestionList
            .filter(x => x.id != this.securityQuestions.securityQuestion2Id
                && x.id != this.securityQuestions.securityQuestion3Id)
            .map(x => { return { value: x.id, label: x.question } });

        this.securityQuestionList2 = this.securityQuestionList
            .filter(x => x.id != this.securityQuestions.securityQuestion1Id
                && x.id != this.securityQuestions.securityQuestion3Id)
            .map(x => { return { value: x.id, label: x.question } });

        this.securityQuestionList3 = this.securityQuestionList
            .filter(x => x.id != this.securityQuestions.securityQuestion1Id
                && x.id != this.securityQuestions.securityQuestion2Id)
            .map(x => { return { value: x.id, label: x.question } });
    }

    saveSecurityQuestions(): void {
        if (!this.securityQuestionsFormValidation.isFormValid()) {
            return;
        }

        this.savingSecurityQuestions = true;
        this._profileService.updateSecurityQuestions(this.securityQuestions)
            .pipe(finalize(() => { this.savingSecurityQuestions = false; }))
            .subscribe(success => {
                if (success) {
                    if (this.redirectAfterSave) {
                        this._sessionService.setSecurityQuestionsFlag(false);
                        this._router.navigate(['/']);
                    } else {
                        this.resetSecurityQuestionsForm();
                        this.notify.info(this.l('SecurityQuestionsUpdated'));
                    }
                } else {
                    this.securityQuestions.hasTwoFactorCode = true;
                    this.securityQuestionsRemainingSeconds = this.twoFactorExpirySeconds;

                    const timerSource = timer(0, 1000);
                    this.securityQuestionsTimerSubscription = timerSource.subscribe(() => {
                        this.securityQuestionsRemainingSeconds = this.securityQuestionsRemainingSeconds - 1;

                        this.securityQuestionsDisplayMinutes = Math.floor(Math.round(this.securityQuestionsRemainingSeconds / 60 * 100) / 100);
                        this.securityQuestionsDisplaySeconds = this.securityQuestionsRemainingSeconds - this.securityQuestionsDisplayMinutes * 60;

                        if (this.securityQuestionsRemainingSeconds === 0) {
                            this.message.warn(this.l('TimeoutPleaseTryAgain')).then(() => {
                                this.securityQuestions.hasTwoFactorCode = false;
                                this.securityQuestionsTimerSubscription.unsubscribe();
                            });
                        }
                    });
                }
            });
    }
}
