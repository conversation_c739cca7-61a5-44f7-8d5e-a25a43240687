import { Component, OnInit, Injector, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ModalDirective } from 'ngx-bootstrap';
import { SelectItem } from 'primeng/api';
import {LookUp} from '@app/economicsubstance/EconomicSubstance';
import { AnyARecord } from 'dns';
import { CIGALookUpDto, CIGARelevantActivityDto } from '@shared/service-proxies/service-proxies';
import { AppConsts } from '@shared/AppConsts';

@Component({
    selector: 'app-ciga-detail-table',
    templateUrl: './ciga-detail-table.component.html'
})
export class CigaDetailTableComponent extends AppComponentBase implements OnInit
{
    @Input() cigalookup: CIGALookUpDto[];
    @Input() headingSeq: string;
    @Input() details: CIGARelevantActivityDto[];
    @Input() sequenceNo: string;
    @Input() readOnlyMode: boolean;
    @Input() showHideCondition: boolean= false;

    constructor(injector: Injector)
    {
        super(injector);

    }
  
    ngOnInit()
    {
      
    }

    updateSource(items: CIGALookUpDto[]): void {

        
        if (items != null) {

            items.forEach((item) => {
                let object1: CIGARelevantActivityDto = new CIGARelevantActivityDto();
                //first need to hande the new added
                object1.cigaActivity = item;
                if (this.details.findIndex(x => x.cigaActivity.activityOrder == object1.cigaActivity.activityOrder && !x.isDeleted) == -1)
                    this.details.push(object1);


            });
            // need to handle the remove one by marking it as deleted
            this.details.forEach((item) => {
                  if (items.findIndex(x => x.activityOrder == item.cigaActivity.activityOrder) == -1) {
                    item.isDeleted = true;
                }
            });

        }

        this.details.sort((a, b) => (a.cigaActivity.activityOrder > b.cigaActivity.activityOrder) ? 1 : -1);
        

    }

    getSelectedCIGAItems(): CIGALookUpDto[] {
        let selectedItems: CIGALookUpDto[];
        selectedItems = [];
        if (this.details) {
            this.details.forEach
                (x => { if (!x.isDeleted) selectedItems.push(x.cigaActivity) });
        }
        return selectedItems;
    }

    removesource(code: number)
    {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            'Are you sure you want to delete it?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.handleDelete(code);

                }
            }
        );
    }



    handleDelete(code: number)
    {
        let index = this.details.findIndex(x => x.cigaActivity.activityOrder == code && !x.isDeleted);
        this.details[index].isDeleted = true;
    }


  
   
}
