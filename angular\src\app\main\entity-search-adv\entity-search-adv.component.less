.ra-advancedsearch-container {
    padding: 5px;
    margin: 0px auto;
    width:100%;
    height: auto;
    max-height: calc(100vh-22em);
    max-width: 1200px;
    text-align: center;

    .ra-advancedsearch-title {
        font-weight: normal !important;
    }

    .ra-advancedsearch-basic-section {
        text-align: left;
        max-width: 573px;
    }

    .ra-advancedsearch-submit {
        margin-top: 15px;
    }

    .ra-advancedsearch-submit-section {
        clear: both;
    }
}

.ra-advancedsearch-basic-section {
    margin: 0px auto;
}

.ra-advancedsearch-searchcriteria-section {
    clear: both;
    padding-top: 15px;

    .ra-advancedsearch-searchcriteria-grid-section {
        padding-top: 15px
    }

    .ra-advancedsearch-searchcriteria-header-row {
        background-color: #cccccc;
        font-weight: bold;
        padding-top: 0.1em !important;
        padding-top: 0.1em !important;
    }

    .ra-advancedsearch-searchcriteria-body {
        max-height: calc(100vh - 40em);
        overflow-y: auto;

        .startgroup {
            border-style: solid none none solid;
            border-color: #000000;
            border-width: thin;
            width: 5px;
            height: 30px;
            margin-left: 10px;
        }

        .group {
            border-style: none none none solid;
            border-color: #000000;
            border-width: thin;
            width: 5px;
            height: 30px;
            margin-left: 10px;
        }

        .endgroup {
            border-style: none none solid solid;
            border-color: #000000;
            border-width: thin;
            width: 5px;
            height: 30px;
            margin-left: 10px;
        }

        .ui-g-1, .ui-g-2, .ui-g-3, .ui-g-4, .ui-g-5, .ui-g-6, .ui-g-7, .ui-g-8, .ui-g-9, .ui-g-10, .ui-g-11, .ui-g-12 {
            padding-top: 0.01em !important;
            padding-left: 0.01em !important;
            padding-right: 0.01em !important;
            padding-bottom: 0.01em !important;
        }

        .hasGroup {
            .ui-g-1, .ui-g-2, .ui-g-3, .ui-g-4, .ui-g-5, .ui-g-6, .ui-g-7, .ui-g-8, .ui-g-9, .ui-g-10, .ui-g-11, .ui-g-12 {
                padding-top: 0.001em !important;
                padding-bottom: 0.001em !important;
            }
        }
        .ra-advancedsearch-searchcriteria-checkbox-section {
            padding-top: 5px;
        }

        .hasGroup .ra-advancedsearch-searchcriteria-checkbox-section {
            padding-top: 10px;
        }

        .ra-advancedsearch-searchcriteria-cell {
            padding: 0.1em !important;
        }
    }

    .ra-advancedsearch-searchcriteria-deleterow {
        color: red !important;
        font-size: 18px;
        cursor: pointer;
        vertical-align: middle;
    }
    
    .ra-advancedsearch-group {
        width: 90%;
        padding: 5px;
    }
    .ui-calendar {
        width: 100%;
    }
}

.multibuttons button {
    margin-left: 10px;
}
.bdo-errormessage {
    color: red;
}