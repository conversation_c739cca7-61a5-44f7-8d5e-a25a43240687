import { Component, OnInit, EventEmitter, Output, Injector, ViewChild, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AddressDto, CorporateEntityDto, CountryDto } from '@shared/service-proxies/service-proxies';
import { FormGroup } from '@angular/forms';
import * as uuid from 'uuid';
import { ModalDirective } from 'ngx-bootstrap';
import * as _ from 'lodash';
import { AppConsts } from '@shared/AppConsts';

import { SharedComponent } from '@app/economicsubstance/sharedfunctions';

@Component({
  selector: 'app-address-detail-table',
  templateUrl: './address-detail-table.component.html',
  styleUrls: ['./address-detail-table.component.css']
})
export class AddressDetailTableComponent extends AppComponentBase implements OnInit {


    @Input() public details: AddressDto[];
    @Input() headingSeq: string;
    @Input() country: CountryDto;
    @Input() sequenceNo: string;
    @Input() readOnlyMode: boolean;
    @Input() public corporateEntity: CorporateEntityDto;

    constructor(injector: Injector, private _sharedComponet: SharedComponent) {
        super(injector);
    }

    ngOnInit()
    {
        
    }
    removesource(id: any): void
    {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            'Are you sure you want to delete it?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.handleDelete(id);
                }
            }
        );
    }

    handleDelete(id: any) {
        let index = this.details.findIndex(x => x.id == id);
        this.details[index].isDeleted = index != -1 ? true : this.details[index].isDeleted;
    }

    //update table
    updateSource(sp: any) {
        let detail = _.cloneDeep(sp);
        let index = (this.details) ? this.details.findIndex(x => x.id == sp.id) : -1;
        if (index == -1)  this.details.push(detail);
            else this.details[index] = detail;
    }


    formatAddress(address: any): string {
        return this._sharedComponet.formatAddress(address);
    }


}
