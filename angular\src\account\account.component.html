<div class="ess-ac-main-container" *ngIf="!isMobileDevice">
    <div class="ess-ac-header">
        <img class="ess-ac-logo" src="../assets/common/images/logo.png">
        <div class="ess-ac-name">{{ 'ESSTitle' | localize }}</div>
        <div class="ess-ac-version">{{ ("Build" | localize) + ": " + appSession.application.serverBuild }}</div>
    </div>
    <div class="ess-ac-body">
        <router-outlet></router-outlet>
    </div>
    <div class="ess-ac-footer">{{ ("Note" | localize)}}</div>
</div>
<bdo-loader></bdo-loader>

<div class="ac-main-container not-compatible-message" *ngIf="isMobileDevice">
    <h3>{{deviceNotCompatibleMessage}}</h3>
</div>
