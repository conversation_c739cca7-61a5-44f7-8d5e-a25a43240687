import { Component, OnInit, Injector, Input } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, NgForm } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { SelectItem } from 'primeng/api';
import { EntitySearchInput } from './entity-search-input';
import { NullTemplateVisitor } from '@angular/compiler';

@Component({
  selector: 'app-entity-search',
  templateUrl: './entity-search.component.html',
  styleUrls: ['./entity-search.component.less'],
  viewProviders: [{ provide: ControlContainer, useExisting: NgForm }]
})
export class EntitySearchComponent extends AppComponentBase implements OnInit {

  @Input() entitySearchInput: EntitySearchInput = new EntitySearchInput()

  @Input() hidden: boolean = false;
  statuses: SelectItem[];

  constructor(injector: Injector) {
    super(injector)
  }

  ngOnInit() {
    this.statuses = [
      { label: "Any", value: false },
      { label: "All requiring Economic Substance Declaration", value: true }
    ];

    this.entitySearchInput.companyNumber = null
    this.entitySearchInput.entityName = null
    this.entitySearchInput.entityNumber = null
    this.entitySearchInput.requireDeclaration = false
  }

}
