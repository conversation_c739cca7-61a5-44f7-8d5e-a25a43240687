<div class="informationExchangeAddContainer">
    <p-card class="p-card-properties_form">
        <p-header class="row-flex-space">
            <p>
                {{ l("Spontaneous Information Exchange for: ")
                }}<b>{{ informationEchange.entityName }}</b>
            </p>

            <div>
                <button
                    id="save"
                    *ngIf="action === '1'"
                    pButton
                    type="button"
                    icon="pi pi-save"
                    class="ui-button-rounded margin-left"
                    iconPos="left"
                    (click)="onSave()"
                    tooltip="Save Draft"
                ></button>

                <button
                    id="close"
                    pButton
                    type="button"
                    class="
                        ui-button-rounded ui-button-secondary
                        margin-left
                        bigger-icons
                    "
                    icon="pi pi-times"
                    tooltip="Close"
                    (click)="onExit()"
                ></button>
            </div>
        </p-header>

        <form #addExchangeForm="ngForm">
            <p-card class="p-card-properties_form">
                <p-card
                    class="p-card-error"
                    *ngIf="errors && errors.length > 0"
                >
                    <div *ngFor="let error of errors">
                        {{ error }}
                    </div>
                </p-card>

                <div class="row-flex-justified">
                    <div class="col-flex">
                        <div class="row-flex-justified-largefont">
                            <label>{{ l("IEExcnageReason") }} </label>
                        </div>

                        <div class="row-flex-justified">
                            <label class="p-readonly-label margin-left"
                                >{{
                                    informationEchange.exchangeReason
                                        | exchangereason
                                }}
                            </label>
                        </div>
                    </div>

                    <div class="col-flex">
                        <div class="row-flex-justified-largefont">
                            <label>{{ "Current Status" }} </label>
                        </div>

                        <div class="row-flex-justified">
                            <label class="p-readonly-label margin-left"
                                >{{
                                    informationEchange.informationExchangeStatus
                                        | informationeschangestatus
                                }}
                            </label>
                        </div>
                    </div>

                    <div class="col-flex">
                        <div *ngIf="!disableDropDown">
                            <div class="row-flex-justified">
                                <label>{{ "Update Status" }} </label>
                            </div>
                            <div class="row-justified">
                                <p-dropdown
                                    id="status"
                                    [options]="informationStatuses"
                                    name="informationStatuses"
                                    [(ngModel)]="selectedInformationStatus"
                                    (onChange)="changeReadOnly()"
                                    optionLabel="description"
                                    required
                                ></p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>
            </p-card>
            <p-card class="p-card-properties_form">
                <div class="row-flex-justified-largefont">
                    <label
                        >{{ l("IEFiscalStartDate") }}
                        <span class="required">*</span>
                    </label>
                </div>
                <div class="row-flex-justified">
                    <label class="p-readonly-label margin-left">
                        {{
                            informationEchange.informationExchangeDetail.fiscalStartDate | date: 'dd/MM/yyyy':'UTC'
                        }}
                    </label>
                </div>

                <div class="row-flex-justified-largefont">
                    <label
                        >
                        {{ l("IEFiscalEndDate") }}
                        <span class="required">*</span>
                    </label>
                </div>
                <div class="row-flex-justified">
                    <label class="p-readonly-label margin-left"
                        >
                        {{
                            informationEchange.informationExchangeDetail.fiscalEndDate | date: 'dd/MM/yyyy':'UTC'
                        }}
                       
                    </label>
                </div>

                <div class="row-flex-justified-largefont">
                    <label
                        >{{ l("IELegalName") }} <span class="required">*</span>
                    </label>
                </div>
                <div class="row-flex-justified">
                    <label class="p-readonly-label margin-left"
                        >{{
                            informationEchange.informationExchangeDetail
                                .entityName
                        }}
                    </label>
                </div>

                <div class="row-flex-justified-largefont">
                    <label
                        >{{ l("IERegisteredOffice") }}
                        <span class="required">*</span>
                    </label>
                </div>
                <div class="row-flex-justified">
                    <label class="p-readonly-label margin-left"
                        >{{
                            informationEchange.informationExchangeDetail
                                .registeredAddressString
                        }}
                    </label>
                </div>

                <div class="row-flex-justified-largefont width-60">
                    <div class="col-flex width-30">
                        <label>{{ l("IETaxIdentification") }} </label>

                        <input
                            pInputText
                            type="text"
                            class="form-control"
                            [(ngModel)]="
                                informationEchange.informationExchangeDetail
                                    .taxIdentificationNo
                            "
                            [disabled]="readonly"
                            id="taxIdentificationid"
                            name="taxIdentificationname"
                        />
                    </div>

                    <div
                        *ngIf="
                            informationEchange.informationExchangeDetail
                                .taxIdentificationNo
                        "
                        class="col-flex width-30"
                    >
                        <label
                            >{{ "Issued Country" }}
                            <span class="required">*</span></label
                        >
                        <div
                            [ngClass]="{
                                'input-error-box-thin': hasError('IssuedBy')
                            }"
                        >
                            <p-dropdown
                                [options]="countries"
                                id="IssuedByTaxIden"
                                name="Issued By"
                                optionLabel="countryName"
                                [disabled]="readonly"
                                [(ngModel)]="
                                    informationEchange.informationExchangeDetail
                                        .taxIdentificationCountry
                                "
                            ></p-dropdown>
                        </div>
                    </div>
                    <div class="col-flex width-30"></div>
                </div>

                <div class="row-flex-justified-largefont width-60">
                    <div class="col-flex width-30">
                        <label>{{ l("IEOtherIdentification") }} </label>
                        <input
                            pInputText
                            type="text"
                            class="form-control"
                            [(ngModel)]="
                                informationEchange.informationExchangeDetail
                                    .otherIdentificationNo
                            "
                            [disabled]="readonly"
                            id="otherIdentificationid"
                            name="otherIdentificationid"
                        />
                    </div>

                    <div
                        class="col-flex width-30"
                        *ngIf="
                            informationEchange.informationExchangeDetail
                                .otherIdentificationNo
                        "
                    >
                        <label>{{ "Issued Country" }}</label>
                        <div>
                            <p-dropdown
                                [options]="countries"
                                id="selectedotherIdentification"
                                name="selectedotherIdentification"
                                optionLabel="countryName"
                                [disabled]="readonly"
                                [(ngModel)]="
                                    informationEchange.informationExchangeDetail
                                        .otherIdentificationCountry
                                "
                            ></p-dropdown>
                        </div>
                    </div>
                    <div
                        class="col-flex width-30"
                        *ngIf="
                            informationEchange.informationExchangeDetail
                                .otherIdentificationNo
                        "
                    >
                        <label>{{ "IN Type" }} </label>
                        <input
                            pInputText
                            type="text"
                            class="form-control"
                            [disabled]="readonly"
                            [(ngModel)]="
                                informationEchange.informationExchangeDetail
                                    .otherIdentificationTIN
                            "
                            id="otherIdentificationTIN"
                            name="otherIdentificationTIN"
                        />
                    </div>
                </div>

                <div class="row-flex-justified-largefont" *ngIf="this.informationEchange.exchangeReason != 2">
                    <label
                        >{{ l("IEGrossTotal") }}
                        <span
                            class="required"
                            *ngIf="this.informationEchange.exchangeReason != 2"
                            >*</span
                        >
                    </label>
                </div>
                <div class="row-flex-justified width-12" *ngIf="this.informationEchange.exchangeReason != 2">
                    <label
                        class="margin-top-5"
                        *ngIf="
                            informationEchange.informationExchangeDetail
                                .currency
                        "
                    >
                        {{
                            informationEchange.informationExchangeDetail
                                .currency.currencyCode
                        }}</label
                    >
                    <div
                        [ngClass]="{
                            'input-error-box-thin': hasError('GrossTotal')
                        }"
                    >
                        <p-spinner
                            [(ngModel)]="
                                informationEchange.informationExchangeDetail
                                    .totalAnnualIncome
                            "
                            [disabled]="readonly"
                            (keypress)="maskDecimal($event, 15, 2)"
                            [min]="0"
                            [max]="999999999999999.99"
                            [step]="0.25"
                            [formatInput]="true"
                            thosandSeparator=","
                            decimalSeparator="."
                            id="totalannualid"
                            name="TotalGrosIncome"
                        ></p-spinner>
                    </div>
                </div>

                <div class="row-flex-justified-largefont" *ngIf="this.informationEchange.exchangeReason != 2">
                    <label>{{ l("IENetBook") }} </label>
                </div>
                <div class="row-flex-justified width-12" *ngIf="this.informationEchange.exchangeReason != 2">
                    <label
                        class="margin-top-5"
                        *ngIf="
                            informationEchange.informationExchangeDetail
                                .currency
                        "
                    >
                        {{
                            informationEchange.informationExchangeDetail
                                .currency.currencyCode
                        }}</label
                    >
                    <p-spinner
                        [(ngModel)]="
                            informationEchange.informationExchangeDetail
                                .netBookValue
                        "
                        [disabled]="readonly"
                        (keypress)="maskDecimal($event, 15, 2)"
                        [min]="0"
                        [max]="999999999999999.99"
                        [step]="0.25"
                        [formatInput]="true"
                        thosandSeparator=","
                        decimalSeparator="."
                        id="totalannualid"
                        name="totalannualname"
                    ></p-spinner>
                </div>

                <div class="row-flex-justified-largefont">
                    <label>{{ l("IEBusinessPerm") }} </label>
                </div>
                <div class="row-flex-justified margin-left">
                    <div
                        *ngIf="
                            informationEchange.informationExchangeDetail &&
                            informationEchange.informationExchangeDetail
                                .bussinesPrimisess &&
                            informationEchange.informationExchangeDetail
                                .bussinesPrimisess.length > 0
                        "
                    >
                        <li
                            *ngFor="
                                let item of informationEchange
                                    .informationExchangeDetail.bussinesPrimisess
                            "
                        >
                            {{ item }}
                        </li>
                    </div>
                </div>
                
                <div class="row-flex-justified-largefont width-60" *ngIf="showDeclarationChanges_2_0()">                    
                        <div class="col-flex width-60">
                        <label>{{ l("IEMNEName") }}</label> 
                        <label class="p-readonly-label margin-left word-wrapping">{{ informationEchange.informationExchangeDetail.mneGroupName }}
                        </label>                       
                    </div>
                </div>

            </p-card>

            <p-card class="p-card-properties_form">
                <div
                    [ngClass]="{
                        'input-error-box-thin': hasError('recipient')
                    }"
                >
                    <div class="row-flex-justified-largefont">
                        <label>{{ l("IERecioientDetail") }} </label>
                        <div *ngIf="!readonly">
                            <a id="addRow" href="javascript:;" (click)="model.show()"
                                >Add row</a
                            >
                        </div>
                    </div>

                    <p-table
                        [paginator]="false"
                        [lazy]="true"
                        scrollable="true"
                        scrollHeight="400px"
                        [value]="
                            informationEchange.informationExchangeDetail
                                .recipientDetails
                        "
                    >
                        <ng-template pTemplate="header">
                            <tr>
                                <th class="table-text-header">
                                    {{ "Person Name" }}
                                </th>
                                <th class="table-text-header">
                                    {{ l("IERecDetJuris") }}
                                </th>
                                <th class="table-text-header">
                                    {{ l("IERecDetAddress") }}
                                </th>
                                <th class="table-text-header">
                                    {{ l("IERecDetTin") }}
                                </th>
                                <th class="table-text-header">
                                    {{ l("IERecDetOtherIden") }}
                                </th>
                                <th class="table-text-header">
                                    {{ l("IERecDetType") }}
                                </th>
                                <th class="table-text-header">
                                    {{ "UBO Type" }}
                                </th>
                                <th
                                    class="table-text-header"
                                    style="width: 10%"
                                >
                                    Action
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-source>
                            <tr>
                                <td>
                                    <label>
                                        {{ source.nameEntityPerson }}</label
                                    >
                                </td>
                                <td>
                                    <label>
                                        {{
                                            source.jurisdictionResidence
                                                .countryName
                                        }}</label
                                    >
                                </td>

                                <td>
                                    <label class="word-wrapping">
                                        {{
                                            formatAddress(source.address)
                                        }}</label
                                    >
                                </td>
                                <td>
                                    <label *ngIf="source.tin">
                                        {{ source.tin }} -
                                        {{
                                            source.tinIssuedCoutry.countryName
                                        }}</label
                                    >
                                </td>
                                <td>
                                    <label class="word-wrapping">
                                        {{ source.otherIdentification }}
                                    </label>
                                </td>
                                <td>
                                    <label>
                                        {{
                                            entityName(
                                                source.typeOfEntityPerson
                                            )
                                        }}</label
                                    >
                                </td>
                                <td>
                                    <label> {{ boName(source.uboType) }}</label>
                                </td>
                                <td style="width: 10%">
                                    <div *ngIf="!readonly">
                                        <button
                                            id="removeDetail"
                                            pButton
                                            type="button"
                                            icon="pi pi-trash"
                                            iconPos="center"
                                            (click)="removeDetail(source)"
                                        ></button>
                                        <button
                                            id="showModel"
                                            pButton
                                            type="button"
                                            icon="pi pi-pencil"
                                            iconPos="center"
                                            class="margin-left"
                                            (click)="model.show(source)"
                                        ></button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </p-card>

            <div
                *ngIf="
                    informationEchange.informationExchangeDetail
                        .relevantActivityInformationDetail
                "
            >
                <div
                    *ngFor="
                        let item of informationEchange.informationExchangeDetail
                            .relevantActivityInformationDetail;
                        index as i
                    "
                >
                    <p-card class="p-card-properties_form">
                        <div class="row-flex-justified-largefont">
                            <label
                                >5{{ sub_headings[i] }}.
                                {{ l("IEActivityDetais") }}
                                {{ item.relevantActivityName }}
                            </label>
                        </div>

                        <div class="row-flex-justified-largefont">
                            <label
                                >5{{ sub_headings[i] }}.1 {{ l("IEDetail1") }}
                            </label>
                        </div>
                        <div class="row-flex-justified">
                            <label
                                class="p-readonly-label margin-left"
                                [ngClass]="{
                                    'input-error-box-thin': hasErrorField(
                                        'turn-',
                                        i
                                    )
                                }"
                            >
                                {{
                                    informationEchange.informationExchangeDetail
                                        .currency.currencyCode
                                }}
                                <p-spinner
                                    [(ngModel)]="item.totalTurnover"
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="{{ i }}"
                                    name="turn-{{ i }}"
                                ></p-spinner>
                            </label>
                        </div>

                        <div class="row-flex-justified-largefont">
                            <label
                                >5{{ sub_headings[i] }}.2 {{ l("IEDetail2") }}
                            </label>
                        </div>
                        <div class="row-flex-justified">
                            <label
                                class="p-readonly-label margin-left"
                                [ngClass]="{
                                    'input-error-box-thin': hasErrorField(
                                        'expd-',
                                        i
                                    )
                                }"
                            >
                                {{
                                    informationEchange.informationExchangeDetail
                                        .currency.currencyCode
                                }}
                                <p-spinner
                                    [(ngModel)]="item.totalExpeditureInBVI"
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="{{ i }}"
                                    name="expd-{{ i }}"
                                ></p-spinner>
                            </label>
                        </div>

                        <div class="row-flex-justified-largefont">
                            <label
                                >5{{ sub_headings[i] }}.3 {{ l("IEDetail3") }}
                            </label>
                        </div>
                        <div class="row-flex-justified">
                            <label
                                class="p-readonly-label margin-left"
                                [ngClass]="{
                                    'input-error-box-thin': hasErrorField(
                                        'expbvi-',
                                        i
                                    )
                                }"
                            >
                                {{
                                    informationEchange.informationExchangeDetail
                                        .currency.currencyCode
                                }}
                                <p-spinner
                                    [(ngModel)]="
                                        item.totalExpenditureIncurredInBVI
                                    "
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="{{ i }}"
                                    name="expbvi-{{ i }}"
                                ></p-spinner>
                            </label>
                        </div>

                        <div class="row-flex-justified-largefont">
                            <label
                                >5{{ sub_headings[i] }}.4 {{ l("IEDetail4") }}
                            </label>
                        </div>
                        <div class="row-flex-justified">
                            <label class="p-readonly-label margin-left"
                                >{{ item.totalFullTimeEmployeeInBVI }}
                            </label>
                        </div>

                        <div *ngIf="item.releventActivityValue === 8">
                            <div class="row-flex-justified-largefont">
                                <label
                                    >5{{ sub_headings[i] }}.5
                                    {{ l("IEDetail5") }}
                                </label>
                            </div>

                            <div
                                class="
                                    row-flex-justified-width
                                    width-12
                                    margin-left
                                "
                            >
                                <label
                                    class="margin-top-5"
                                    *ngIf="
                                        informationEchange
                                            .informationExchangeDetail.currency
                                    "
                                >
                                    {{
                                        informationEchange
                                            .informationExchangeDetail.currency
                                            .currencyCode
                                    }}</label
                                >
                                <p-spinner
                                    [(ngModel)]="item.grossIncomeRoyalities"
                                    [disabled]="readonly"
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="grossIncomeRoyalitiesn"
                                    name="Gross Annual"
                                ></p-spinner>
                            </div>

                            <div class="row-flex-justified-largefont">
                                <label
                                    >5{{ sub_headings[i] }}.6
                                    {{ l("IEDetail6") }}
                                </label>
                            </div>

                            <div
                                class="
                                    row-flex-justified-width
                                    width-12
                                    margin-left
                                "
                            >
                                <label
                                    class="margin-top-5"
                                    *ngIf="
                                        informationEchange
                                            .informationExchangeDetail.currency
                                    "
                                >
                                    {{
                                        informationEchange
                                            .informationExchangeDetail.currency
                                            .currencyCode
                                    }}</label
                                >
                                <p-spinner
                                    [(ngModel)]="item.grossIncomeGains"
                                    [disabled]="readonly"
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="grossIncomeGains"
                                    name="grossIncomeGains"
                                ></p-spinner>
                            </div>  
                            <div *ngIf="showDeclarationChanges_2_0()">
                            <div class="row-flex-justified-largefont" *ngIf="showDeclarationChanges_2_0()">
                                <label
                                    >5{{ sub_headings[i] }}.7
                                    {{ l("IEDetail7") }}
                                </label>
                            </div>

                            <div 
                                class="
                                    row-flex-justified-width
                                    width-12
                                    margin-left
                                "
                            >
                                <label
                                    class="margin-top-5"
                                    *ngIf="
                                        informationEchange
                                            .informationExchangeDetail.currency
                                    "
                                >
                                    {{
                                        informationEchange
                                            .informationExchangeDetail.currency
                                            .currencyCode
                                    }}</label
                                >
                                <p-spinner
                                    [(ngModel)]="item.grossIncomeOthers"
                                    [disabled]="readonly"
                                    (keypress)="maskDecimal($event, 15, 2)"
                                    [min]="0"
                                    [max]="999999999999999.99"
                                    [step]="0.25"
                                    [formatInput]="true"
                                    thosandSeparator=","
                                    decimalSeparator="."
                                    id="grossIncomeOthers"
                                    name="grossIncomeOthers"
                                ></p-spinner>
                            </div>
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>

            <p-card class="p-card-properties_form">
                <div class="row-flex-justified-largefont">
                    <label
                        >{{ "6. Summary" }}
                        <span
                            class="required"
                            *ngIf="this.informationEchange.exchangeReason == 0"
                            >*</span
                        >
                    </label>
                </div>

                <div
                    class="row-flex-justified"
                    [ngClass]="{ 'input-error-box-thin': hasError('Summary') }"
                >
                    <textarea
                        required
                        pInputTextarea
                        rows="10"
                        maxlength="1000"
                        [disabled]="readonly"
                        [(ngModel)]="
                            informationEchange.informationExchangeDetail.summary
                        "
                        id="summaryid"
                        name="Summary"
                    ></textarea>
                </div>
            </p-card>
        </form>
    </p-card>
    <recepientdialog
        #model        
        (submitted)="updateSource($event)"
    ></recepientdialog>
</div>
