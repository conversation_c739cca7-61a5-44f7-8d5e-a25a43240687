
<div *ngIf="displayHeader">
    <p-card class="p-card-properties_form">

        <div *ngIf="!displayFromCa">
            <p-header> This declaration has been successfully submitted by {{currentEconomicSubstance.displayName}} at {{returnDate(currentEconomicSubstance.submissionDate) | date: "dd/MM/yyyy HH:mm:ss"}}</p-header>


        </div>

        <p-header *ngIf="displayFromCa"> This declaration has been successfully submitted at {{returnDate(currentEconomicSubstance.submissionDate) | date: "dd/MM/yyyy HH:mm:ss"}}</p-header>
    </p-card>

    <div class="es-header es-header-spacing">
        <div class="es-header-item-small">
            <h5>{{l("ESCE1")}}</h5>
            <p class="word-wrapping">{{corporateEntity.name}}</p>
        </div>
        <div *ngIf="!displayFromCa" class="es-header-item-small">
            <h5>{{l("ESCE1b")}}</h5>
            <p class="word-wrapping">{{corporateEntity.clientNumber}}</p>
        </div>
        <div class="es-header-item-medsmall">
            <h5>{{l("ESCE2")}}</h5>
            <p>{{corporateEntity |entityIncoperationFormation}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE3")}}</h5>
            <p>{{corporateEntity.incorporationDate | date: "dd/MM/yyyy"}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE4")}}</h5>
            <p>{{ corporateEntity.entityType | entityDescription}}</p>
        </div>
        <div class="es-header-item-med">
            <h5>{{l("ESCE5")}}</h5>
            <div>
                {{corporateEntity.listedOnStockExchange?'Yes':'No'}}
            </div>

        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE5A")}}</h5>
            <p class="word-wrapping">  {{corporateEntity.stockExchange}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE6")}}</h5>
            <p class="word-wrapping">  {{corporateEntity.statusText}}</p>
        </div>
        <div class="es-header-item-small">
            <h5>{{l("ESCE6D")}}</h5>
            <p class="word-wrapping"> {{corporateEntity?.addressLine1 || '' }} {{ corporateEntity?.addressLine2 || '' }} {{ corporateEntity?.countryName || '' }}</p>
        </div>
    </div>


</div>

<app-step1-es [economicsubstance]="currentEconomicSubstance"
              [readOnlyMode]="true"
              [displayFromCa]="displayFromCa"> </app-step1-es>

 <app-es-entity-details  *ngIf="showHideCondition"
              [economicsubstance]="currentEconomicSubstance"  
              [esEntityDetail] = "esEntityDetail"                
              [readOnlyMode]="true"
              [importOnlyMode]="importOnlyMode"
              [displayFromCa] ="displayFromCa">
</app-es-entity-details> 

<app-step2-es [economicsubstance]="currentEconomicSubstance"
              [readOnlyMode]="true"
              [importOnlyMode]="importOnlyMode"
              [displayFromCa]="displayFromCa">
</app-step2-es>
<div *ngIf="CheckIfNonIsSelected()">
    <app-step3-es [economicsubstance]="currentEconomicSubstance"
                  [readOnlyMode]="true"
                  [importOnlyMode]="importOnlyMode"
                  [displayFromCa]="displayFromCa"
                  [historyDisplay]="historyDisplay"
                  [ctspId]="ctspId"></app-step3-es>
</div>
<app-step4-es [economicsubstance]="currentEconomicSubstance"
              [corporateEntity]="corporateEntity"
              [readOnlyMode]="true"
              [displayFromCa]="displayFromCa"
              [importOnlyMode]="importOnlyMode"
              [esassessment]="esassessment"
              [historyDisplay]="historyDisplay"
              [relevantActivityStatus]="relevantActivityStatus"
              [ctspId]="ctspId"></app-step4-es>

<app-step5-es [economicsubstance]="currentEconomicSubstance"
              [readOnlyMode]="true"
              [displayFromCa]="displayFromCa"
              [historyDisplay]="historyDisplay"
              [ctspId]="ctspId"> </app-step5-es>

<div *ngIf="currentEconomicSubstance.approveRejectProvisionalTreatment">
    <app-provisional-treatment-es [economicsubstance]="currentEconomicSubstance"
                                  [readOnlyMode]="true"
                                  [displayFromCa]="displayFromCa"
                                  [ctspId]="ctspId">
    </app-provisional-treatment-es>
</div>


<div *ngIf="currentEconomicSubstance.economicSubstanceInformationRequired && currentEconomicSubstance.economicSubstanceInformationRequired.length>0">
    <app-requested-information-es [informationRequiredsHistory]="informationRequiredsHistory"
                                  [informationRequired]="informationRequired"
                                  [readOnlyMode]="true"
                                  [displayFromCa]="displayFromCa"
                                  [ctspId]="ctspId">
    </app-requested-information-es>
</div>

<div *ngIf="esassessment&&  esassessment.esReviewStatus.code === 8">

    <app-action-enforced-es
                            [esassessment]="esassessment"
                            [readOnlyMode]="true"
                            [displayFromCa]="displayFromCa"
                            [ctspId]="ctspId">

    </app-action-enforced-es>
</div>

