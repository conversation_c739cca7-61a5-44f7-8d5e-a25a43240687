import { Location } from '@angular/common';
import { Component, Injector, HostListener } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SubmittedLocation, ESActionStatus } from '@app/economicsubstance/EconomicSubstance';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AppConsts } from '@shared/AppConsts';
import {
    EconomicSubstanceDeclarationDto, EsEntityDetailDto,
    CorporateEntityDto, EconomicSubstanceServiceProxy, LookUpMainServiceProxy,
    AuditEventServiceProxy, SingleFieldInputOfGuid, RelevantActivityDetailDto, FileImportAuditDto,
    FileUploadServiceProxy,  RelevantActivity,ValidationResultMainDto, UploadedDeclarationStatus, UploadedDeclarationDto,ValidationResultDto, ValidationType, EconomicSubstanceStatus
} from '@shared/service-proxies/service-proxies';
import { forkJoin } from 'rxjs';
import { PrintService } from '@shared/services/print.service';
import { EconomicSubstanceService, EsAction } from '@app/economicsubstance/economicsubstance.service';
import * as _ from 'lodash';
import { stagger } from '@angular/animations';


import * as moment from 'moment';

@Component({
  selector: 'app-es-import',
  templateUrl: './es-import.component.html',
  styleUrls: ['./es-import.component.css']
})
export class ESImportComponent extends AppComponentBase {

    id: any;
    readytoDisplay: boolean = false;
    parsedValue: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();
    isEdit: boolean = false;
    ctspId: any;
    disableIfAnyError: boolean = false;
    disableIfImported: boolean = false;


    importobject: UploadedDeclarationDto = new UploadedDeclarationDto();   

    currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    corporateEntity: CorporateEntityDto = new CorporateEntityDto();
    public esEntityDetail: EsEntityDetailDto  = new EsEntityDetailDto();
    public showHideEntityDetails: boolean;

    constructor(injector: Injector, private router: Router, private location: Location,
        private route: ActivatedRoute, private _fileUploadServiceProxy: FileUploadServiceProxy,
        public _economicSubstanceService: EconomicSubstanceService,
        private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy,
        private _lookUpMainServiceServiceProxy: LookUpMainServiceProxy,
        private _auditEventService: AuditEventServiceProxy,
        private _sharedComponet: SharedComponent, private _router: Router, public printService: PrintService

    ) {
        super(injector)
        this.route.params.subscribe(x => {

            this.id = x.id;

            this._economicSubstanceService.initializeEmptyError();


            this._fileUploadServiceProxy.getDeclarationDetailsById(this.id).subscribe(result =>
            {
                this.importobject = result;
                this.parsedValue = result.fileUploadValue.parsedValue;
                this.currentEconomicSubstance = this.parsedValue;
                this.currentEconomicSubstance.uploadedDeclarationId = this.id; 
                this.corporateEntity = this.currentEconomicSubstance.corporateEntity;
                this.showHideEntityDetails = moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day');  

                if(this.currentEconomicSubstance.esEntityDetails)
                    this.esEntityDetail = this.currentEconomicSubstance.esEntityDetails.find(e => typeof e !== 'undefined');

                if (this.importobject.uploadStatus !== UploadedDeclarationStatus.WithErrors
                    && this.importobject.uploadStatus !== UploadedDeclarationStatus.WithWarnings

                    && this.importobject.uploadStatus !== UploadedDeclarationStatus.Pending)
                {
                    
                    this.disableIfImported = true;
                }
                else {


                    this._economicSubstanceServiceProxy.allStepValidation(this.parsedValue).subscribe((data: ValidationResultMainDto[]) => {
                        if (data) {
                            data.forEach((validation: ValidationResultMainDto) => {
                                validation.validationResultDto.forEach((validation: ValidationResultDto) => {
                                    if (validation.validationType == ValidationType.Error) {
                                        this.disableIfAnyError = true;
                                        this._economicSubstanceService.errorList.push(validation.validationString);
                                    }
                                    else {
                                        this._economicSubstanceService.warningList.push(validation.validationString);

                                    }
                                });

                            });
                        }
                        this.readytoDisplay = true;

                    },
                        err => {
                            console.error(err);
                        });
                }
              
            }
            );
          
        });

    }

    ngOnInit()
    {
        this._economicSubstanceService.initializeEmptyError();               
    }

    onEdit() {

        this._economicSubstanceService.initializeEmptyError();
        this._router.navigate(['app/economicsubstance/editimport/' +this.id]);
    }

    onClose() { this.location.back(); }


    ClearOldData() {
        if (!this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {
            this.currentEconomicSubstance.jurisdictionTaxResident = null;
            this.currentEconomicSubstance.jurisdictionTaxResidentId = null;
            this.currentEconomicSubstance.doesEntityHaveParentEntity = null;
            this.currentEconomicSubstance.parentEntityName = null;
            this.currentEconomicSubstance.parentEntityAlternativeName = null;
            this.currentEconomicSubstance.parentJurisdiction = null;
            this.currentEconomicSubstance.parentJurisdictionId = null;
            this.currentEconomicSubstance.entityIncorporationNumber = null;
            this.currentEconomicSubstance.isEvidenceNonResidenceOrTreatment = null;
            this.currentEconomicSubstance.taxPayerIdentificationNumber = null;

            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments) {
                this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments.forEach(x => { x.isDeleted = true; })
            }
        }
        else {
            if (!this.currentEconomicSubstance.doesEntityHaveParentEntity) {
                this.currentEconomicSubstance.parentEntityName = null;
                this.currentEconomicSubstance.parentEntityAlternativeName = null;
                this.currentEconomicSubstance.parentJurisdiction = null;
                this.currentEconomicSubstance.parentJurisdictionId = null;
                this.currentEconomicSubstance.entityIncorporationNumber = null;
            }
        }
        let activities = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
        activities.forEach(x => {
            if (!x.isCarriedForPartFinancialPeriod) { x.startDateString = null; x.endDateString = null; x.startDate = null, x.endDate = null; }
            if (this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {

                x.isActivityDirectedInBVI = null;
                x.noofConductedMeetingString = null;
                x.noofMeetingHeldInBVIString = null;

                x.isMeetingMinutesInBVI = null;
                x.totalTurnoverString = null;

                x.totalExpeditureString = null;
                x.totalExpeditureInBVIString = null;
                x.totalNoFullTimeEmployeeString = null;
                x.totalFullTimeEmployeeInBVIString = null;

                x.cigaOtherDetail = null;
                x.hasAnyIncomeBeenOutsourced = null;
                x.wasCIGAOutsourcedInBVI = null;
                x.totalExpenditureIncurredInBVI = null;


                x.doesEntityComplyItsStatutoryObligations = null;
                x.doesEntityManageEquity = null;
                x.doesEntityHaveAdequateEmployee = null;

                // intelect
                x.isLegalEntityHighRisk = null;
                x.doesEntityProvideEvidence = null;
                x.totalExpenditureIncurredInBVIString = null;
                x.grossIncomeRoyalitiesString = null;

                x.totalGrossAnnualIncomeString = null;
                x.grossIncomeGainsString = null;
                x.doesLegalEntityConductCIGA = null;
                x.doesEntityProvideEvidenceroRebut = null;
                x.doesBusinessRequireEquipment = null;
                x.equipmentInJurisdiction = null;
                x.equipmentDescription = null;

                if (x.conductedCIGAActivity) x.conductedCIGAActivity.forEach(x => x.isDeleted = true);
                if (x.premisesAddress) x.premisesAddress.forEach(x => x.isDeleted = true);
                if (x.serviceProviders) x.serviceProviders.forEach(x => x.isDeleted = true);
                if (x.managementDetails) x.managementDetails.forEach(x => x.isDeleted = true);
                if (x.attendMeetingDetails) x.attendMeetingDetails.forEach(x => x.isDeleted = true);
                if (x.employeeQualificationDetails) x.managementDetails.forEach(x => x.isDeleted = true);
                if (x.otherCIGADocuments) x.otherCIGADocuments.forEach(x => x.isDeleted = true);
                if (x.highRiskDocuments) x.highRiskDocuments.forEach(x => x.isDeleted = true);


                 //BVI Declaration Changes 2.0                 
                 x.grossIncomeType = null;
                 x.totalAssetsValue = null;
                 x.netAssetsValue = null;
                 x.noofQuorumBoardMeeting = null;
                 x.isQuorumBoardMeetingInBVI = null;
                 x.totalNoCorporateLegalEmployee = null;
                 x.tangibleAsset = null;
                 x.tangibleAssetIncome = null;
                 x.tangibleAssetEmployeeResponsibility = null;
                 x.historyofStrategicDecisionsInBVI = null;
                 x.historyofTradingActivityIncome = null;
                 x.relevantIPAsset = null;
                 x.ipAssetsInBVI = null;
                 x.ipAssetsEmployeeResponsibility = null;
                 x.concreteEvidenceDecisionInBVI = null;
                 x.grossIncomeOthersString = null;

                 if (x.tangibleAssetIncomeDocuments) x.tangibleAssetIncomeDocuments.forEach(x => x.isDeleted = true);
                 if (x.tangibleAssetEmployeeResponsibilityDocuments) x.tangibleAssetEmployeeResponsibilityDocuments.forEach(x => x.isDeleted = true);
                 if (x.historyofStrategicDecisionsInBVIDocuments) x.historyofStrategicDecisionsInBVIDocuments.forEach(x => x.isDeleted = true);
                 if (x.historyofTradingActivityIncomeDocuments) x.historyofTradingActivityIncomeDocuments.forEach(x => x.isDeleted = true);
                 if (x.ipAssetsInBVIDocuments) x.ipAssetsInBVIDocuments.forEach(x => x.isDeleted = true);
                 if (x.ipAssetsEmployeeResponsibilityDocuments) x.ipAssetsEmployeeResponsibilityDocuments.forEach(x => x.isDeleted = true); 
                 if (x.concreteEvidenceDecisionInBVIDocuments) x.concreteEvidenceDecisionInBVIDocuments.forEach(x => x.isDeleted = true);
            }

        });

        //BVI Declaration Changes 2.0
        this.currentEconomicSubstance.esEntityDetails = [];
    }

    getIncoperationFormatationNumber(formatationNumber: any, companyNumber: any):string
    {
        if (formatationNumber) return formatationNumber;
        if (companyNumber) return companyNumber;
        return "";
    }

    saveESDraft(status: EconomicSubstanceStatus) {

        this.currentEconomicSubstance.status = status;
        this.currentEconomicSubstance.localSubmissionDate = moment();
        this.currentEconomicSubstance.submissionDate = moment.utc();
        this.ClearOldData();
        this.currentEconomicSubstance.esEntityDetails.push(this.esEntityDetail);
       

        
        let fileImportAudit = new FileImportAuditDto();

        fileImportAudit.corporateEntityId = this.currentEconomicSubstance.corporateEntityId;
        fileImportAudit.entityName = this.currentEconomicSubstance.corporateEntity.name;
        fileImportAudit.entityUniqueID = this.currentEconomicSubstance.corporateEntity.clientNumber;
        fileImportAudit.incorFormationNumber = this.getIncoperationFormatationNumber(this.currentEconomicSubstance.corporateEntity.formationNumber, this.currentEconomicSubstance.corporateEntity.companyNumber);
        fileImportAudit.fiscalStartDate = this.currentEconomicSubstance.fiscalStartDateString;
        fileImportAudit.fiscalEndDate = this.currentEconomicSubstance.fiscalEndDateString;
        fileImportAudit.fileName = this.importobject.fileName;
        if (status === EconomicSubstanceStatus.Draft)
            this._auditEventService.auditImportDraftEntityViewEvent(fileImportAudit).subscribe();
        if (status === EconomicSubstanceStatus.Submitted)
            this._auditEventService.auditImportSubmitEntityViewEvent(fileImportAudit).subscribe();

        this._economicSubstanceServiceProxy.importEconomicSubstance(this.currentEconomicSubstance).subscribe((data: any) => {
            if (data) {

                this.importobject.fiscalEndDateString = data.fiscalEndDateString;
                this.importobject.fiscalStartDateString = data.fiscalStartDateString;
                this.importobject.uploadStatus = UploadedDeclarationStatus.Imported;
                this.importobject.fileUploadValue = null;
                this._fileUploadServiceProxy.updateUploadDeclarationStatus(this.importobject).subscribe((dataAny: any) => { });
          
                if (status === EconomicSubstanceStatus.Submitted)
                    this.router.navigate(['app/economicsubstance/display/' + data.id + '/' + SubmittedLocation.FromImport + '/' + this.importobject.uploadedFileId])
                else {

                    this.router.navigate(['app/economicsubstance/edit/' + data.id + '/' + this.importobject.uploadedFileId + '/' + ESActionStatus.Import])
                }
            }
        });
    }

    onSaveDraft() {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDarftConfirmation,
            'Are you sure?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.saveESDraft(EconomicSubstanceStatus.Draft);
                }
            }
        );
    }

    onSubmitForReview()
    {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsSubmitConfirmation,
            'Are you sure?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.saveESDraft(EconomicSubstanceStatus.Submitted);
                }
            }
        );

    }


    onDelete()
    {
        let self = this;
        let items = [];
        items.push(this.importobject);
        if (items) {
            abp.message.confirm(
                AppConsts.messageList.ESRemoveImportedDeclaration,
                'Are you sure?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        self._fileUploadServiceProxy.selectedImportedItemsToRemove(items)
                            .subscribe(() => {
                                self.router.navigate(['app/main/filetriage/' + self.importobject.uploadedFileId]);

                            });
                    }
                }
            );
        }
    }

   

    onPrint() {
        this.printService.print();
    }

    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'display': 'none',
            };
        }

        return {};
    }


    CheckIfNonIsSelected(): Boolean {

        let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);

        if (object.length > 0) return false;

        return true;
    }

    
}

