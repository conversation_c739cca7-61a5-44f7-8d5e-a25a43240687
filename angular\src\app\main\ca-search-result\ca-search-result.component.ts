import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AuditEntity, AuditEventServiceProxy, CaCorporateEntityAuditDto, CASearchServiceServiceProxy, CtspServiceProxy } from '@shared/service-proxies/service-proxies';
import { EntityChangeHistoryModalComponent } from 'app/shared/common/audit/entity-change-history-modal.component';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { CaSearchResultViewModel } from '../viewmodel/ca-search-result-viewmodel';


@Component({
  selector: 'app-ca-search-result',
  templateUrl: './ca-search-result.component.html',
  styleUrls: ['./ca-search-result.component.less']
})
export class CaSearchResultComponent extends AppComponentBase implements OnInit {
  @ViewChild('entityChangeHistoryModal', { static: true }) entityChangeHistoryModal: EntityChangeHistoryModalComponent;

  viewModelObservable: Observable<CaSearchResultViewModel>

  selectedId: string = null;
  selectedCtspNumber: string = null;

  constructor(private router: Router,
    private route: ActivatedRoute,
    searchService: CASearchServiceServiceProxy,
      ctspService:CtspServiceProxy,
    injector: Injector,
      private _auditEventService: AuditEventServiceProxy)
  {
    super(injector);
    
    this.viewModelObservable = route.queryParams.pipe(
      map(x => {
        const url = window.location.href;
        const urlParams = new URLSearchParams(url.split('?')[1]);
        let ctspIds = urlParams.getAll('ctspIds');
        let item = new CaSearchResultViewModel(searchService, ctspService, x, s => this.l(s),ctspIds)
        return item;
      }),
      shareReplay(1),
    );
    
    this.viewModelObservable.subscribe(x => x.resultItems.selectedEntityObservable.subscribe(y => {
      this.selectedId = y.id;
      this.selectedCtspNumber = y.ctspNumber;

      let auditInput = new CaCorporateEntityAuditDto();
      auditInput.ctspNumber = this.selectedCtspNumber;
      auditInput.id = this.selectedId
      this._auditEventService.auditCaCorporateEntityViewEvent(auditInput).subscribe();
    }));
  }

  colorCodeList: any[] = [];

  ngOnInit() {
  }

  setColorCodes(list) {
    this.colorCodeList = list;
  }

  newSearch() {
    this.router.navigate(['../casearch'], { relativeTo: this.route })
  }

  changeHistory(): void {
    this.entityChangeHistoryModal.show(AuditEntity.CorporateEntity, this.selectedId, this.selectedCtspNumber);
  }
}
