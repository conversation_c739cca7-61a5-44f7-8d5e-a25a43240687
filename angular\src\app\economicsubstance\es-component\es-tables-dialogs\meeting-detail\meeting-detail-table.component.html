<div class="row-flex-justified-largefont">
    <div>9{{headingSeq}}.{{sequenceNo}}e. {{l('DirectionManagementE')}}<span *ngIf="showHideCondition" class="required">*</span></div>
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)="model.show(null,true,importOnlyMode)">Add row</a>
    </div>
</div>
<p-table [paginator]="false" [lazy]="true" scrollable="true" scrollHeight="400px"
         [value]="details">
    <ng-template pTemplate="header">
        <tr>
            <th style="width:15%" class="table-text-header">Meeting # </th>
            <th style="width:15%" class="table-text-header">Name</th>
            <th style="width:20%" class="table-text-header">Physically Present?</th>
            <th style="width:20%" class="table-text-header">Relation to the entity</th>
            <th style="width:20%" class="table-text-header">Qualification</th>
            <th *ngIf="!readOnlyMode" style="width:10%" class="table-text-header">Action</th>

        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-source>
        <tr *ngIf="!source.isDeleted">
         <td style="width:15%" *ngIf="!importOnlyMode">
            <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.meetingNo}}</label>
        </td>

        
        <td style="width:15%" *ngIf="importOnlyMode">
            <label [ngClass]="{'p-readonly-label':readOnlyMode}"> {{source.meetingNoString}}</label>
        </td>
        
            <td style="width:15%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.name}}</label>
            </td>
            <td style="width:20%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}">  {{returnyesno(source.isPhysicallyPresent)}} </label>
            </td>
            <td style="width:20%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.relationToEntity}} </label>
            </td>
            <td style="width:20%">
                <label [ngClass]="{'p-readonly-label':readOnlyMode}" class="word-wrapping">  {{source.qualification}} </label>
            </td>

            <td style="width:10%" *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="center"
                        (click)="removesource(source.id)"></button>
                <button pButton type="button" icon="pi pi-pencil" iconPos="center" class="margin-left"
                        (click)="model.show(source,false,importOnlyMode)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>

<app-meeting-detail-dialog #model (submitted)="updateSource($event)"></app-meeting-detail-dialog>
