<div [@routerTransition]>
    <div class="ess-ac-card">
        <div class="ess-ac-card-header">LOGIN</div>

        <div class="ess-ac-card-body">
            <form #loginForm="ngForm" method="post" (ngSubmit)="login()">

                <div class="ess-ca-form-control col-flex">
                    <label>Username or Email *</label>
                    <input id="loginUsernameOrEmail" #userNameOrEmailAddressInput="ngModel" placeholder="Username or Email"
                           [(ngModel)]="loginService.authenticateModel.userNameOrEmailAddress" autoFocus type="text"
                           autocomplete="new-password" name="userNameOrEmailAddress" required
                           [ngClass]="{ 'ess-ca-error': (userNameOrEmailAddressInput.touched && userNameOrEmailAddressInput.invalid) }"/>
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label>Password *</label>
                    <input id="loginPassword" #passwordInput="ngModel" placeholder="Password"
                           [(ngModel)]="loginService.authenticateModel.password" type="password"
                           autocomplete="new-password" name="password" required 
                           [ngClass]="{ 'ess-ca-error': (passwordInput.touched && passwordInput.invalid) }"/>
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label>Send the Verification Code to *</label>
                    <p-dropdown id="loginVerificationCode" [options]="twoFactorProviders"
                                [(ngModel)]="loginService.authenticateModel.twoFactorProvider" name="twoFactorProvider">
                    </p-dropdown>
                </div>

                <div class="ess-ca-form-control">
                    <button id="loginSubmit" pButton [disabled]="!loginForm.form.valid" type="submit" label="Continue"
                            class="ui-button-rounded ui-button-warning"></button>
                </div>

                <div class="ess-ca-form-control col-flex">
                    <div>
                        <a id="loginForgotPassword" routerLink="/account/forgot-password" id="forget-password">Forgot Password</a>
                        &nbsp; | &nbsp;
                        <a id="loginHelp" (click)="loginHelp()">Login Help</a>
                    </div>
                    <bdo-needhelp></bdo-needhelp>
                </div>
            </form>
        </div>
    </div>
</div>
