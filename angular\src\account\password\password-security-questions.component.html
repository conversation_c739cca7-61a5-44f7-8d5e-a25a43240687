<div class="row-flex-center" [@routerTransition]>
    <div class="ess-ac-card">
        <div class="ess-ac-card-header">CHANGE PASSWORD</div>

        <div class="ess-ac-card-body">
            <form #securityQuestionsForm="ngForm" method="post" (ngSubmit)="verifySecurityQuestions()">

                <div class="ess-ca-form-control col-flex">
                    <label for="SecurityQuestion1">{{securityQuestion1.question}}</label>
                    <input #securityAnswer1="ngModel" placeholder="Answer"
                           [(ngModel)]="securityQuestion1.answer" type="text"
                           id="SecurityAnswer1" name="SecurityAnswer1" required
                           [ngClass]="{ 'ess-ca-error': (securityAnswer1.touched && securityAnswer1.invalid) }" />
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label for="SecurityQuestion2">{{securityQuestion2.question}}</label>
                    <input #securityAnswer2="ngModel" placeholder="Answer"
                           [(ngModel)]="securityQuestion2.answer" type="text"
                           id="SecurityAnswer2" name="SecurityAnswer2" required
                           [ngClass]="{ 'ess-ca-error': (securityAnswer2.touched && securityAnswer2.invalid) }" />
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label for="SecurityQuestion3">{{securityQuestion3.question}}</label>
                    <input #securityAnswer3="ngModel" placeholder="Answer"
                           [(ngModel)]="securityQuestion3.answer" type="text"
                           id="SecurityAnswer3" name="SecurityAnswer3" required
                           [ngClass]="{ 'ess-ca-error': (securityAnswer3.touched && securityAnswer3.invalid) }" />
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label>Send the Verification Code to *</label>
                    <p-dropdown id="passwordVerificationCode" [options]="twoFactorProviders"
                                [(ngModel)]="verifySecurityQuestionsInput.twoFactorProvider" name="twoFactorProvider">
                    </p-dropdown>
                </div>
                <div class="ess-ca-form-control col-flex" *ngIf="verifySecurityQuestionsInput.hasTwoFactorCode">
                    <label for="TwoFactorCode">{{"Verification Code" | localize}}</label>
                    <input id="passwordTwoFactorCode" #twoFactorCodeInput="ngModel" placeholder="Code"
                           [(ngModel)]="verifySecurityQuestionsInput.twoFactorCode" type="text"
                           name="TwoFactorCode">
                    <span>
                        Time Remaining: <strong>{{displayMinutes | number : '1.0-0'}}:{{displaySeconds | number : '2.0-0'}}</strong>
                    </span>
                </div>

                <div class="ess-ca-form-control">
                    <button id="passwordSubmit" pButton [disabled]="!securityQuestionsForm.form.valid" type="submit" label="Continue"
                            class="ui-button-rounded ui-button-warning"></button>
                </div>

                <div class="ess-ca-form-control">
                    <bdo-needhelp></bdo-needhelp>
                </div>
            </form>
        </div>
    </div>

</div>
