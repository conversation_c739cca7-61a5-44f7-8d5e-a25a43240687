
<div bsModal #recipientmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="recipientmodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Recipient Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #recipientForm="ngForm">
                    <p-card>
                        <ngFormValidation #recepientFormValidation displayMode="1" [extraValidation]="extraValidation"></ngFormValidation>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l('IERecDetName')}}<span class="required">*</span></label>
                                <div [ngClass]="{'input-error-box-thin':  hasError('Name of Entity') }">
                                    <input pInputText type="text"
                                           class="form-control"
                                           [(ngModel)]="detail.nameEntityPerson"
                                           maxlength="150"
                                           required
                                           name="Name of Entity" id="entitynmae">
                                </div>
                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l("IERecDetJuris")}}<span class="required">*</span> </label>
                                <div [ngClass]="{'input-error-box-thin':  hasCustomError('Jurisdiction') }">
                                    <p-dropdown [options]="countries"
                                                id="selectedjurisdiction"
                                                name="Jurisdiction"
                                                optionLabel="countryName"
                                                [(ngModel)]="detail.jurisdictionResidence"></p-dropdown>
                                </div>

                            </div>
                        </div>

                        <div class="row-flex-justified margin-top">
                            <div class="col-flex full-width">
                                <label>{{l("IERecDetAddressLine1")}} <span class="required">*</span> </label>

                                <div [ngClass]="{ 'input-error-box-thin': hasError('Address Line1') }">
                                    <input pInputText type="text"
                                           class="form-control"
                                           [(ngModel)]="detail.address.addressLine1"
                                           required maxlength="1000"
                                           name="Address Line1" id="addressLine1">
                                </div>
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l("IERecDetAddressLine2")}}</label>
                                <input pInputText type="text" class="form-control"
                                       [(ngModel)]="detail.address.addressLine2" maxlength="1000"
                                       name="addressLine2" id="addressLine2">
                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l("IERecDetCountry")}} <span class="required">*</span></label>
                                <div [ngClass]="{'input-error-box-thin':  hasCustomError('Country') }">
                                    <p-dropdown [options]="countries"
                                                id="selectedCountry"
                                                name="selectedCountry"
                                                optionLabel="countryName"
                                                [(ngModel)]="detail.address.country"></p-dropdown>
                                </div>

                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex width-45">
                                <label>{{l('IERecDetTin')}}</label>
                                <div>
                                    <input pInputText type="text"
                                           class="form-control"
                                           [(ngModel)]="detail.tin"
                                           maxlength="150"
                                           name="tinnmae" id="tinnmae">
                                </div>
                            </div>
                            <div *ngIf="detail.tin" class="col-flex width-45">
                                <label>{{("Issued Country")}} <span class="required">*</span></label>
                                <div [ngClass]="{'input-error-box-thin':  hasCustomError('IssuedCountry') }">
                                    <p-dropdown [options]="countries"
                                                id="IssuedCountry"
                                                name="IssuedCountry"
                                                optionLabel="countryName"
                                                [(ngModel)]="detail.tinIssuedCoutry"></p-dropdown>
                                </div>
                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l('IERecDetOtherIden')}}</label>
                                <div>
                                    <input pInputText type="text"
                                           class="form-control"
                                           [(ngModel)]="detail.otherIdentification"
                                           maxlength="200"
                                           name="othernmae" id="othernmae">
                                </div>
                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>{{l('IERecDetType')}}<span class="required">*</span></label>
                                <div [ngClass]="{'input-error-box-thin':  hasError('Type of Entity Person') }">
                                    <p-dropdown [options]="entityStatus" name="Type of Entity Person"
                                                [(ngModel)]="selectedEntityStatus"
                                                optionLabel="description"></p-dropdown>

                                </div>
                            </div>
                        </div>
                     
                            <div class="col-flex full-width" *ngIf="selectedEntityStatus && selectedEntityStatus.value === 0">
                                <label>{{('UBO Type')}}<span class="required">*</span></label>
                                <div [ngClass]="{'input-error-box-thin':  hasCustomError('recipient') }">
                                    <p-dropdown [options]="boStatuses" name="UBO"
                                                [(ngModel)]="selectedboStatus"
                                                
                                                optionLabel="description"></p-dropdown>

                                </div>
                            </div>
                  



                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
