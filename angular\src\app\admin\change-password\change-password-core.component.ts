import { Component, Injector, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ChangePasswordInput, ProfileServiceProxy } from '@shared/service-proxies/service-proxies';
import { Subscription, timer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';
import { AppAuthService } from '../../shared/common/auth/app-auth.service';

@Component({
    selector: 'changePasswordCore',
    templateUrl: './change-password-core.component.html'
})
export class ChangePasswordCoreComponent extends AppComponentBase {
    @ViewChild('passwordFormValidation', { static: true }) passwordFormValidation: NgFormValidationComponent;
    @ViewChild('passwordForm', { static: true }) passwordForm: FormGroup;

    savingPassword: boolean = false;
    passwordInput: ChangePasswordInput = new ChangePasswordInput();
    passwordConfirm: string;

    passwordRemainingSeconds: number = 0;
    passwordDisplayMinutes: number = 0;
    passwordDisplaySeconds: number = 0;
    passwordTimerSubscription: Subscription;

    constructor(
        injector: Injector,
        private _profileService: ProfileServiceProxy,
        private _authService: AppAuthService
    ) {
        super(injector);
    }

    ngOnInit() {
        this.passwordFormValidation.formGroup = this.passwordForm;

        this.resetPasswordForm();
    }

    passwordHasError(fieldName: string): boolean {
        return this.passwordFormValidation.fieldHasErrors(fieldName);
    }

    resetPasswordForm(): void {
        this.passwordForm.reset();

        this.passwordInput.twoFactorProvider = this.twoFactorProviders[0].value;
        this.passwordInput.hasTwoFactorCode = false;
        this.passwordRemainingSeconds = 0;
        this.passwordDisplayMinutes = 0;
        this.passwordDisplaySeconds = 0;
        if (this.passwordTimerSubscription) {
            this.passwordTimerSubscription.unsubscribe();
        }
    }

    savePassword(): void {
        if (!this.passwordFormValidation.isFormValid()) {
            return;
        }

        this.savingPassword = true;
        this._profileService.changePassword(this.passwordInput)
            .pipe(finalize(() => { this.savingPassword = false; }))
            .subscribe(result => {
                if (!result.isCurrentPasswordValid || !result.isNewPasswordDifferent) {
                    if (!result.isCurrentPasswordValid) {
                        var control = this.passwordForm.controls["CurrentPassword"];
                        if (control) {
                            control.markAsTouched();

                            control.setErrors({
                                customErrors: {
                                    errors: ['{0} is incorrect.']
                                }
                            });
                        }
                    }
                    if (!result.isNewPasswordDifferent) {
                        var control = this.passwordForm.controls["NewPassword"];
                        if (control) {
                            control.markAsTouched();

                            control.setErrors({
                                customErrors: {
                                    errors: ['{0} cannot be the same as current password.']
                                }
                            });
                        }
                    }
                } else if (result.requiresTwoFactor) {
                    this.passwordInput.hasTwoFactorCode = true;
                    this.passwordRemainingSeconds = this.twoFactorExpirySeconds;

                    const timerSource = timer(0, 1000);
                    this.passwordTimerSubscription = timerSource.subscribe(() => {
                        this.passwordRemainingSeconds = this.passwordRemainingSeconds - 1;

                        this.passwordDisplayMinutes = Math.floor(Math.round(this.passwordRemainingSeconds / 60 * 100) / 100);
                        this.passwordDisplaySeconds = this.passwordRemainingSeconds - this.passwordDisplayMinutes * 60;

                        if (this.passwordRemainingSeconds === 0) {
                            this.message.warn(this.l('TimeoutPleaseTryAgain')).then(() => {
                                this.passwordInput.hasTwoFactorCode = false;
                                this.passwordTimerSubscription.unsubscribe();
                            });
                        }
                    });
                } else {
                    this.passwordForm.reset();
                    this.notify.info(this.l('YourPasswordHasChangedSuccessfully'));
                    this._authService.logout();
                }
            });
    }
}
