import { Component, OnInit, Injector, Input, ViewChild, Output, EventEmitter } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, ValidationResultMainDto, ValidationResultDto, ValidationType } from '@shared/service-proxies/service-proxies';
import { EconomicSubstanceService, } from '@app/economicsubstance/economicsubstance.service';

import { NgForm } from '@angular/forms';
import { start } from 'repl';
import * as _ from 'lodash';

@Component({
  selector: 'app-step1-es',
  templateUrl: './step1-es.component.html',
  styleUrls: ['./step1-es.component.css']
})
export class Step1ESComponent extends AppComponentBase implements OnInit
{

    @Input() public economicsubstance: EconomicSubstanceDeclarationDto; // this is typed as string, but you can use any type you want
    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() importOnlyMode: boolean;
    @Input()  localFiscalStartDate: any;
    @Input() localFiscalEndDate: any;
    @ViewChild('#step1', { static: false }) form: NgForm;
    ShowHideConditions: boolean[] = [];
    @Output() warningFirstChanged = new EventEmitter<boolean>();
    constructor(injector: Injector, public _economicSubstanceService: EconomicSubstanceService) { super(injector);}


    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }

    ShowHideResult(index) {

        this.ShowHideConditions[0] = !this.IsNullorUndefined(this.economicsubstance.isFinaincialCanChange);
        return this.ShowHideConditions[index]
        
    }
    setWarningFirst(): void {
        this.warningFirstChanged.emit(true);
    }
    
    ngOnInit() {
     
        this._economicSubstanceService.validations$.subscribe((data: ValidationResultMainDto[]) => {
            if (data)
            {
                data.forEach((validation: ValidationResultMainDto) => {
                    if (validation.activityName == "Step1")
                    {
                        let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                        if (backendValidations != null) this.runNextValidations(backendValidations);
                    }
                });
            }
           
        });
    }



    runNextValidations(validations: ValidationResultDto[])
    {
        //this._economicSubstanceService.setFormPrestineValue(this.form.pristine);
        validations.forEach((validation: ValidationResultDto) =>
        {
            if (!this._economicSubstanceService.step1Error[validation.fieldName])
            {
                this._economicSubstanceService.step1Error[validation.fieldName] = true;
                    if (validation.validationType == ValidationType.Error) {

                        this._economicSubstanceService.errorList.push(validation.validationString);
                    }
                    else {
                        this._economicSubstanceService.warningList.push(validation.validationString);

                    }
            }
        })

    }

    ChoseFinaicialPeriod(event: any)
    {
        if (!this.economicsubstance.isFinaincialCanChange)
        {
            this.economicsubstance.fiscalStartDateString = this.economicsubstance.defaultStartDateString;
            this.economicsubstance.fiscalEndDateString = this.economicsubstance.defaultEndDateString;


        }
        else {
       
            this.economicsubstance.fiscalStartDateString = this.localFiscalStartDate;
            this.economicsubstance.fiscalEndDateString = this.localFiscalEndDate;
        }

        

    }
   

}
