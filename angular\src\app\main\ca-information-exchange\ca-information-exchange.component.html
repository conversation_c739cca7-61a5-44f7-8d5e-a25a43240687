<div class="informationExchangeContainer">
    <p-card class="p-card-properties_dashboard">
        <p-header class="row-flex-space">
            <p>{{ l("Information Exchange") }}</p>
        </p-header>
        <div class="margin-top">
            <p-card class="p-card-properties_dashboard">
                <div class="row-flex-space">
                    <div class="half-and-quarter-width">
                        <b>{{ "Stats Summary:" | localize }}</b>
                        <p-table [value]="summary">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>
                                        {{ "Total # of Reports" | localize }}
                                    </th>
                                    <th>
                                        {{
                                            "Total # of Reports Sent" | localize
                                        }}
                                    </th>
                                    <th>
                                        {{
                                            "Total # of Reports Ready for Exchange"
                                                | localize
                                        }}
                                    </th>
                                    <th>
                                        {{
                                            "Total # of Reports for Review"
                                                | localize
                                        }}
                                    </th>
                                    <th>
                                        {{
                                            "Total # of Reports not sent"
                                                | localize
                                        }}{{ _selectedYear }}
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item>
                                <tr>
                                    <td>{{ item.totalNoofReports }}</td>
                                    <td>
                                        {{ item.totalNoofExchangedReports }}
                                    </td>
                                    <td>
                                        {{
                                            item.totalNoofReadyExchangedReports
                                        }}
                                    </td>
                                    <td>{{ item.totalNoofReviewReports }}</td>
                                    <td>{{ item.totalNotSentReports }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
                <div class="row-flex-space margin-top-15">
                    <div class="col-flex margin-top-15 width-80">
                        <div>
                            <button
                                id="NonComplianceXML"
                                pButton
                                type="submit"
                                label="Non-compliance XML"
                                (click)="GenerateCompliance()"
                                class="
                                    ui-button-rounded ui-button-warning
                                    margin-right-5
                                "
                                [disabled]="!canGenerateXMLData()"
                            ></button>

                            <button
                                id="HighRiskIPXML"
                                pButton
                                type="submit"
                                label="High Risk IP XML"
                                (click)="GenerateIP()"
                                class="
                                    ui-button-rounded ui-button-warning
                                    margin-right-5
                                "
                                [disabled]="!canGenerateXMLData()"
                            ></button>

                            <button
                                id="NonResidentXML"
                                pButton
                                type="submit"
                                label="Non-resident XML"
                                (click)="GenerateNonResidence()"
                                class="ui-button-rounded ui-button-warning"
                                [disabled]="!canGenerateXMLData()"
                            ></button>
                        </div>
                    </div>
                    <div class="col-flex">
                        <label
                            ><b>{{ "Report Status" | localize }}</b></label
                        >
                        <p-dropdown
                            id="reportStatus"
                            [options]="informationStatuses"
                            name="informationStatuses"
                            [(ngModel)]="selectedInformationStatus"
                            optionLabel="description"
                            (onChange)="getDataAndSummary()"
                            required
                        ></p-dropdown>
                    </div>
                    <div class="col-flex">
                        <label
                            ><b>{{
                                "Financial Period End" | localize
                            }}</b></label
                        >
                        <p-dropdown
                            id=financialPeriodEnd
                            name="year"
                            [options]="years"
                            [style]="{ width: 'auto' }"
                            [(ngModel)]="selectedYear"
                            (onChange)="getDataAndSummary()"
                        >
                        </p-dropdown>
                    </div>
                </div>
            </p-card>
            <p-card class="p-card-properties_dashboard">
                <div class="primeng-datatable-container">
                    <p-table
                        #dataTable
                        (onLazyLoad)="getDataAndSummary($event)"
                        [value]="primengTableHelper.records"
                        rows="{{
                            primengTableHelper.defaultRecordsCountPerPage
                        }}"
                        [paginator]="false"
                        [lazy]="true"
                        [scrollable]="true"
                        ScrollWidth="100%"
                        selectionMode="none"
                    >
                        <ng-template pTemplate="header">
                            <tr>
                                <th
                                    style="width: 50px"
                                    pSortableColumn="ExchangeReason"
                                >
                                    {{ "Exchange Reason" | localize }}
                                    <p-sortIcon
                                        id="sortExchangeReason"
                                        field="ExchangeReason"
                                    ></p-sortIcon>
                                </th>
                                <th
                                    style="width: 50px"
                                    pSortableColumn="EntityName"
                                >
                                    {{ "Entity Name" | localize }}
                                    <p-sortIcon  id="sortEntityName" field="EntityName"></p-sortIcon>
                                </th>
                                <th
                                    style="width: 50px"
                                    pSortableColumn="CompanyFormationNumber"
                                >
                                    {{ "Incop#./Formation#" | localize }}
                                    <p-sortIcon
                                        id="sortCompanyFormationNumber"
                                        field="CompanyFormationNumber"
                                    ></p-sortIcon>
                                </th>
                                <th
                                    style="width: 50px"
                                    pSortableColumn="FiscalEndDate"
                                >
                                    {{ "Financial Period End Date" | localize }}
                                    <p-sortIcon
                                        id="sortFiscalEndDate"
                                        field="FiscalEndDate"
                                    ></p-sortIcon>
                                </th>

                                <th
                                    style="width: 50px"
                                    pSortableColumn="DueDate"
                                >
                                    {{ "Due Date" | localize }}
                                    <p-sortIcon id="sortDueDate" field="DueDate"></p-sortIcon>
                                </th>

                                <th
                                    style="width: 40px"
                                    pSortableColumn="InformationExchangeStatus"
                                >
                                    {{
                                        "Information Exchange Status" | localize
                                    }}
                                    <p-sortIcon
                                        id="sortInformationExchangeStatus"
                                        field="InformationExchangeStatus"
                                    ></p-sortIcon>
                                </th>
                                <th style="width: 30px">
                                    {{ "View Declaration" | localize }}
                                </th>
                                <th style="width: 20px">
                                    {{ "XML Data" | localize }}
                                </th>
                                <th style="width: 30px">
                                    {{ "View History" | localize }}
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit">
                            <tr [pSelectableRow]="record">
                                <td style="width: 50px">
                                    {{ record.exchangeReason | exchangereason }}
                                </td>
                                <td style="width: 50px">
                                    {{ record.entityName }}
                                </td>
                                <td style="width: 50px">
                                    {{ record.companyFormationNumber }}
                                </td>
                                <td style="width: 50px">
                                    {{
                                        record.fiscalEndDateString
                                            | date: "yyyy-MM-dd"
                                    }}
                                </td>
                                <td style="width: 50px">
                                    {{ record.dueDate | date: "yyyy-MM-dd" }}
                                </td>
                                <td
                                    [style.color]="getReviewStatusColor(record)"
                                    style="width: 40px"
                                >
                                    {{
                                        record.informationExchangeStatus
                                            | informationeschangestatus
                                    }}
                                </td>
                                <td style="width: 30px">
                                    <a (click)="viewSubmission(record)">View</a>
                                </td>
                                <td
                                    *ngIf="canUpdateXMLData()"
                                    style="width: 20px"
                                >
                                    <a id="XMLData" (click)="viewXml(record)">XML Data</a>
                                </td>
                                <td
                                    *ngIf="!canUpdateXMLData()"
                                    style="width: 20px"
                                >
                                    <a id="viewXML" (click)="viewXml(record, true)"
                                        >XML Data</a
                                    >
                                </td>
                                <td style="width: 30px">
                                    <div *ngIf="record.hasHistoryRecord">
                                        <a id="viewHistory" (click)="viewHistory(record)"
                                            ><i class="pi pi-undo"></i
                                        ></a>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div
                        class="primeng-no-data"
                        *ngIf="primengTableHelper.totalRecordsCount == 0"
                    >
                        {{ "NoData" | localize }}
                    </div>
                    <div class="primeng-paging-container">
                        <app-custom-paginator #customPagination id="informationExchangePaginator"
                        [totalRecordsCount]="primengTableHelper.totalRecordsCount"
                        [defaultRecordsCountPerPage]="primengTableHelper.defaultRecordsCountPerPage"
                        [predefinedRecordsCountPerPage]="primengTableHelper.predefinedRecordsCountPerPage"
                        (pageChange)="getDataAndSummary($event)"
                        (pageSizeChange)="getDataAndSummary($event)">
                      </app-custom-paginator>
                    </div>
                </div>
            </p-card>
        </div>
    </p-card>
    <informationHistoryModel #informationHistoryModel>
    </informationHistoryModel>
</div>
