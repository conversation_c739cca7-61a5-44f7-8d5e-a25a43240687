namespace Bdo.Ess.Dtos.FileUpload
{
    public enum ValidationPropertyType
    {
        // Standard
        Required,
        MaxLength,

        // Types
        Boolean,
        Integer,
        Decimal,

        // Formats
        Phone,
        Email,
        YearMonthDay,
        DayMonthYear,
        MonthDay,

        // Lookups
        Country,
        EntityStatus,
        Currency,
        CigaCode,
        RelevantActivity,

        // Conditional
        RequiredIfEqual,
        RequiredIfNotEqual,
        RequiredIfEmpty,
        RequiredIfNotEmpty,

        EmptyIfEqual,
        EmptyIfNotEqual,
        EmptyIfEmpty,
        EmptyIfNotEmpty,

        CigaMatch,
    }
}
