import { Component, Injector, ViewChild, ViewEncapsulation, AfterViewInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router'
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CustomPaginatorComponent } from '@shared/common/components/custom-paginator/custom-paginator.component';
import { CADashboardServiceProxy, DashboardSearchResultDto } from '@shared/service-proxies/service-proxies';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Paginator } from 'primeng/components/paginator/paginator';
import { Table } from 'primeng/components/table/table';
import { finalize } from 'rxjs/operators';

@Component({
    templateUrl: './dashboard-es-search.component.html',
    styleUrls: ['./dashboard-es-search.component.less'],
    encapsulation: ViewEncapsulation.None,
    animations: [appModuleAnimation()]
})
export class DashboardEsSearchComponent extends AppComponentBase implements AfterViewInit {
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild(CustomPaginatorComponent, { static: true }) paginationComponent: CustomPaginatorComponent;
    paginator: Paginator;
    searchId: string;
    title: string = "Results";
    createdAt: any = null;

    constructor(
        injector: Injector,
        private _router: Router,
        private _route: ActivatedRoute,
        private _dashboardServiceProxy: CADashboardServiceProxy
    ) {
        super(injector);

        this._route.params.subscribe(x => {
            this.searchId = x.id

            this._dashboardServiceProxy
                .getDashboardSearchInfo(x.id)
                .subscribe(result => {
                    this.title = result.title;
                    this.createdAt = result.createdAt;
                });
        })
    }

    ngOnInit() {
        if (this.paginationComponent) {
            this.paginator = this.paginationComponent.getPaginator();

         }
    }

    ngAfterViewInit(): void {
        this.primengTableHelper.adjustScroll(this.dataTable);
    }

    ngOnDestroy() {
    }

    getResults(event?: LazyLoadEvent) {
        if (this.primengTableHelper.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            return;
        }

        this.primengTableHelper.showLoadingIndicator();

        this._dashboardServiceProxy.getDashboardSearchResults(
            this.searchId,
            this.primengTableHelper.getSorting(this.dataTable),
            this.primengTableHelper.getMaxResultCount(this.paginator, event),
            this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }

    reloadPage(): void {
        this.paginator.changePage(this.paginator.getPage());
    }

    onRowSelect(record: any) {
    }

    goToReview(record: DashboardSearchResultDto): void {
        this._router.navigate(['app/economicsubstance/careview/' + record.economicSubstanceId + '/'  + record.ctspNumber]);
    }

    goToRedFlagsDashboard(e: any): void {
        this._router.navigate(['app/main/cadashboard/redflags']);
    }
}
