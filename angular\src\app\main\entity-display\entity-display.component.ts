import { Component, OnInit, Injector, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CorporateEntityDto } from '@shared/service-proxies/service-proxies';
import { CtspListDto, CtspServiceProxy, CASearchServiceServiceProxy } from '@shared/service-proxies/service-proxies';
import { elementAt } from 'rxjs/operators';
import { GetCtspInformation } from '../shared/get-ctps-Inforamtion'


@Component({
    selector: 'app-entity-display',
    templateUrl: './entity-display.component.html',
    styleUrls: ['./entity-display.component.css']
})
export class EntityDisplayComponent extends AppComponentBase implements OnInit {
    displayBasic: boolean;
    items: string[] = [];
    private findField(id: string): any {
        for (const fieldGroup of this.fields) {
            for (const field of fieldGroup) {
                if (field.id == id) {
                    return field
                }
            }
        }
    }

    @Input() set entity(val) {
        if (!val) return
        for (const prop in val) {
            var field = this.findField(prop)
            if (field) {
                field.value = val[prop]
            }
        }

        this.populateAddress(val);
        if (val.hasOwnProperty('ctspNumber') && val.hasOwnProperty('ctspName')) {
            const field = this.findField('ctspInfo')
            field.value = `${val.ctspName}`
            field.ctspId = `${val.ctspId}`
            field.disabled = false


            const field1 = this.findField('clientNumber')
            field1.disabled = true

        }
    }

    fields =
        [
            [
                {
                    type: "stringwithtooltip",
                    id: "ctspInfo",
                    name: "Registered with CSP",
                    value: "",
                    disabled: true,
                    ctspId: ""
                },
                {
                    type: "string",
                    id: "clientNumber",
                    name: "Unique ID",
                    value: "",
                },
                {
                    type: "entityType",
                    id: "entityType",
                    name: "Type",
                    value: "",
                },
                {
                    type: "string",
                    id: "name",
                    name: "Name",
                    value: "",
                },

                {
                    type: "string",
                    id: "statusText",
                    name: "Status",
                    value: "",
                },
                {
                    type: "string",
                    id: "companyNumber",
                    name: "Incorp. #",
                    value: "",
                },
                {
                    type: "string",
                    id: "formationNumber",
                    name: "Formation #",
                    value: "",
                },
        
                {
                    type: "string",
                    id: "alternativeName",
                    name: "Alternative Name",
                    value: "",
                },
                {
                    type: "address",
                    id: "registeredOfficeAddress",
                    name: "Registered Office Address",
                    value: "",
                },
                {
                    type: "date",
                    id: "incorporationDate",
                    name: "Date of Incorp.",
                    value: null,
                },
        
                {
                    type: "date",
                    id: "dateLiquidationCompleted",
                    name: "Date Liquidation Completed",
                    value: null,
                },

                {
                    type: "date",
                    id: "dissolvedDate",
                    name: "Date Dissolved",
                    value: null,
                },
                {
                    type: "date",
                    id: "mergedDate",
                    name: "Date Merged",
                    value: null,
                },
                {
                    type: "date",
                    id: "continuationOutDate",
                    name: "Date Continued-Out",
                    value: null,
                },
                {
                    type: "date",
                    id: "resignedDate",
                    name: "Date Resigned",
                    value: null,
                },
                {
                    type: "bool",
                    id: "listedOnStockExchange",
                    name: "Has Securities Listed on a Recognised Stock Exchange",
                },

                {
                    type: "string",
                    id: "stockExchange",
                    name: "Stock Exchange",
                },


                {
                    type: "bool",
                    id: "isDeleted",
                    name: "Deleted",
                }
      ],
    ]

    constructor(injector: Injector, public _getCtspInformation: GetCtspInformation, public _ctspServiceProxy: CtspServiceProxy, private searchService: CASearchServiceServiceProxy) {
    super(injector)
  }

  ngOnInit() {
  }

    showBasicDialog(item: any)
    {

        this._ctspServiceProxy.getCtspDetailById(item).subscribe(result =>
        {
            this.items = this._getCtspInformation.getCtspInformation(result);
            this.displayBasic = true;
        });
    }

    formatAddress(val:any){
        const addrValues = [val.addressLine1, val.addressLine2, val.countryName];
        this.findField('registeredOfficeAddress').value = addrValues.filter(x => x).join(', ');            
    }
    populateAddress(val:any){
        if (val.ctspNumber && val.id){
            this.searchService.getEntityAddressDetail(val.ctspNumber, val.id).subscribe(v => {
                val.addressLine1 = v.addressLine1;
                val.addressLine2 = v.addressLine2;
                this.formatAddress(val);
            });
        }
        else {
            this.formatAddress(val);
        }
    }
}
