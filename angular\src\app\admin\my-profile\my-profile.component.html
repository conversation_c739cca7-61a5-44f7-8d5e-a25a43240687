<div class="row-flex-center" [@routerTransition]>
    <div class="width-40 margin-top">
        <p-card class="p-card-properties_form">
            <p-header>
                <p>{{l("MY PROFILE")}}</p>
            </p-header>

            <tabset #tabs>
                <tab heading="{{'MY SETTINGS' | localize}}">
                    <ngFormValidation #settingsFormValidation displayMode="2"></ngFormValidation>

                    <form #settingsForm="ngForm">
                        <div>
                            <label for="Username">{{"Username" | localize}}</label>
                            <div>
                                <input id="Username" #usernameInput="ngModel" type="text" name="Username" class="form-control" [(ngModel)]="user.userName" disabled required />
                            </div>
                        </div>
                        <div class="margin-top-5">
                            <label for="EmailAddress">{{"EmailAddress" | localize}} *</label>
                            <div [ngClass]="{ 'input-error': settingsHasError('EmailAddress') }">
                                <input id="EmailAddress" #emailAddressInput="ngModel" type="email" (change)="onUpdateEmail()"
                                       name="EmailAddress" class="form-control" [(ngModel)]="user.emailAddress" required maxlength="512" email />
                            </div>
                        </div>
                        <div class="margin-top-5">
                            <label for="FirstName">{{"FirstName" | localize}} *</label>
                            <div [ngClass]="{ 'input-error': settingsHasError('FirstName') }">
                                <input id="FirstName" #firstNameInput="ngModel" class="form-control" type="text" name="FirstName" [(ngModel)]="user.name" required maxlength="256">
                            </div>
                        </div>
                        <div class="margin-top-5">
                            <label for="LastName">{{"LastName" | localize}} *</label>
                            <div [ngClass]="{ 'input-error': settingsHasError('LastName') }">
                                <input id="LastName" #lastNameInput="ngModel" type="text" name="LastName" class="form-control" [(ngModel)]="user.surname" required maxlength="256">
                            </div>
                        </div>
                        <div class="margin-top-5">
                            <label for="PhoneNumber">{{"PhoneNumber" | localize}} *</label>
                            <div [ngClass]="{ 'input-error': settingsHasError('PhoneNumber') }">
                                <ngx-intl-tel-input id="myProfilePhoneNumber"
                                                    #phoneNumberInput
                                                    [(ngModel)]="phoneNumber"
                                                    [enableAutoCountrySelect]="true"
                                                    [enablePlaceholder]="true"
                                                    [searchCountryFlag]="true"
                                                    [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                                                    [selectFirstCountry]="true"
                                                    [preferredCountries]="preferredCountries"
                                                    [maxLength]="15"
                                                    [tooltipField]="TooltipLabel.Name"
                                                    [phoneValidation]="true"
                                                    [separateDialCode]="true"
                                                    name="PhoneNumber"
                                                    required>
                                </ngx-intl-tel-input>
                            </div>
                        </div>
                        <div class="margin-top-5">
                            <label for="Role">{{"Role" | localize}}</label>
                            <div>
                                <input id="Role" #roleInput="ngModel" type="text" name="Role" class="form-control" [(ngModel)]="user.role" disabled />
                            </div>
                        </div>
                    </form>
                    <div class="row-flex-space margin-top">
                        <button id="myProfileCancel" pButton type="button" (click)="settingsForm.reset()" [disabled]="savingSettings" label="{{l('Cancel')}}"></button>
                        <button id="myProfileSave" pButton type="button" (click)="saveSettings()" [disabled]="validatingEmail || savingSettings" label="{{l('Save')}}"></button>
                    </div>
                </tab>
                <tab heading="{{'CHANGE PASSWORD' | localize}}">
                    <changePasswordCore></changePasswordCore>
                </tab>
                <tab heading="{{'SECURITY QUESTIONS' | localize}}">
                    <securityQuestionsCore></securityQuestionsCore>
                </tab>
            </tabset>
        </p-card>
    </div>
</div>
