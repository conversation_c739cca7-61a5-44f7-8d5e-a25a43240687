import { Component, OnInit, Input, Injector, ViewChild, Output, EventEmitter } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CorporateEntityServiceProxy, AddEntityInput, UpdateEntityInput } from '@shared/service-proxies/service-proxies';
import { TopbarButtonAction } from '@app/shared/layout/topbar.service';
import { takeWhile, finalize } from 'rxjs/operators';
import { Location } from '@angular/common';
import { EntityEditComponent } from '../entity-edit/entity-edit.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-entity-edit-container',
  templateUrl: './entity-edit-container.component.html',
  styleUrls: ['./entity-edit-container.component.css']
})
export class EntityEditContainerComponent extends AppComponentBase implements OnInit {

  @Output() onReview: EventEmitter<string> = new EventEmitter<string>()

  @ViewChild(EntityEditComponent, { static: false }) editor: EntityEditComponent

  handleReviewCancel() {
    this.location.back();
  }

  handleReviewBackToEdit() {
    this.editing = true
  }

  handleReviewSave() {
    if (this.buttonSubscription) {
      this.buttonSubscription.unsubscribe()
    }

    if (this.isNew) {
      const input = Object.assign(new AddEntityInput(), this.entity)

      this._entityService.addEntity(input).pipe(
        finalize(() => this.subscribeButtons())
      ).subscribe(x => {
        this.onReview.emit(x)
      })
    } else {
      // rename to updateInput due to chrome debugging
      const updateInput = Object.assign(new UpdateEntityInput(), this.entity)
      this._entityService.updateEntity(updateInput).pipe(
        finalize(() => this.subscribeButtons())
      ).subscribe(_ => {
        this.onReview.emit(this.entity.id)
      })
    }
  }

  handleEditCancel() {
    this.location.back();
  }

  handleEditContinue() {
    this.editor.submit()
  }

  handleEditSubmitted(val) {
    this.entity = { id: this.entity.id }
    Object.assign(this.entity, val)
    this.editing = false
  }

  @Input() isNew: boolean

  constructor(injector: Injector, private _entityService: CorporateEntityServiceProxy, private location: Location) {
    super(injector)
  }

  ngOnInit() {
    this.editing = true
  }

  ngOnDestroy() {
    this.topbarService.clearValues();
  }

  @Input() set entityId(val: string) {
    this._entityService.getEntity(val).subscribe(x => {
      this.entity = x
    })
  }

  // need to provide default values
  entity: any = { alternativeName: '' }

  private buttonSubscription: Subscription

  private subscribeButtons() {
    if (this.buttonSubscription) {
      this.buttonSubscription.unsubscribe()
    }

    if (this.editing) {
      this.buttonSubscription = this.topbarService.topbarOnButtonClick$.pipe(
        takeWhile(x => x != null)
      ).subscribe((buttonAction: TopbarButtonAction) => {
        switch (buttonAction) {
          case TopbarButtonAction.Cancel:
            this.handleEditCancel();
            break;
          case TopbarButtonAction.Continue:
            this.handleEditContinue();
            break;
        }
      });
    } else {
      this.buttonSubscription = this.topbarService.topbarOnButtonClick$.pipe(
        takeWhile(x => x != null)
      ).subscribe((buttonAction: TopbarButtonAction) => {
        switch (buttonAction) {
          case TopbarButtonAction.Cancel:
            this.handleReviewCancel();
            break;
          case TopbarButtonAction.BackToEdit:
            this.handleReviewBackToEdit();
            break;
          case TopbarButtonAction.Save:
            this.handleReviewSave();
            break;
        }
      });
    }
  }

  private _editing: boolean = true
  get editing() {
    return this._editing;
  }
  set editing(val: boolean) {
    this.topbarService.clearValues();
    this.topbarService.notifyTitle(this.isNew ? this.l('Add New Entity') : this.l('Edit Entity'));

    this._editing = val
    if (this.editing) {
      this.topbarService.notifyButtonsToTopbar([
        { label: this.l("Cancel"), action: TopbarButtonAction.Cancel },
        { label: this.l("Continue to Review"), action: TopbarButtonAction.Continue },
      ]);
    } else {
      this.topbarService.notifyButtonsToTopbar([
        { label: this.l("Cancel"), action: TopbarButtonAction.Cancel },
        { label: this.l("Back to Edit"), action: TopbarButtonAction.BackToEdit },
        { label: this.l("Save & Confirm"), action: TopbarButtonAction.Save },
      ]);
    }

    this.subscribeButtons()
  }
}
