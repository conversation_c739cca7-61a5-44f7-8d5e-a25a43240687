import { Location } from '@angular/common';
import { Component, Injector, HostListener, Input, ViewChild} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SubmittedLocation } from '@app/economicsubstance/EconomicSubstance';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    EconomicSubstanceDeclarationDto, CurrencyDto, CountryDto, CIGALookUpDto,
    CorporateEntityDto, EconomicSubstanceServiceProxy, 
    AuditEventServiceProxy, SingleFieldInputOfGuid, RelevantActivityDetailDto,
    EconomicSubstanceInformationRequiredDto, WrokflowServiceProxy, ESCAAssessmentDto, CorporateEntityServiceProxy, GetEntitiesSearchInput,
    AmendmentRequest
} from '@shared/service-proxies/service-proxies';
import { forkJoin } from 'rxjs';
import { PrintService } from '@shared/services/print.service';
import * as moment from 'moment';
import { EconomicSubstanceService, EsAction } from '@app/economicsubstance/economicsubstance.service';
import { downloadFile } from '@app/main/viewmodel/utils';
import { AppConsts } from '@shared/AppConsts';
import { ModalDirective } from 'ngx-bootstrap';


@Component({
  selector: 'app-display-submitted',
  templateUrl: './display-submitted.component.html',
  styleUrls: ['./display-submitted.component.css']
})
export class DisplaySubmittedComponent extends AppComponentBase {
    id: any;
    readytoDisplay:boolean= false;
    @ViewChild('reopenModal', {static: false}) reopenModal: ModalDirective;
    

    corporateEntity: CorporateEntityDto;
    currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    currencies: CurrencyDto[];
    countries: CountryDto[];
    cigaLookup: CIGALookUpDto[];
    selectedActivity: RelevantActivityDetailDto[] = [];
    informationRequired: EconomicSubstanceInformationRequiredDto;
    informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[] = [];
    country: any;
    action: any;
    importOnlyMode: boolean = false;
    importStatus: string = '';
    fileid: any;
    esassessment: ESCAAssessmentDto;
    reopenReason: string;
    errorMessage: string;

    constructor(injector: Injector, private location: Location,
        private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy,
        private _wrokflowServiceProxy: WrokflowServiceProxy,
        private _auditEventService: AuditEventServiceProxy,
        private _corporateEntityServiceProxy: CorporateEntityServiceProxy,
        private _sharedComponet: SharedComponent,
        private router: Router,
        private route: ActivatedRoute,
        public printService: PrintService,
        public _economicSubstanceService: EconomicSubstanceService,)
    {
        super(injector)

        this._economicSubstanceService.initializeEmptyError();

        this.route.params.subscribe(x =>
        {
             this.id = x.economicsubid;
            this.action = x.action;
            if (this.action === "4") {
                this.fileid = x.uploadid;
            }

            forkJoin([
                this._economicSubstanceServiceProxy.getEconomicSubstanceDetailById(this.id, undefined, true),
                this._wrokflowServiceProxy.getESAssessment(undefined, this.id, undefined)
            ]).subscribe(responseList => {
                
                this.currentEconomicSubstance = responseList[0];
                this.esassessment = responseList[1];
               this._sharedComponet.convertDatetime(this.currentEconomicSubstance, responseList[0]);
                this.corporateEntity = this.currentEconomicSubstance.corporateEntity;

                if (typeof this.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined")
                {
                    this.informationRequired = null;
                    this.informationRequiredsHistory = null;
                }
                else {
                    let result = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
                    if (result != null && result.length>0) {
                        this.informationRequired = result[0];
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.id !== this.informationRequired.id);
                    }
                    else {
                        this.informationRequired = null
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired;
                    }
                }

                this.readytoDisplay = true;

            });

            let auditInput = new SingleFieldInputOfGuid();
            auditInput.value = this.id;
            this._auditEventService.auditCtspEconomicSubstanceViewEvent(auditInput).subscribe();
        });
    }

    @HostListener("window:afterprint", [])
    onWindowAfterPrint() {
        let auditInput = new SingleFieldInputOfGuid();
        auditInput.value = this.id;
        this._auditEventService.auditCtspEconomicSubstancePrintEvent(auditInput).subscribe();
    }

    onPrint() {
        this.printService.isEconomicSubstancePrintMode = true;
        this.printService.print();
    }
    requestAmendment(id: any): void {
        this.reopenModal.show();
    }
    
    
    saveAmendmentRequest(): void {
        if (this.reopenReason && this.reopenReason.length <= 250) {
            var requestAmmendment = new AmendmentRequest();
            requestAmmendment.entityName = this.currentEconomicSubstance.corporateEntity.name;
            requestAmmendment.incorpNumber = this.currentEconomicSubstance.corporateEntity.companyNumber;
            requestAmmendment.uniqueId = this.currentEconomicSubstance.corporateEntity.clientNumber;
            requestAmmendment.financialPeriodEndDate = this.currentEconomicSubstance.fiscalEndDateString.toString();
            requestAmmendment.reopenReason = this.reopenReason; // Add the reopen reason to the request
            requestAmmendment.corporateEntityId = this.currentEconomicSubstance.corporateEntityId;
            requestAmmendment.economicSubstanceId = this.currentEconomicSubstance.id;
            this._economicSubstanceServiceProxy.requestToReopenEconomicSubstance(requestAmmendment).subscribe(() => {
                abp.notify.success('Amendment request sent successfully.');
                this.currentEconomicSubstance.isEmailSentToReopen = true;
                this.reopenReason = null;
                this.reopenModal.hide();
            });
        }
        else if(!this.reopenReason){
            this.errorMessage = 'Reopen reason is required';
        }
        else if (this.reopenReason!=null && this.reopenReason.length > 250) {
            this.errorMessage = 'Reopen reason must be 250 characters or less';
    }   else {
            this.errorMessage = null;
    }
    }
    closeReopenReasonModal(): void {
        this.reopenReason = null;
        this.reopenModal.hide();
    }
    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'display': 'none',
            };
        }

        return {};
    }

    checkIfProvisionalTreatmentSubmission(item): boolean {
        return (typeof item.provisionalTreatmentSubmissionDate !== "undefined");
    }

    checkIfinformationRequired(): boolean
    {
        return (this.informationRequired !== null);
    }
    onSubmitForProvisionalTreatment()
    {
        this._economicSubstanceService.initializeEmptyError();
        this._economicSubstanceServiceProxy.validateProvisionalTreatment(this.currentEconomicSubstance).subscribe(result => {
            if (result && result.validationString) {
                this._economicSubstanceService.ProvisionalTreatment = true;
                this._economicSubstanceService.errorList.push(result.validationString);
            }
            else
            {
                this.saveProvisionalTreatment();
            }
        });

    }

    onSubmitForRequestInformation()
    {
        this._economicSubstanceService.initializeEmptyError();
        this._economicSubstanceServiceProxy.validateInformationRequired(this.informationRequired).subscribe(result => {
            if (result && result.validationString)
            {
                this._economicSubstanceService.InformationRequired = true;
                this._economicSubstanceService.errorList.push(result.validationString);
            }
            else {
                this.saveInformationRequired();
            }
        });

    }
    
    isCtspPoratal(){
        if(AppConsts.isCtspPortal) return true;
    }

    generateExcel(){

        //Inilitialize
        var request = new GetEntitiesSearchInput();

        if (this.currentEconomicSubstance && this.currentEconomicSubstance.id) {
            
            request.corporateEntityId = this.currentEconomicSubstance.corporateEntityId;
            request.economicDeclarationId = this.currentEconomicSubstance.id;
            request.financialPeriodEndYear =  parseInt(moment(this.currentEconomicSubstance.fiscalEndDate).format("YYYY"));
            request.includeRequiringDeclaration = true;
            request.skipCount = 0;
            this._corporateEntityServiceProxy.generateExportFile(request).subscribe(x => {
                if(x.content != null && x.fileName != null){
                    downloadFile(x.content, x.fileName)
                }
    
        });
    }
  }
    saveProvisionalTreatment() {
        this.currentEconomicSubstance.provisionalTreatmentSubmissionDate = moment.utc();
        this._economicSubstanceServiceProxy.submitProvisionalTreatment(this.currentEconomicSubstance).subscribe
            (result => {
                this.currentEconomicSubstance = result;
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, result);
                this.corporateEntity = this.currentEconomicSubstance.corporateEntity;


                if (typeof this.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined") {
                    this.informationRequired = null;
                    this.informationRequiredsHistory = null;
                }
                else {
                    let result = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
                    if (result != null && result.length > 0) {
                        this.informationRequired = result[0];
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.id !== this.informationRequired.id);
                    }
                    else {
                        this.informationRequired = null
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired;
                    }
                }
            });

    }
    saveInformationRequired() {
        this.informationRequired.informationRequestedSubmissionDate = moment.utc();
        this._economicSubstanceServiceProxy.submitInformationRequest(this.informationRequired).subscribe
            (result => {
                this.currentEconomicSubstance = result;
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, result);
                this.corporateEntity = this.currentEconomicSubstance.corporateEntity;


                if (typeof this.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined") {
                    this.informationRequired = null;
                    this.informationRequiredsHistory = null;
                }
                else {
                    let result = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
                    if (result != null && result.length > 0) {
                        this.informationRequired = result[0];
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.id !== this.informationRequired.id);
                    }
                    else {
                        this.informationRequired = null
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired;
                    }
                }

            });
    }
    onClose()
    {
            if (this.action === "1") {
                this.location.back();
                this.location.back();
            }
            if (this.action === "2") { this.location.back(); }

            if (this.action === "3") {
                this.location.back();
                this.location.back();
                this.location.back();
            }

            if (this.action === "4")
            {
                this.router.navigate(['app/main/filetriage/' + this.fileid]);
            }
       
    }
}
