import { Component, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AttendMeetingDetailsDto } from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { ModalDirective } from 'ngx-bootstrap';
import * as uuid from 'uuid';
import { NgFormValidationComponent } from '../../../../../shared/utils/validation/ng-form-validation.component';

@Component({
  selector: 'app-meeting-detail-dialog',
  templateUrl: './meeting-detail-dialog.component.html',
  styleUrls: ['./meeting-detail-dialog.component.css']
})
export class MeetingDetailDialogComponent extends AppComponentBase implements OnInit {

    @Output() submitted: EventEmitter<AttendMeetingDetailsDto> = new EventEmitter<AttendMeetingDetailsDto>();
    meetingDetails: AttendMeetingDetailsDto = new AttendMeetingDetailsDto();
    importOnlyMode: boolean;
    @ViewChild('meetingmodal', { static: true }) modal: ModalDirective;
    @ViewChild('meetingform', { static: true }) meetingform: FormGroup;
    @ViewChild('meetingFormValidation', { static: true }) meetingFormValidation: NgFormValidationComponent;

    constructor(injector: Injector) { super(injector); }

    ngOnInit() {
        this.meetingFormValidation.formGroup = this.meetingform;
    }
    shown(): void {
    }
    show(item, isNew, importOnlyMode): void {
        
        this.importOnlyMode = importOnlyMode;
        this.meetingDetails = new AttendMeetingDetailsDto();
        if (!isNew) this.meetingDetails = _.cloneDeep(item);
        this.modal.show();
    }

    close(): void {
        this.meetingform.reset();
        this.modal.hide();
    }

    save(): void {
        if (!this.meetingFormValidation.isFormValid()) {
            return;
        }
        if (!this.meetingDetails.id)
        {
            this.meetingDetails.id = uuid.v4();
            this.meetingDetails.isNew = true;
        }
        this.submitted.emit(this.meetingDetails);
        this.close();

    }
}
