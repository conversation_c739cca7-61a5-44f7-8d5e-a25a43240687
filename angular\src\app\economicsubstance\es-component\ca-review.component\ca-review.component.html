<ngFormValidation #esReviewFormValidation></ngFormValidation>
<form #esessmentform="ngForm">
    <p-card class="p-card-properties_form">
        <p-header class="row-flex-space">
            <div>{{ l("CaAssessmentTitle") }}</div>
            <div
                [ngStyle]="getButtonBarStyle(printService.isPrintMode)"
                class="row-flex-justified"
            >
                <div *ngIf="esassessment.isEditable">
                    <button
                        id="save"
                        pButton
                        type="button"
                        icon="pi pi-save"
                        class="ui-button-rounded ui-button-danger margin-left"
                        iconPos="left"
                        tooltip="Save"
                        (click)="onUpdate()"
                    ></button>
                </div>
                <button
                    id="print"
                    pButton
                    type="button"
                    icon="pi pi-print"
                    class="
                        ui-button-rounded ui-button-secondary
                        margin-left
                        bigger-icons
                    "
                    iconPos="left"
                    tooltip="Print"
                    (click)="onPrint()"
                ></button>

                <button
                    id="close"
                    pButton
                    type="button"
                    class="
                        ui-button-rounded ui-button-secondary
                        margin-left
                        bigger-icons
                    "
                    icon="pi pi-times"
                    tooltip="Close"
                    (click)="onExit()"
                ></button>
            </div>
        </p-header>

        <div *ngIf="esassessment.isEditable">
            <div class="row-flex-justified-largefont">
                <div class="col-flex ten-percent-width">
                    <label>{{ l("CaAssessmentReason") }} </label>
                    <li *ngFor="let item of redFlagEventResult">
                        {{ item }}
                    </li>
                </div>

                <div class="col-flex fortyfive-percent-width">
                    <label style="font-size: 14px"
                        >{{ l("CaAssessmentStatus") }}
                        {{
                            esassessment.esReviewStatus
                                ? esassessment.esReviewStatus.name
                                : "Not started"
                        }}</label
                    >
                    <!--need 2 condition to show the approval and reject-->
                    <div *ngIf="esassessment.isProvisionalTreatment">
                        <!--need to check if the user already submit for waiting or not yet-->
                        <div
                            *ngIf="
                                !currentEconomicSubstance.approveRejectProvisionalTreatment
                            "
                        >
                            <div class="row-flex-justified-rows radio-input">
                                <div>
                                    <p-radioButton
                                        name="Provisional Treatment approval rejection"
                                        [value]="true"
                                        [(ngModel)]="
                                            esassessment.approveRejectProvisionalTreatment
                                        "
                                        id="approveProvisionalTreatmentT"
                                        required
                                        (onClick)="ApproveRejectPT($event)"
                                    >
                                    </p-radioButton>
                                    <span style="font-size: 14px"
                                        >Approve Provisional Treatment</span
                                    >
                                </div>
                                <div>
                                    <p-radioButton
                                        name="Provisional Treatment approval rejection"
                                        [value]="false"
                                        [(ngModel)]="
                                            esassessment.approveRejectProvisionalTreatment
                                        "
                                        required
                                        id="approveProvisionalTreatmentF"
                                        (onClick)="ApproveRejectPT($event)"
                                    >
                                    </p-radioButton>
                                    <span style="font-size: 14px"
                                        >Reject Provisional Treatment</span
                                    >
                                </div>
                            </div>
                        </div>
                        <div
                            *ngIf="
                                currentEconomicSubstance.approveRejectProvisionalTreatment
                            "
                        >
                            <label class="p-readonly-label"
                                >Approve Provisional Treatment:{{
                                    esassessment.approveRejectProvisionalTreatment !==
                                    null
                                        ? esassessment.approveRejectProvisionalTreatment
                                            ? "Yes"
                                            : "No"
                                        : ""
                                }}
                            </label>
                            <br />
                        </div>
                        <!--<div *ngIf="!currentEconomicSubstance.approveRejectProvisionalTreatment">-->
                        <div
                            *ngIf="
                                esassessment.approveRejectProvisionalTreatment ||
                                currentEconomicSubstance.approveRejectProvisionalTreatment
                            "
                        >
                            <div class="row-flex-justified-largefont">
                                <label
                                    >{{ l("CaEvdenceDueDate") }}
                                    <span class="required">*</span>
                                </label>
                            </div>
                            <div class="row-flex-justified">
                                <div>
                                    <span>
                                        <p-calendar
                                            class="p-calender-properties"
                                            [utc]="true"
                                            name="Evidenceduedate"
                                            dataType="string"
                                            appendTo="body"
                                            dateFormat="{{ dateFormatString }}"
                                            id="evidenceDueDate1"
                                            [yearNavigator]="true"
                                            [monthNavigator]="true"
                                            [yearRange]="getYearRange()"
                                            [showIcon]="true"
                                            [minDate]="minimumDate"
                                            required
                                            [(ngModel)]="
                                                esassessment.prTreatmentDueDateString
                                            "
                                            placeholder="dd/MM/yyyy"
                                        >
                                        </p-calendar>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="!esassessment.isProvisionalTreatment">
                        <div *ngIf="esassessment.assessmentLevel != 0">
                            <label style="font-size: 14px"
                                >{{ l("CaAssignTo") }} Level
                                {{ esassessment.assessmentLevel }}</label
                            >
                        </div>

                        <div *ngIf="esassessment.canAssignUsers">
                            <p-dropdown
                                [options]="esUserlist"
                                optionLabel="displayName"
                                [style]="{ width: '60%' }"
                                [(ngModel)]="esassessment.currentAssignedUser"
                                id="userlistid"
                                name="userlistid"
                                (onChange)="SetAssignedName($event)"
                            >
                            </p-dropdown>
                        </div>
                    </div>
                </div>
                <div class="col-flex fortyfive-percent-width">
                    <div class="row-flex-justified-largefont">
                        <label
                            >{{ l("CaAssessmentComments") }}
                            <span class="required">*</span>
                        </label>
                        <textarea
                            pInputTextarea
                            rows="3"
                            style="width: 390px"
                            required
                            emptyspace
                            [(ngModel)]="esassessment.comments"
                            id="commentsX1"
                            name="CaAssessmentComments"
                        ></textarea>
                        <button
                            id="assessmentHistory"
                            pButton
                            type="button"
                            icon="pi pi-undo"
                            tooltip="Assessment History"
                            (click)="onShowComments()"
                        ></button>
                    </div>
                </div>
            </div>
            <div class="row-flex-justified-largefont row-flex-add-margin">
                <div class="col-flex ten-percent-width"></div>
                <div class="col-flex fortyfive-percent-width">
                    <div
                        class="row-flex-justified-largefont"
                        style="width: 140px"
                    >
                        <p-checkbox
                            binary="false"
                            [ngModelOptions]="{ standalone: true }"
                            id="penaltyAppliedid"
                            [name]="penaltyAppliedname"
                            [(ngModel)]="esassessment.penaltyApplied"
                        ></p-checkbox>
                        <label for="binary">{{ l("Penalty Applied") }} </label>
                    </div>
                </div>
                <div class="col-flex fortyfive-percent-width">
                    <label>{{ l("CaAssessmentAttachments") }}</label>

                    <app-upload-files
                        [Documents]="currentComments.escaAssessmentDocuments"
                        [DocumentTypeName]="ESCAAssessmentDocumentsDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="false"
                        [displayFromCa]="true"
                        [ctspId]="ctspId"
                    >
                    </app-upload-files>
                </div>
            </div>
            <div class="row-flex-justified">
                <div class="col-flex ten-percent-width">
                    <div *ngIf="esassessment.canResubmit">
                        <button
                            id="requireResubmission"
                            pButton
                            type="button"
                            class="ui-button-rounded ui-button width-60 center"
                            style="width: 300px"
                            [disabled]="!esassessment.canBeReopened"
                            label="Require Resubmission"
                            (click)="onRequireResubmission()"
                        ></button>
                    </div>
                </div>
                <div class="col-flex fortyfive-percent-width">
                    <div *ngIf="esassessment.canChangeStatus">
                        <button
                            id="assessmentAction"
                            pButton
                            type="button"
                            class="ui-button-rounded ui-button width-60 center"
                            label="Assessment Action"
                            (click)="
                                changestatusmodal.show(
                                    esassessment,
                                    currentEconomicSubstance
                                )
                            "
                        ></button>
                    </div>
                </div>
                <div class="col-flex fortyfive-percent-width">
                    <div *ngIf="esassessment.canClose">
                        <button
                            id="close"
                            pButton
                            type="button"
                            class="ui-button-rounded ui-button width-60 center"
                            label="Close"
                            style="width: 300px"
                            (click)="onClose()"
                        ></button>
                    </div>
                </div>
            </div>

            <changestatusmodal
                #changestatusmodal
                [ctspId]="ctspId"
                (finishAssessment)="assessmentGetUpdated($event)"
            ></changestatusmodal>
        </div>

        <div *ngIf="esassessment && !esassessment.isEditable">
            <div class="row-flex-justified">
                <div class="col-flex ten-percent-width">
                    <label>{{ l("CaAssessmentReason") }} </label>
                    <li *ngFor="let item of redFlagEventResult">
                        {{ item }}
                    </li>
                </div>
                <div class="col-flex fortyfive-percent-width">
                    <label style="font-size: 14px"
                        >{{ l("CaAssessmentStatus") }}
                        {{
                            esassessment.esReviewStatus
                                ? esassessment.esReviewStatus.name
                                : "Not started"
                        }}</label
                    >
                    <div *ngIf="esassessment.assessmentLevel != 0">
                        <label style="font-size: 14px"
                            >{{ l("CaAssignTo") }} Level
                            {{ esassessment.assessmentLevel }}</label
                        >
                    </div>
                    <label style="font-size: 14px" for="binary"
                        >{{ l("Penalty Applied: ") }}
                        {{ esassessment.penaltyApplied ? "Yes" : "No" }}
                    </label>
                </div>

                <div class="col-flex fortyfive-percent-width">
                    <div class="col-flex">
                        <label>{{ l("CaAssessmentComments") }} </label>
                        <div class="row-flex-space">
                            <button
                                id="CaAssessmentComments"
                                pButton
                                type="button"
                                icon="pi pi-undo"
                                tooltip="Assessment History"
                                (click)="onShowComments()"
                            ></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </p-card>
</form>
<p-card class="p-card-properties_form">
    <app-main-economics-reo
        [corporateEntity]="corporateEntity"
        [currentEconomicSubstance]="currentEconomicSubstance"
        [informationRequired]="informationRequired"
        [informationRequiredsHistory]="informationRequiredsHistory"
        [esassessment]="esassessment"
        [displayHeader]="true"
        [displayFromCa]="true"
        [relevantActivityStatus]="relevantActivityStatus"
        [ctspId]="ctspId"
    >
    </app-main-economics-reo>
</p-card>

<reopenmodal #reopenmodal (reopenSubmit)="reopenSubmit($event)"></reopenmodal>
