import { AfterViewInit, Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppConsts } from '@shared/AppConsts';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EntityDtoOfInt64, UserListDto, UserServiceProxy } from '@shared/service-proxies/service-proxies';
import { SelectItem } from 'primeng/api';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Table } from 'primeng/components/table/table';
import { finalize } from 'rxjs/operators';
import { CreateOrEditUserComponent } from './create-or-edit-user.component';

@Component({
    templateUrl: './users.component.html',
    encapsulation: ViewEncapsulation.None,
    styleUrls: ['./users.component.less'],
    animations: [appModuleAnimation()]
})
export class UsersComponent extends AppComponentBase implements AfterViewInit {

    @ViewChild('createOrEditUser', { static: true }) createOrEditUser: CreateOrEditUserComponent;
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    //@ViewChild('paginator', { static: true }) paginator: Paginator;

    //Filters
    filterText = '';
    status = '';

    activeStatus = 'Active';
    inactiveStatus = 'Inactive';

    statuses: SelectItem[] = [
        { label: "Active", value: this.activeStatus },
        { label: "Inactive", value: this.inactiveStatus },
        { label: "All", value: "All" }
    ];

    constructor(
        injector: Injector,
        private _userServiceProxy: UserServiceProxy,
        private _activatedRoute: ActivatedRoute,
    ) {
        super(injector);
        this.filterText = this._activatedRoute.snapshot.queryParams['filterText'] || '';
    }

    ngOnInit() {
        this.status = this.statuses[0].value
        this.createOrEditUser.get();
    }

    ngAfterViewInit(): void {
        this.primengTableHelper.adjustScroll(this.dataTable);
    }

    ngOnDestroy() {
    }

    getUsers(event?: LazyLoadEvent) {
        var isActive = this.status == this.activeStatus;
        var isInactive = this.status == this.inactiveStatus;

        this.primengTableHelper.showLoadingIndicator();

        this._userServiceProxy.getUsers(
            this.filterText,
            isActive,
            isInactive,
            this.primengTableHelper.getSorting(this.dataTable),
            0,//this.primengTableHelper.getMaxResultCount(this.paginator, event),
            0 //this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }

    unlockUser(record: UserListDto): void {
        this._userServiceProxy.unlockUser(new EntityDtoOfInt64({ id: record.idInMaster })).subscribe(() => {
            this.reloadPage();
            this.notify.success(this.l('UnlockedTheUser', record.userName));
        });
    }

    reloadPage(): void {
        this.getUsers();
    }

    createUser(): void {
        this.createOrEditUser.get();
    }

    deleteUser(user: UserListDto): void {
        if (user.userName === AppConsts.userManagement.defaultAdminUserName) {
            this.message.warn(this.l('{0}UserCannotBeDeleted', AppConsts.userManagement.defaultAdminUserName));
            return;
        }

        this.message.confirm(
            this.l('UserRemoveWarningMessage', user.userName),
            this.l('AreYouSure'),
            (isConfirmed) => {
                if (isConfirmed) {
                    this._userServiceProxy.deleteUser(user.id, user.idInMaster, user.userName)
                        .subscribe(() => {
                            this.reloadPage();
                            this.notify.success(this.l('SuccessfullyRemoved'));
                        });
                }
            }
        );
    }

    onRowSelect(record: any) {
        this.createOrEditUser.get(record.data.id, record.data.idInMaster, record.data.userName);
    }
}
