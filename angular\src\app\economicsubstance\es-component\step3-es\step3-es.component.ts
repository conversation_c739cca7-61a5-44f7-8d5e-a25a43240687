import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, ValidationResultMainDto, EvidenceNonResidencyDocumentsDto, ValidationResultDto, CountryDto } from '@shared/service-proxies/service-proxies';
import { NgForm } from '@angular/forms';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';
import { EconomicDocumentsName} from '@app/economicsubstance/EconomicSubstance';
import { AppConsts } from '@shared/AppConsts';
import * as moment from 'moment';

@Component({
  selector: 'app-step3-es',
  templateUrl: './step3-es.component.html',
  styleUrls: ['./step3-es.component.css']
})
export class Step3EsComponent extends AppComponentBase implements OnInit
{
    @Input() public economicsubstance: EconomicSubstanceDeclarationDto;
    @Input() listOfCountry: CountryDto[];
    @Input() euListcountries: CountryDto[];
    @Input() readOnlyMode: boolean;
    @Input() importOnlyMode: boolean = false;
    @Input() displayFromCa: boolean;
    @Input() historyDisplay: boolean;
    @Input() ctspId: any;

    @ViewChild('#step3', { static: false }) form: NgForm;
    public showHideConditions: boolean[] = [];

    evidanceDoc: EvidenceNonResidencyDocumentsDto;
    selectUploadEvidence: boolean;
    NonResidencyDoc: any;
    TreatmentDoc: any;

    private isDeclaration2_0: boolean;

    constructor(injector: Injector, public _economicSubstanceService: EconomicSubstanceService ) { super(injector); }

    ngOnInit()
    {
        this.NonResidencyDoc = EconomicDocumentsName.EvidenceNonResidency;
        this.TreatmentDoc = EconomicDocumentsName.EvidenceProvisionalTreatment;
        this.isDeclaration2_0 = moment(this.economicsubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day'); 

        this._economicSubstanceService.validations$.subscribe((data: ValidationResultMainDto[]) => {
            if (data) {
                data.forEach((validation: ValidationResultMainDto) => {
                    if (validation.activityName == "Step3") {
                        let backendValidations: ValidationResultDto[] = validation.validationResultDto;
                        if (backendValidations != null) this.runNextValidations(backendValidations);
                    }
                });
            }
        });

        this.showHideConditions[0] = !this.IsNullorUndefined(this.economicsubstance.doesEntityMakeClaimOutisedBVI) && this.economicsubstance.doesEntityMakeClaimOutisedBVI;
        this.showHideConditions[1] = this.showHideConditions[0] && !this.isDeclaration2_0 && !this.IsNullorUndefined(this.economicsubstance.doesEntityHaveParentEntity) && this.economicsubstance.doesEntityHaveParentEntity;
        this.showHideConditions[2] = this.showHideConditions[0] && this.isDeclaration2_0;
        this.showHideConditions[3] = this.showHideConditions[0] && !this.isDeclaration2_0;
    }

    runNextValidations(validations: ValidationResultDto[]) {
        validations.forEach((validation: ValidationResultDto) => {
            if (!this._economicSubstanceService.step3Error[validation.fieldName]) {
                this._economicSubstanceService.step3Error[validation.fieldName] = true;
                this._economicSubstanceService.errorList.push(validation.validationString);
                
            }
        })
    }
    SetjurisdictionId(item: any)
    {
        this.economicsubstance.jurisdictionTaxResidentId = item.value.id;
    }

    SetParentjurisdictionId(item: any) {
        this.economicsubstance.parentJurisdictionId = item.value.id;
    }

    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }

    showUpload(index:any)
    {
        if (index === 1) return (!this.IsNullorUndefined(this.economicsubstance.isEvidenceNonResidenceOrTreatment) && this.economicsubstance.isEvidenceNonResidenceOrTreatment);
        if (index === 2) return (!this.IsNullorUndefined(this.economicsubstance.isEvidenceNonResidenceOrTreatment) && !this.economicsubstance.isEvidenceNonResidenceOrTreatment);
    }  
   

    public chkEntityClaimOutsideBVI(item: any) : void {
        this.showHideConditions[0] = !this.IsNullorUndefined(this.economicsubstance.doesEntityMakeClaimOutisedBVI) && this.economicsubstance.doesEntityMakeClaimOutisedBVI;        
        this.showHideConditions[1] = this.showHideConditions[0] && !this.isDeclaration2_0 && !this.IsNullorUndefined(this.economicsubstance.doesEntityHaveParentEntity) && this.economicsubstance.doesEntityHaveParentEntity;
        this.showHideConditions[2] = this.showHideConditions[0] && this.isDeclaration2_0;
        this.showHideConditions[3] = this.showHideConditions[0] && !this.isDeclaration2_0;
    }

    public chkEntityHaveParent(item: any) : void {        
        this.showHideConditions[1] = this.showHideConditions[0] && !this.isDeclaration2_0 && (!this.IsNullorUndefined(this.economicsubstance.doesEntityHaveParentEntity) && this.economicsubstance.doesEntityHaveParentEntity);        
    }
  
}
