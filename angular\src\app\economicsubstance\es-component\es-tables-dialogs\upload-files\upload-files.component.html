<div *ngIf="!readOnlyMode" class="row-flex-justified">
    <div *ngIf="!displayFromCa">
        <upload-document (itemUploaded)="AddDocument($event)"></upload-document>
        <div>
            <span style="font-size:12px">{{l('DeclarationNote')}}</span>
        </div>
    </div>

    <div *ngIf="displayFromCa">
        <upload-document  [CtspId]="ctspId" (itemUploaded)="AddDocument($event)"></upload-document>
        <div>
            <span style="font-size:12px">{{l('DeclarationNote')}}</span>
        </div>
    </div>
</div>
    <div class="row-flex-justified">
        <p-table [paginator]="false" [lazy]="true" scrollable="true" ScrollWidth="100%"
                 [value]="Documents"
                 *ngIf="Documents && Documents.length > 0">
            <ng-template pTemplate="header">
                <tr>
                    <th>File name</th>
                    <th>File type</th>
                    <th>Date/Time</th>
                    <th>Action</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-file>
                <tr *ngIf="!file.isDeleted">
                    <td>
                        {{file.fileName}}
                    </td>
                    <td>
                        {{file.contetntType}}
                    </td>
                    <td>
                        {{returnDate(file.updatedAt) | date: "dd/MM/yyyy HH:mm"}}
                    </td>
                    <td>
                        <button id="removeDocument" *ngIf="!readOnlyMode" pButton type="button" (click)="RemoveDoc(file)" icon="pi pi-trash" iconPos="left">
                            </button>
                        <button id="previewDocument" pButton type="button" (click)="PreviewDoc(file.documentsId)" icon="pi pi-search" class="margin-left" iconPos="left">
                        </button>
                    </td>
                </tr>
            </ng-template>
        </p-table>
        
    </div>
