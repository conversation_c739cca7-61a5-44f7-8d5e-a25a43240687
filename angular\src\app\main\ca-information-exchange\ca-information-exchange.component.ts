import {
    Component,
    Injector,
    ViewChild,
    ViewEncapsulation,
    AfterViewInit,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { AppComponentBase } from "@shared/common/app-component-base";
import {
    InformationExchangeServiceProxy,
    InformationExchangeStatus,
    InformationExchangeDto,
    InformationExchangeSummary,
    ExchangeReason,
    ProfileServiceProxy,
    CurrentUserProfileEditDto,
} from "@shared/service-proxies/service-proxies";
import { LazyLoadEvent } from "primeng/components/common/lazyloadevent";
import { Paginator } from "primeng/components/paginator/paginator";
import { Table } from "primeng/components/table/table";
import { finalize } from "rxjs/operators";
import {
    lastNumberOfYears,
    getRandomKey,
    getSearchCriteria,
} from "../viewmodel/utils";
import { SelectItem } from "primeng/api";
import { AllRequestStatusesInclAny } from "@app/shared/common/informationexchangestatus/informationexchange-status";
import { fork<PERSON>oin, Observable } from "rxjs";
import { downloadFile } from "@app/main/viewmodel/utils";
import { CaInformationHistoryComponent } from "../ca-information-exchange/ca-information-history/ca-information-history.component";
import { MessageService } from "@abp/message/message.service";
import { ValueSelectionService } from "@shared/services/ValueSelectionService";
import { CustomPaginatorComponent } from "@shared/common/components/custom-paginator/custom-paginator.component";

@Component({
    selector: "informationExchange",
    templateUrl: "./ca-information-exchange.component.html",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["./ca-information-exchange.component.css"],
})
export class CaInformationExchangeComponent
    extends AppComponentBase
    implements AfterViewInit {
    @ViewChild("dataTable", { static: true }) dataTable: Table;
    @ViewChild(CustomPaginatorComponent, { static: true }) paginationComponent: CustomPaginatorComponent;
    @ViewChild("informationHistoryModel", { static: true })
    caInformationHistoryComponent: CaInformationHistoryComponent;

    years: SelectItem[] = [];
    informationStatuses;
    selectedInformationStatus: any;
    selectedYear: any;
    summary: any;
    user: CurrentUserProfileEditDto = new CurrentUserProfileEditDto();
    complianceJunior: string = "Compliance Junior";
    complianceSenior: string = "Compliance Senior";
    admin: string = "Admin";
    paginator: Paginator;
    colorMap = [
        { code: InformationExchangeStatus.NotStarted, color: "red" },
        { code: InformationExchangeStatus.InformationExchange, color: "black" },
        {
            code: InformationExchangeStatus.WaitingForAppeal,
            color: "darkorange",
        },
        { code: InformationExchangeStatus.ReadyForExchange, color: "green" },
        { code: InformationExchangeStatus.NotRequired, color: "grey" },
        { code: InformationExchangeStatus.WaitingReview, color: "darkorange" },
    ];

    constructor(
        injector: Injector,
        private _route: ActivatedRoute,
        private _messageService: MessageService,
        private _informationExchangeServiceProxy: InformationExchangeServiceProxy,
        private router: Router,
        private _profileService: ProfileServiceProxy,
        private valueSelectionService: ValueSelectionService
    ) {
        super(injector);
        const currentYear = new Date().getFullYear();
        this.years = lastNumberOfYears(currentYear, 10).map((x) => ({
            label: x.toString(),
            value: x,
        }));
        this.informationStatuses = AllRequestStatusesInclAny;
        this.selectedInformationStatus = this.valueSelectionService.status && this.valueSelectionService.isPreserveSelectedValues ? this.valueSelectionService.status : this.informationStatuses[0];
        this._profileService
            .getCurrentUserProfileForEdit()
            .subscribe((result) => {
                this.user = result;
            });
        this.selectedYear = this.valueSelectionService.selectedYear && this.valueSelectionService.isPreserveSelectedValues ? this.valueSelectionService.selectedYear : this.years[0].value;
    }

    canGenerateXMLData() {
        return (
            this.user.role === this.admin ||
            this.user.role === this.complianceSenior
        );
    }

    canUpdateXMLData() {
        return (
            this.user.role === this.complianceJunior ||
            this.user.role === this.complianceSenior
        );
    }

    ngOnInit() {
        this.valueSelectionService.isPreserveSelectedValues = false;
        if (this.paginationComponent) {
            this.paginator = this.paginationComponent.getPaginator();
            this.paginator.first = this.valueSelectionService.currentPage && this.valueSelectionService.isPreserveSelectedValues ? (this.valueSelectionService.currentPage * this.primengTableHelper.defaultRecordsCountPerPage) as number : this.paginator.first;

         }

    }

    ngAfterViewInit(): void {
        this.primengTableHelper.adjustScroll(this.dataTable);
    }
    ngOnDestroy() {
        this.valueSelectionService.isPreserveSelectedValues = false;
    }

    refresh() {
        this.paginator.changePage(0);
        this.reloadPage();
    }

    getReviewStatusColor(item: InformationExchangeDto) {
        var color = this.colorMap.find(
            (x) => x.code == item.informationExchangeStatus
        );
        if (color != null) {
            return color.color;
        }

        return "black";
    }

    reloadPage(): void {
        this.paginator.changePage(this.paginator.getPage());
    }

    onRowSelect(record: any): void { }

    viewSubmission(item: any) {
        this.setValueSelectionServiceProperties();  
        const url = this.router.serializeUrl(this.router.createUrlTree([`app/economicsubstance/careview/${item.economicSubstanceId}/${item.cspNumber}`]));       
        window.open(url, '_blank');
    }

    viewXml(item: any, readonly?: any) {
        this.setValueSelectionServiceProperties();
        if (readonly) {
            let action = 3;
            const url = this.router.serializeUrl(this.router.createUrlTree([`app/main/cainfoexchage/${item.id}` + "/" + action]));            
            window.open(url, '_blank');
        } else {
            let action = 1;
            const url = this.router.serializeUrl(this.router.createUrlTree([`app/main/cainfoexchage/${item.id}` + "/" + action]));            
            window.open(url, '_blank');            
        }
    }

    viewHistory(item: any) {
        this.caInformationHistoryComponent.show(item);
    }

    GenerateFiles(reason: ExchangeReason, year: any) {
        this._informationExchangeServiceProxy
            .getnformationForByType(reason, year)
            .subscribe((result) => {
                if (result && result[0] /* if there is any item */) {
                    // need to download the result
                    result.forEach((file) => {
                        downloadFile(file.content, file.fileName);
                    });
                    this.getDataAndSummary();
                } else {
                    this._messageService.info("No data to export.");
                }
            });
    }

    GenerateCompliance() {
        this.GenerateFiles(ExchangeReason.NonCompliance, this.selectedYear);
    }

    GenerateIP() {
        this.GenerateFiles(ExchangeReason.HighRisk, this.selectedYear);
    }

    GenerateNonResidence() {
        this.GenerateFiles(ExchangeReason.NonResidence, this.selectedYear);
    }

    getDataAndSummary(event?: LazyLoadEvent) {
        this._informationExchangeServiceProxy
            .getInformationExchangeByFilter(
                this.selectedYear,
                this.selectedInformationStatus.value,
                this.primengTableHelper.getSorting(this.dataTable),
                this.primengTableHelper.getMaxResultCount(
                    this.paginator,
                    event
                ),
                this.primengTableHelper.getSkipCount(this.paginator, event)
            )
            .subscribe((result) => {
                this.primengTableHelper.totalRecordsCount = result.totalCount;

                this.primengTableHelper.records = result.items;
                this.primengTableHelper.hideLoadingIndicator();
            });

        this._informationExchangeServiceProxy
            .getSummaryByYear(this.selectedYear)
            .subscribe((result) => {
                this.summary = [result];
            });
    }
    setValueSelectionServiceProperties() {
        this.valueSelectionService.selectedYear = this.selectedYear;
        this.valueSelectionService.currentPage = this.paginator.getPage();
        this.valueSelectionService.status = this.selectedInformationStatus;
    }
}
