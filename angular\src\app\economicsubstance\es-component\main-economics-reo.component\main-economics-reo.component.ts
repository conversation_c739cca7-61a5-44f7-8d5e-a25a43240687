import { Component, Injector, OnInit,Input} from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ES_Step1 } from '@app/economicsubstance/EconomicSubstance';
import {
    CorporateEntityDto, EconomicSubstanceDeclarationDto,
    EconomicSubstanceInformationRequiredDto, RelevantActivity, ESCAAssessmentDto, EsEntityDetailDto
} from '@shared/service-proxies/service-proxies';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { RelaventActivityReviewStatus } from '@app/economicsubstance/EconomicSubstance';
import { EconomicSubstanceService, EsAction } from '@app/economicsubstance/economicsubstance.service';
import { AppConsts } from '@shared/AppConsts';
import * as _ from 'lodash';
import * as moment from 'moment';
@Component({
  selector: 'app-main-economics-reo',
  templateUrl: './main-economics-reo.component.html',
  styleUrls: ['./main-economics-reo.component.css']
})
export class MainEconomicsReoComponent extends AppComponentBase implements OnInit
{
 
    @Input() corporateEntity: CorporateEntityDto;
    @Input() currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    
    @Input() displayHeader: boolean;
    @Input() ctspId: any;
    @Input() displayFromCa: boolean;
    @Input() historyDisplay: boolean;
    @Input() importOnlyMode: boolean = false;

    @Input() informationRequired: EconomicSubstanceInformationRequiredDto;
   
    @Input() informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[];
    @Input() esassessment: ESCAAssessmentDto;
    @Input() relevantActivityStatus: RelaventActivityReviewStatus[];

    public esEntityDetail: EsEntityDetailDto;
    public showHideCondition: boolean;
    
    constructor(injector: Injector, private _sharedComponet: SharedComponent, public _economicSubstanceService: EconomicSubstanceService)
    {
        super(injector);
    }

    returnDate(obj) {
        if (obj) return new Date(obj.format('YYYY-MM-DD HH:mm:ss') + ' UTC');
    }

    ngOnInit()
    {
        this._economicSubstanceService.initializeEmptyError(); 
       
        this.showHideCondition = moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day');    

        this.esEntityDetail  = new EsEntityDetailDto();
        this.esEntityDetail = this.currentEconomicSubstance.esEntityDetails.find(x => x.economicSubstanceDeclarationId == this.currentEconomicSubstance.id) || this.currentEconomicSubstance.esEntityDetails.find(e => typeof e !== 'undefined');       
    }    

    CheckIfNonIsSelected(): Boolean
    {
        
        if (this.currentEconomicSubstance.relevantActivities) {
            let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);

            if (object.length > 0) return false;


            return true;
        }
        return true;
    }

    checkIfinformationRequired(): boolean
    {
        //if (typeof this.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined") return false;
        //let inforequired = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
        //if (inforequired === null) return false;
        return this.informationRequired !== null;
    }
}
