<div class="row-flex-justified-largefont">
    <label> 9{{headingSeq}}.{{sequenceNo}}a. {{l("ESCoreIncomeSelect")}} <span *ngIf="showHideCondition" class="required">*</span></label>   
    <div *ngIf="!readOnlyMode">
        <a href="javascript:;" (click)=" model.show(cigalookup,getSelectedCIGAItems())">Select CIGA(s)</a>
    </div>
</div>

<p-table #dataTableCIGA [paginator]="false" [lazy]="true" scrollable="true" crollHeight="400px"
         [value]="details">
    <ng-template pTemplate="header">
        <tr>
            <th>Code</th>
            <th>Text</th>
            <th *ngIf="!readOnlyMode"></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-activity>
        <tr *ngIf="!activity.isDeleted" >
            <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}">  {{activity.cigaActivity.activityOrder}}</label>
               
            </td>
            <td>
                <label [ngClass]="{'p-readonly-label':readOnlyMode}">  {{activity.cigaActivity.activityDetails}}</label>
               
            </td>
            <td *ngIf="!readOnlyMode">
                <button pButton type="button" icon="pi pi-trash" iconPos="left"
                        (click)="removesource(activity.cigaActivity.activityOrder)"></button>
            </td>
        </tr>
    </ng-template>
</p-table>


<app-ciga-detail-dialog #model (submitted)="updateSource($event)" ></app-ciga-detail-dialog>
