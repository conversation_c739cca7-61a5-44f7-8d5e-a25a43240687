import * as moment from 'moment';

export class DateTimeHelper {

    public static DATEFORMAT_DDMMYYYY: string = 'dd/MM/yyyy';
    public static DATETIMEFORMAT_DDMMYYYY_HHMM: string = 'dd/MM/yyyy HH:mm';

    //moment date format, different from momentpipe format
    public static DATETIMEFORMAT_MOMENT_DDMMYYYY_HHMMTT: string = 'DD/MM/YYYY hh:mm A';
    public static DATETIMEFORMAT_MOMENT_DDMMYYYY_HHMM: string = 'DD/MM/YYYY HH:mm';

    // moment and momentpipe has different format!!
    private static PARSE_DATEFORMAT_DDMMYYYY: string = 'DD/MM/YYYY';
    public static PARSE_DATETIMEFORMAT_DDMMYYYY_HHMM: string = 'DD/MM/YYYY hh:mm';
    public static PARSE_DATETIMEFORMAT_DDMMYYYY_HHMMA: string = 'DD/MM/YYYY hh:mm A';
    public static PARSE_DATETIMEFORMAT_DDMMYYYY_HMMA: string = 'DD/MM/YYYY h:mm A';

    public static getFormattedDate(date: any, includeTime: boolean = false, dateFormat: string = this.DATEFORMAT_DDMMYYYY): any {
        // it is required for case that date is string contains date string
        if (date && (typeof(date) == "string")) {
            if (date.indexOf('T') > 0) {
                // it should be iso date
                let datefromstring = new Date(Date.parse(date));
                if (datefromstring && (datefromstring instanceof Date)) {
                    date = datefromstring;
                }
            }
        }
        
        if (date && (typeof(date) == "string")) {
            try {
                let datefromstring = DateTimeHelper.parseDateTime(date);
                if (datefromstring && (datefromstring instanceof Date) && DateTimeHelper.getFormattedDate(datefromstring) == date.substr(0, 10)) {
                    date = datefromstring;
                }
            }
            catch (err)
            { }
        }

        if (!(date instanceof Date))
            return date;

        var d: Date = new Date(date);
        if (!d || !date)
            return date;

        if (dateFormat == this.DATEFORMAT_DDMMYYYY) {
            var day = d.getDate().toString();
            day = day.length == 1 ? '0' + day : day;
    
            var month = (d.getMonth() + 1).toString();
            month = month.length == 1 ? '0' + month : month;
    
            var time = d.toLocaleTimeString();
            
            return day + '/' + month + '/' + d.getFullYear() +
                (includeTime ? " " + time : "");
        }
        else if (dateFormat == this.DATETIMEFORMAT_MOMENT_DDMMYYYY_HHMMTT
                || dateFormat == this.PARSE_DATETIMEFORMAT_DDMMYYYY_HMMA
                || dateFormat == this.DATETIMEFORMAT_MOMENT_DDMMYYYY_HHMM) {
            return moment(d).format(dateFormat);
        }

        return date;
    }

    public static getJSDate(date: any): any {
        if (!date)
            return null;

        if (!DateTimeHelper.isValidDate(date)) {
            return date;
        }

        //convert dates like 'DD/MM/YYYY' to Date object otherwise will be 'Invalid Date'
        var dateparts = date.toString().split('/');
        if (dateparts && dateparts.length == 3 && date.toString().length == 10) {
            var d = new Date(dateparts[2], dateparts[1] - 1, dateparts[0]);
            return d;
        }

        var d = new Date(date);
        return d;
    }

    public static isValidDate(date: any): boolean {

        if (!date)
            return false;

        if (date instanceof Date)
            return true;

        if (moment(date, [moment.ISO_8601, this.DATEFORMAT_DDMMYYYY.toUpperCase()], true).isValid())
            return true;

        return false;
    }

    public static parseDateTime(value: string, format: string = this.PARSE_DATETIMEFORMAT_DDMMYYYY_HHMM): Date {
        try {
            let result = moment(value, format);
            if (result.isValid())
            {
                return result.toDate();
            }
        }
        catch (err) {
            console.log(err);
        }

        return null;
    }

    public static parseDate(value: string): Date {
        try {
            let result = moment(value, DateTimeHelper.PARSE_DATEFORMAT_DDMMYYYY);
            if (result.isValid())
            {
                return result.toDate();
            }
        }
        catch (err) {
            console.log(err);
        }

        return null;
    }

    public static parseUTCDateTimeTOLocal(value: string, format: string = this.PARSE_DATETIMEFORMAT_DDMMYYYY_HHMM): Date {
        try {
            let result = moment.utc(value, format).local();
            if (result.isValid())
            {
                return result.toDate();
            }
        }
        catch (err) {
            console.log(err);
        }

        return null;
    }

    public static parseUTCDateLocal(value: string): Date {
        try {
            let result = moment.utc(value, DateTimeHelper.PARSE_DATEFORMAT_DDMMYYYY).local();
            return result.toDate();
        }
        catch (err) {
            console.log(err);
        }

        return null;
    }

    public static convertToUtcTime(date: Date): Date {
        if (!date)
            return date;

        var d: Date = new Date(date);
        if (!d || !date)
            return date;

        return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
    }
}
