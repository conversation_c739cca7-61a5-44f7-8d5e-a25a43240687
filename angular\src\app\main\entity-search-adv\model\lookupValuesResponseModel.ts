import { LookupInfo } from './lookupInfo';

export interface IIsLookupValuesResponseModel {
    tenancyName: string;
}

export class LookupValuesResponseModel {
    public values: LookupInfo[];

    constructor(data?: IIsLookupValuesResponseModel) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data?: any) {
        if (data) {
            this.values = data.Data["values"];
        }
    }

    static fromJS(data: any): LookupValuesResponseModel {
        data = typeof data === 'object' ? data : {};
        let result = new LookupValuesResponseModel();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["values"] = this.values;
        return data; 
    }
}