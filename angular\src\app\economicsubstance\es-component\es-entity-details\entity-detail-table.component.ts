import { Component, OnInit, Input, Injector, EventEmitter, Output } from '@angular/core';
import { CountryDto, EsEntityAdditionalDetailDto, ParentEntityType } from '@shared/service-proxies/service-proxies';
import { AppComponentBase } from '@shared/common/app-component-base';
import * as _ from 'lodash';
import { AppConsts } from '@shared/AppConsts';


@Component({
  selector: 'app-entity-detail-table',
  templateUrl: './entity-detail-table.component.html',
  styleUrls: ['./entity-detail-table.component.css']
})
export class EntityDetailTableComponent extends AppComponentBase implements OnInit {

    @Input() public details: EsEntityAdditionalDetailDto[];   
    @Input() public readOnlyMode: boolean;
    @Input() public headingSeq: string;
    @Input() public sequenceNo: string;
    @Input() public importOnlyMode: boolean = false;
    @Input() public entityTypeName: ParentEntityType;
    @Input() public listOfCountry: CountryDto[];
    public entityType: string;
    public additionalDetails: EsEntityAdditionalDetailDto[] = [];  
    constructor(injector: Injector) { super(injector); }

    ngOnInit() {
        this.additionalDetails = this.details.filter(i => i.entityType == this.entityTypeName);
        this.entityType =  ParentEntityType[this.entityTypeName];
    }

    removesource(id: any): void {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            'Are you sure you want to delete it?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.handleDelete(id);
                }
            }
        );
    }


    handleDelete(id: any) {
        let index = this.details.findIndex(x => x.id == id);
        this.details[index].isDeleted = index != -1 ? true : this.details[index].isDeleted;
        this.additionalDetails = this.details.filter(i => i.entityType == this.entityTypeName);
    }

    //update table
    updateSource(sp: any) {
        let entitydetail = _.cloneDeep(sp);       
        let index = this.details.findIndex(x => x.id == sp.id);
        if (index == -1) this.details.push(entitydetail);
        else this.details[index] = entitydetail;        
        this.additionalDetails = this.details.filter(i => i.entityType == this.entityTypeName);       
    }

    returnyesno(item: any): string {
        if (item === undefined || item === null) return '';
        return item ? 'Yes' : 'No';

    }

}
