import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CaSearchResultComponent } from './ca-search-result.component';

describe('CaSearchResultComponent', () => {
  let component: CaSearchResultComponent;
  let fixture: ComponentFixture<CaSearchResultComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CaSearchResultComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CaSearchResultComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
