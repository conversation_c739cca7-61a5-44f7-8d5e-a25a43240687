import { AbpSessionService } from '@abp/session/abp-session.service';
import { Component, Injector, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { accountModuleAnimation } from '@shared/animations/routerTransition';
import { AppConsts } from '@shared/AppConsts';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ResourceServiceProxy, SessionServiceProxy, UpdateUserSignInTokenOutput } from '@shared/service-proxies/service-proxies';
import { RecaptchaComponent } from 'ng-recaptcha';
import { SelectItem } from 'primeng/api';
import { UrlHelper } from 'shared/helpers/UrlHelper';
import { downloadFile } from '../../app/main/viewmodel/utils';
import { ExternalLoginProvider, LoginService } from './login.service';

@Component({
    templateUrl: './login.component.html',
    animations: [accountModuleAnimation()],
    styleUrls: ['./login.component.less'],
    encapsulation: ViewEncapsulation.None
})
export class LoginComponent extends AppComponentBase implements OnInit {
    @ViewChild('recaptchaRef', { static: false }) recaptchaRef: RecaptchaComponent;

    submitting = false;
    isMultiTenancyEnabled: boolean = this.multiTenancy.isEnabled;
    recaptchaSiteKey: string = AppConsts.recaptchaSiteKey;
    captchaResponse?: string;

    constructor(
        injector: Injector,
        public loginService: LoginService,
        private _router: Router,
        private _route: ActivatedRoute,
        private _sessionService: AbpSessionService,
        private _sessionAppService: SessionServiceProxy,
        private resourceService: ResourceServiceProxy
    ) {
        super(injector);
    }

    get multiTenancySideIsTeanant(): boolean {
        return this._sessionService.tenantId > 0;
    }

    get isTenantSelfRegistrationAllowed(): boolean {
        return this.setting.getBoolean('App.TenantManagement.AllowSelfRegistration');
    }

    get isSelfRegistrationAllowed(): boolean {
        if (!this._sessionService.tenantId) {
            return false;
        }

        return this.setting.getBoolean('App.UserManagement.AllowSelfRegistration');
    }

    ngOnInit(): void {

        //If Ctsp Portal, redirect to boss login
        if (AppConsts.isCtspPortal)
            this._router.navigate(["../../bosslogin"], { relativeTo: this._route });

        if (this._sessionService.userId > 0 && UrlHelper.getReturnUrl() && UrlHelper.getSingleSignIn()) {
            this._sessionAppService.updateUserSignInToken()
                .subscribe((result: UpdateUserSignInTokenOutput) => {
                    const initialReturnUrl = UrlHelper.getReturnUrl();
                    const returnUrl = initialReturnUrl + (initialReturnUrl.indexOf('?') >= 0 ? '&' : '?') +
                        'accessToken=' + result.signInToken +
                        '&userId=' + result.encodedUserId +
                        '&tenantId=' + result.encodedTenantId;

                    location.href = returnUrl;
                });
        }

        let state = UrlHelper.getQueryParametersUsingHash().state;
        if (state && state.indexOf('openIdConnect') >= 0) {
            this.loginService.openIdConnectLoginCallback({});
        }

        this.loginService.authenticateModel.userNameOrEmailAddress = '';
        this.loginService.authenticateModel.password = '';
        this.loginService.authenticateModel.twoFactorProvider = this.twoFactorProviders[0].value;
        this.loginService.authenticateModel.twoFactorVerificationCode = '';
    }

    forgotPassword() {
        this._router.navigate(['account/forgot-password']);
    }

    loginHelp() {
        let path = this.l('ContactingSupportQuickGuideFileName');
        this.resourceService.getLoginHelp(path).subscribe(x => {
          downloadFile(x.content, path)
        })
    }

    login(): void {
        if (this.useCaptcha && !this.captchaResponse) {
            this.message.warn(this.l('CaptchaCanNotBeEmpty'));
            return;
        }

        //abp.ui.setBusy(undefined, '', 1);
        this.submitting = true;
        this.loginService.authenticate(
            () => {
                this.submitting = false;
                //abp.ui.clearBusy();
                if (this.recaptchaRef) {
                    this.recaptchaRef.reset();
                }
            },
            null,
            this.captchaResponse
        );
    }

    externalLogin(provider: ExternalLoginProvider) {
        this.loginService.externalAuthenticate(provider);
    }

    get useCaptcha(): boolean {
        return this.setting.getBoolean('App.UserManagement.UseCaptchaOnLogin');
    }

    captchaResolved(captchaResponse: string): void {
        this.captchaResponse = captchaResponse;
    }
}
