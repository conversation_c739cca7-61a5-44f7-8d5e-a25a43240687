<p-card class="p-card-properties_dashboard">
    <p-header class="row-flex-space">
        <p>{{l("Non-Resident Reports")}}</p>
        <div class="width-20">
            <p-dropdown [options]="viewModel.weeks" [(ngModel)]="viewModel.selectedWeek" optionLabel="name">
            </p-dropdown>
        </div>
    </p-header>
    <p-table [value]="viewModel.report" class="margin-top">
        <ng-template pTemplate="header">
            <tr>
                <th>{{"Period Start" | localize}}</th>
                <th>{{"Period End" | localize}}</th>
                <th>{{"Jurisdication" | localize}}</th>
                <th>{{"# of ES Returns" | localize}}</th>
                <th>{{"PDF Report" | localize}}</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr>
                <td>{{item.startDate | date: 'yyyy-MM-dd'}}</td>
                <td>{{viewModel.addDays(item.endDate, -1) | date: 'yyyy-MM-dd'}}</td>
                <td>{{item.countryName}}</td>
                <td> <a [href]="getSearchResultLink(item)" class="dashboardLink">
                        {{item.count}}
                    </a></td>
                <td>
                    <button pButton type="button" (click)="viewModel.downloadPdf(item)" icon="pi pi-download"
                        iconPos="left"></button>
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-card>