import { Component, Injector, Input, OnInit, HostListener } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { Location } from '@angular/common';
import {
    WrokflowServiceProxy, EconomicSubstanceInformationRequiredDto,
    CorporateEntityDto, EconomicSubstanceDeclarationDto, ESCAAssessmentDto
} from '@shared/service-proxies/service-proxies';

import { DialogService } from 'primeng/api';
import { DisplayCaReviewCommentComponent } from '@app/economicsubstance/es-component/display-ca-review-comment.component/display-ca-review-comment.component';
import { PrintService } from '@shared/services/print.service';
import { RelaventActivityReviewStatus } from '@app/economicsubstance/EconomicSubstance';
import { ValueSelectionService } from '@shared/services/ValueSelectionService';


@Component({
  selector: 'ca-re-review',
  templateUrl: './ca-re-review.component.html',
  styleUrls: ['./ca-re-review.component.css']
})
export class CaReReviewComponent extends AppComponentBase implements OnInit
{
    @Input() corporateEntity: CorporateEntityDto;
    @Input() currentEconomicSubstance: EconomicSubstanceDeclarationDto;

    @Input() informationRequired: EconomicSubstanceInformationRequiredDto;
   @Input() informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[];
    @Input() esassessment: ESCAAssessmentDto;
    @Input() relevantActivityStatus: RelaventActivityReviewStatus[];
    @Input() ctspId: any;

    @Input() redFlagEventResult: string[];

    constructor(  injector: Injector,
        private location: Location,
        private _wrokflowServiceProxy: WrokflowServiceProxy,
        private _dialogService: DialogService,
        public printService: PrintService,
        private valueSelectionService : ValueSelectionService)
    {
        super(injector);
        
       

    }

      

    ngOnInit()
    {


        // need to pass it to the main component for assessment status for each relavent activity
   
  }
    onExit() {
        this.valueSelectionService.isPreserveSelectedValues=true;
        this.location.back();
    }

  

    onPrint() {
        this.printService.print();
    }

    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'display': 'none',
            };
        }

        return {};
    }



    onShowComments() {
        this._wrokflowServiceProxy.getCaReviewComments(this.ctspId, this.esassessment.economicSubstanceDeclarationId).subscribe(data => {

            this._dialogService.open(DisplayCaReviewCommentComponent,
                {
                    data: { escaAssessmentCommentsDto: data, ctspId: this.ctspId },
                    header: this.l("Assessment History"),
                    width: '80%',
                    style: { 'overflow': 'auto' }
                });
        });
    }



   
}
