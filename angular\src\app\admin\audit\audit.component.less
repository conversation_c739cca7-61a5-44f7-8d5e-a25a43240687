.ess-audit {
}

.ess-audit-search,
.ess-audit-grid {
    padding: 1em;
}

.ess-audit-search {
    button + button {
        margin-left: 10px;
    }

    > div {
        width: 650px;
    }

    .ess-audit-form-control {
        width: 315px;

        .ess-audit-form-error {
            input {
                border: 2px solid #A20F0F;
                border-radius: 0px;
            }
        }

        .ess-audit-form-list-error {
            input {
                border: 2px solid #A20F0F;
                border-radius: 0px;
            }
        }

        span.ui-calendar {
            width: 100%;
        }

        input {
            width: 100%;
        }
    }
}

.col-flex {
    > .ess-audit-form-control + .ess-audit-form-control {
        margin-top: 10px;
    }
}

.ess-audit-grid {
    background-color: #f3fafd;

    .ess-audit-grid-body {
        height: calc(100vh - 150px);
        overflow-y: auto;
    }

    .ess-title {
        margin-bottom: 3px;
    }

    .ess-audit-pager {
        margin-top: 5px;

        .ui-paginator {
            background-color: transparent;

            .ui-paginator-page.ui-state-active {
                color: white !important;
            }
        }
    }
}
