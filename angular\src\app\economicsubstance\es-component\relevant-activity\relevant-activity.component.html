<div class="col-flex">
    <p-card [ngClass]="{'p-card-properties_form':!readOnlyMode,'p-card-properties_form_readonly':readOnlyMode}">
        <p-header>
            9{{headingSeq}}. {{l('ESrelaventActicvityTitle')}} {{economicsubstance.relevantActivities[index].relevantActivityName}}
            
                <p-dropdown id="ESrelaventActicvityTitle" 
                            *ngIf="displayFromCa"
                            [options]="relevantActivityStatus"
                            optionLabel="label"
                            class="width-10"
                            [disabled]="!esassessment.isEditable || !esassessment.canChangeStatus"
                            [(ngModel)]="selectedActivityStatus"
                            (onChange)="ChangeRAStatus($event)">
                </p-dropdown>
            
        </p-header>       

        <!--Turnover-->
        <div *ngIf="fieldDetails[0].enablment">
            <div class="row-flex-justified ">
                <label class="ac-header" *ngIf="!ShowHideResult(15)">9{{headingSeq}}.{{fieldDetails[0].number}}. {{l('TurnoverTitle')}}</label>
                <label class="ac-header" *ngIf="ShowHideResult(15)">9{{headingSeq}}.{{fieldDetails[0].number}}. {{l('GrossIncome')}}</label>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label *ngIf="!ShowHideResult(15)">9{{headingSeq}}.{{fieldDetails[0].number}}a. {{l('TurnoverA')}} <span class="required">*</span></label>
                    <label *ngIf="ShowHideResult(15)">9{{headingSeq}}.{{fieldDetails[0].number}}a. {{l('GrossIncomeA')}} <span class="required">*</span></label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                     [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalTurnover'] }">
                    <p-dropdown [options]="currency"
                                optionLabel="currencyCode"
                                [(ngModel)]="economicsubstance.currency"
                                (onChange)="SetCurrencyId($event)"
                                class="width-95"
                                id="{{field[4][0]}}"
                                name="{{field[4][2]}}">
                    </p-dropdown>

                    <div *ngIf="!importOnlyMode">
                        <p-spinner [(ngModel)]="economicsubstance.relevantActivities[index].totalTurnover"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[5][0]}}" name="{{field[5][2]}}"></p-spinner>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control"  [(ngModel)]="economicsubstance.relevantActivities[index].totalTurnoverString"
                               id="{{field[5][0]}}" name="{{field[5][2]}}">
                    </div>
                </div>
                <div *ngIf="readOnlyMode" class="margin-left"
                     [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                  'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                    <div class="col-flex-width20 ">
                        <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                    </div>
                    <div *ngIf="!importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalTurnover}}</label>
                        </div>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalTurnoverString}}</label>
                        </div>
                    </div>

                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                        <label class="p-readonly-label">USD</label>
                    </div>
                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                        <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].totalTurnover)}}</label>
                    </div>
                </div>

            </div> 
            <div *ngIf="ShowHideResult(16)" class="row-flex-justified-rows">  
                <div class="row-flex-justified-largefont margin-left">
                    <label *ngIf="!ShowHideResult(22)">9{{headingSeq}}.{{fieldDetails[0].number}}b. {{l('GrossIncomeType')}} <span class="required">*</span></label>
                    <label *ngIf="ShowHideResult(22)">9{{headingSeq}}.{{fieldDetails[0].number}}b. {{l('GrossIncomeTypeIP')}} <span class="required">*</span></label>
                </div>              
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                    [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeType'] }"> 
                        <input pInputText type="text" class="form-control"
                               [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeType"
                               id="txtGrossIncomeTypeid{{headingSeq}}" name="txtGrossIncomeTypename{{headingSeq}}">                       
                </div>
                <div *ngIf="readOnlyMode" class="margin-left">
                    <label class="p-readonly-label word-wrapping">{{economicsubstance.relevantActivities[index].grossIncomeType }} </label>
                </div>
            </div> 
            <div *ngIf="ShowHideResult(19)" class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label>9{{headingSeq}}.{{fieldDetails[0].number}}c. {{l('TotalAssetsValue')}} <span class="required">*</span> </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                        [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalAssetsValue'] }">  
                    <textarea maxlength="255" [(ngModel)]="economicsubstance.relevantActivities[index].totalAssetsValue" class="width-95" rows="3"
                        id="totalAssetsValueid{{headingSeq}}" name="totalAssetsValuename{{headingSeq}}">
                    </textarea> 
                </div>
                <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">  
                    <textarea pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].totalAssetsValue" class="textarea-readonly-background" rows="3"
                        id="totalAssetsValueid{{headingSeq}}" name="totalAssetsValuename{{headingSeq}}"></textarea>                     
                </div>
            </div>
            <div *ngIf="ShowHideResult(19)" class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label>9{{headingSeq}}.{{fieldDetails[0].number}}d. {{l('NetAssetsValue')}} <span class="required">*</span> </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                        [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['NetAssetsValue'] }">
                    <div *ngIf="!importOnlyMode">
                        <p-spinner [(ngModel)]="economicsubstance.relevantActivities[index].netAssetsValue" (keypress)="maskDecimal($event, 15, 2, true)" [min]="-999999999999999.99" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="."
                            id="netAssetsValueid{{headingSeq}}" name="netAssetsValuename{{headingSeq}}"></p-spinner>                   
                    </div>
                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].netAssetsValueString"
                            id="txtNetAssetsValueid{{headingSeq}}" name="txtNetAssetsValuename{{headingSeq}}">
                    </div>
                </div>
                <div *ngIf="readOnlyMode" class="margin-left">   
                    <div *ngIf="!importOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].netAssetsValue }} </label>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].netAssetsValueString }} </label>
                    </div>         
                </div>
            </div>
        </div>

        <!--Direction and Mangement-->
        <div *ngIf="fieldDetails[1].enablment">
            <div class="row-flex-justified ">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[1].number}}. {{l('DirectionManagementTitle')}}</label>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <div>9{{headingSeq}}.{{fieldDetails[1].number}}a. {{l('DirectionManagementA')}}<span *ngIf="ShowHideResult(15)" class="required">*</span></div>
                </div>
                <div class="row-flex-justified margin-left">
                    <div *ngIf="!readOnlyMode" class="radio-input"
                         [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IsActivityDirectedInBVI'] }">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].isActivityDirectedInBVI" id="{{field[0][0]}}" name="{{field[0][2]}}">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].isActivityDirectedInBVI" id="{{field[0][1]}}" name="{{field[0][2]}}">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].isActivityDirectedInBVI | yesNo}} </label>
                    </div>
                </div>
            </div>
            
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified margin-left"
                         [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['PersonDetail'] }">
                        <app-person-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[1].number" [personDetails]="economicsubstance.relevantActivities[index].managementDetails" [readOnlyMode]="readOnlyMode"></app-person-detail-table>
                    </div>
                </div>
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[1].number}}c. {{l('DirectionManagementC')}}<span *ngIf="ShowHideResult(15)" class="required">*</span></label>
                    </div>
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['NoofConductedMeeting'] }">

                        <div *ngIf="!readOnlyMode">
                            <p-spinner *ngIf="!importOnlyMode" size="25" maxlength="3" [(ngModel)]="economicsubstance.relevantActivities[index].noofConductedMeeting"
                                       id="{{field[1][0]}}" name="{{field[1][2]}}"></p-spinner>
                           <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].noofConductedMeetingString"
                                       id="{{field[1][0]}}" name="{{field[1][2]}}">
                        </div>


                        <div *ngIf="readOnlyMode">
                            <div *ngIf="!importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofConductedMeeting }} </label>
                            </div>
                            <div *ngIf="importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofConductedMeetingString }} </label>
                            </div>
                        </div>


                    </div>
                </div>


                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[1].number}}d. {{l('DirectionManagementD')}} <span *ngIf="ShowHideResult(15)" class="required">*</span></label>
                    </div>
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['NoofMeetingHeldInBVI'] }">
                        <div *ngIf="!readOnlyMode">
                            <p-spinner *ngIf="!importOnlyMode" size="25" maxlength="3" [(ngModel)]="economicsubstance.relevantActivities[index].noofMeetingHeldInBVI"
                                       name="{{field[2][2]}}" id="{{field[2][0]}}"></p-spinner>

                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].noofMeetingHeldInBVIString"
                                    name="{{field[2][2]}}" id="{{field[2][0]}}">

                        </div>
                        <div *ngIf="readOnlyMode">
                            <div *ngIf="!importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofMeetingHeldInBVI }} </label>
                            </div>
                            <div *ngIf="importOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofMeetingHeldInBVIString }} </label>
                            </div>
                        </div>
                    
                    </div>
                </div>
                <div *ngIf="ShowHideResult(1)">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['MeetingTable'] }">
                            <app-meeting-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[1].number"
                                                      [details]="economicsubstance.relevantActivities[index].attendMeetingDetails"
                                                      [readOnlyMode]="readOnlyMode"
                                                      [importOnlyMode]="importOnlyMode"
                                                      [showHideCondition]="ShowHideResult(15)"
                                                      ></app-meeting-detail-table>
                        </div>
                    </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[1].number}}f. {{l('DirectionManagementF')}} <span *ngIf="ShowHideResult(15)" class="required">*</span></div>
                        </div>
                        <div class="row-flex-justified margin-left">
                            <div *ngIf="!readOnlyMode" class="radio-input" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IsMeetingMinutesInBVI'] }">
                                <div>
                                    <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].isMeetingMinutesInBVI" id="{{field[3][0]}}" name="{{field[3][3]}}">
                                    </p-radioButton>
                                    <span>YES</span>
                                </div>
                                <div>
                                    <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].isMeetingMinutesInBVI" id="{{field[3][1]}}" name="{{field[3][3]}}">
                                    </p-radioButton>
                                    <span>NO</span>
                                </div>
                            </div>
                            <div *ngIf="readOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].isMeetingMinutesInBVI | yesNo }} </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(15)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[1].number}}g. {{l('QuorumBoardMeeting')}} <span class="required">*</span></label>
                    </div>
                    <div class="row-flex-justified margin-left">
                        <div *ngIf="!readOnlyMode" 
                                [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['NoofQuorumBoardMeeting'] }" >
                            <p-spinner *ngIf="!importOnlyMode" (keypress)="maskDecimal($event, 3, 0)" size="25" maxlength="3" [(ngModel)]="economicsubstance.relevantActivities[index].noofQuorumBoardMeeting"
                                id="noofQuorumBoardMeetingid{{headingSeq}}" name="noofQuorumBoardMeetingname{{headingSeq}}"></p-spinner>

                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].noofQuorumBoardMeetingString"
                                id="txtNoofQuorumBoardMeetingid{{headingSeq}}" name="txtNoofQuorumBoardMeetingname{{headingSeq}}">

                        </div>

                        <div *ngIf="importOnlyMode">                           
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofQuorumBoardMeetingString }} </label>
                        </div>
                        
                        <div *ngIf="readOnlyMode">                           
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].noofQuorumBoardMeeting }} </label>                            
                        </div>                    
                    </div>
                </div>
                <div *ngIf="ShowHideResult(15)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <div>9{{headingSeq}}.{{fieldDetails[1].number}}h. {{l('QuorumBoardMeetingInBVI')}} <span class="required">*</span> </div>
                    </div>
                    <div class="row-flex-justified margin-left">
                        <div *ngIf="!readOnlyMode" class="radio-input"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IsQuorumBoardMeetingInBVI'] }" >
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].isQuorumBoardMeetingInBVI" id="isQuorumBoardMeetingInBVIT{{headingSeq}}" name="isQuorumBoardMeetingInBVI{{headingSeq}}">
                                </p-radioButton>
                                <span>YES</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].isQuorumBoardMeetingInBVI" id="isQuorumBoardMeetingInBVIF{{headingSeq}}" name="isQuorumBoardMeetingInBVI{{headingSeq}}">
                                </p-radioButton>
                                <span>NO</span>
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].isQuorumBoardMeetingInBVI | yesNo }} </label>
                        </div>
                    </div>
                </div>            
        </div>
        
        <!--Expenditure-->
        <div *ngIf="fieldDetails[2].enablment">
            <div class="row-flex-justified ">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[2].number}}. {{l('ExpenditureTitle')}}</label>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left ">
                    <label>9{{headingSeq}}.{{fieldDetails[2].number}}a. {{l('ExpenditureA')}}  <span class="required">*</span> </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalExpediture'] }">
                    <p-dropdown [options]="currency" optionLabel="currencyCode"
                                [(ngModel)]="economicsubstance.currency"
                                (onChange)="SetCurrencyId($event)"
                                class="width-95" id="{{field[6][0]}}" name="{{field[6][2]}}">
                    </p-dropdown>
                

                    <div *ngIf="!importOnlyMode">
                        <p-spinner [(ngModel)]="economicsubstance.relevantActivities[index].totalExpediture"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[7][0]}}" name="{{field[7][2]}}"></p-spinner>

                    </div>
                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalExpeditureString"
                               id="{{field[7][0]}}" name="{{field[7][2]}}">
                    </div>

                </div>
                
                <div *ngIf="readOnlyMode"
                     class="margin-left"
                     [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                 'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                    <div class="col-flex-width20 ">
                        <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                    </div>
                    <div *ngIf="!importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpediture}}</label>
                        </div>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpeditureString}}</label>
                        </div>
                    </div>
                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                        <label class="p-readonly-label">USD</label>
                    </div>
                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                        <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].totalExpediture)}}</label>
                    </div>
                </div>


            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left ">
                    <label>9{{headingSeq}}.{{fieldDetails[2].number}}b. {{l('ExpenditureB')}}  <span class="required">*</span> </label>
                </div>
                <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalExpeditureInBVI'] }">
                    <p-dropdown [options]="currency" optionLabel="currencyCode"
                                (onChange)="SetCurrencyId($event)"
                                [(ngModel)]="economicsubstance.currency"
                                class="width-95" id="{{field[8][0]}}" name="{{field[8][2]}}">
                    </p-dropdown>

                    <div *ngIf="!importOnlyMode">
                        <p-spinner [(ngModel)]="economicsubstance.relevantActivities[index].totalExpeditureInBVI"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[9][0]}}" name="{{field[9][2]}}"></p-spinner>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <input pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalExpeditureInBVIString"
                               id="{{field[9][0]}}" name="{{field[9][2]}}">
                    </div>

                </div>
                <div *ngIf="readOnlyMode" class="margin-left"
                     [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                  'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                    <div class="col-flex-width20 ">
                        <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                    </div>
                    <div *ngIf="!importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpeditureInBVI}}</label>
                        </div>
                    </div>
                    <div *ngIf="importOnlyMode">
                        <div class="col-flex-width70 ">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpeditureInBVIString}}</label>
                        </div>
                    </div>

                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                        <label class="p-readonly-label">USD</label>
                    </div>
                    <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                        <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].totalExpeditureInBVI)}}</label>
                    </div>
                </div>


            </div>
        </div>

        <!--Pure Equity Holding Entity-->
        <div *ngIf="fieldDetails[5].enablment">
            <div class="row-flex-justified">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[5].number}}. {{l('EquityHoldingEntityTitle')}}</label>
            </div> 
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <div>9{{headingSeq}}.{{fieldDetails[5].number}}a. {{l('EquityHoldingEntityB')}}<span *ngIf="ShowHideResult(15)" class="required">*</span></div>
                </div>
                <div class="row-flex-justified margin-left" [ngClass]="{ 'input-warnning-box': !ShowHideResult(15) && this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesEntityManageEquity'],
                                                                         'input-error-box': ShowHideResult(15) && this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesEntityManageEquity'] }" >
                    <div *ngIf="!readOnlyMode" class="radio-input">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityManageEquity" id="{{field[28][0]}}" name="{{field[28][2]}}">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityManageEquity" id="{{field[28][1]}}" name="{{field[28][2]}}">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].doesEntityManageEquity | yesNo }} </label>
                    </div>
                </div>
            </div>
            <div class="row-flex-justified-rows" *ngIf="!ShowHideResult(21)">
                <div class="row-flex-justified-largefont margin-left">
                    <div>9{{headingSeq}}.{{fieldDetails[5].number}}b. {{l('EquityHoldingEntityA')}}<span *ngIf="ShowHideResult(15)" class="required">*</span></div>
                </div>
                <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesEntityComplyItsStatutoryObligations'] }">
                    <div *ngIf="!readOnlyMode" class="radio-input">
                        <div>
                            <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityComplyItsStatutoryObligations" id="{{field[27][0]}}" name="{{field[27][2]}}">
                            </p-radioButton>
                            <span>YES</span>
                        </div>
                        <div>
                            <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityComplyItsStatutoryObligations" id="{{field[27][1]}}" name="{{field[27][2]}}">
                            </p-radioButton>
                            <span>NO</span>
                        </div>
                    </div>
                    <div *ngIf="readOnlyMode">
                        <label class="p-readonly-label"> {{economicsubstance.relevantActivities[index].doesEntityComplyItsStatutoryObligations | yesNo }} </label>
                    </div>
                </div>                     
            </div>
       </div>

        <!--Employees-->
        <div *ngIf="fieldDetails[3].enablment && ShowHideResult(20)">
            <div class="row-flex-justified ">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[3].number}}. {{l('EmployeeTitle')}}</label>
            </div>
            <div *ngIf="ShowHideResult(15)" class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label>9{{headingSeq}}.{{fieldDetails[3].number}}. {{l('TotalNoCorporateLegalEmployee')}} <span class="required">*</span> </label>
                </div>
                <div class="row-flex-justified-width margin-left">                 

                    <div *ngIf="!readOnlyMode"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalNoCorporateLegalEmployee'] }">
                        <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].totalNoCorporateLegalEmployee"  (keypress)="maskDecimal($event, 15, 3)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="."
                                id="totalNoCorporateLegalEmployeeid{{headingSeq}}" name="totalNoCorporateLegalEmployeename{{headingSeq}}"></p-spinner>

                        <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalNoCorporateLegalEmployeeString"
                                id="txtTotalNoCorporateLegalEmployeeid{{headingSeq}}" name="txtTotalNoCorporateLegalEmployeename{{headingSeq}}">
                    
                    </div>

                    <div *ngIf="importOnlyMode">
                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalNoCorporateLegalEmployeeString }} </label>
                    </div>
                    <div *ngIf="readOnlyMode">                       
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalNoCorporateLegalEmployee }} </label>                        
                    </div>
                </div>
            </div>

            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label>9{{headingSeq}}.{{fieldDetails[3].number}}a. {{l('EmployeeA')}} <span class="required">*</span> <label tooltip="{{'EmployeeATooltip' | localize}}"><i class="pi pi-info-circle" style="font-size:24px;"></i></label></label>
                </div>
                <div class="row-flex-justified-width margin-left"
                     [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalNoFullTimeEmployee'] }">
                   


                    <div *ngIf="!readOnlyMode">
                        <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].totalNoFullTimeEmployee"  (keypress)="maskDecimal($event, 15, 3)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[10][0]}}" name="{{field[10][2]}}"></p-spinner>

                        <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalNoFullTimeEmployeeString"
                               id="{{field[10][0]}}" name="{{field[10][2]}}">

                    </div>

                    <div *ngIf="readOnlyMode">
                        <div *ngIf="!importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalNoFullTimeEmployee }} </label>
                        </div>
                        <div *ngIf="importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalNoFullTimeEmployeeString }} </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-flex-justified-rows">
                <div class="row-flex-justified-largefont margin-left">
                    <label>9{{headingSeq}}.{{fieldDetails[3].number}}b. {{l('EmployeeB')}}<span class="required">*</span> <label tooltip="{{'EmployeeATooltip' | localize}}"><u><i class="pi pi-info-circle" style="font-size:24px;"></i></u></label></label>
                </div>
                <div class="row-flex-justified-width margin-left"
                     [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalFullTimeEmployeeInBVI'] }">
                    <div *ngIf="!readOnlyMode">
                        <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].totalFullTimeEmployeeInBVI"  (keypress)="maskDecimal($event, 15, 3)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[11][0]}}" name="{{field[11][2]}}"></p-spinner>

                        <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalFullTimeEmployeeInBVIString"
                               id="{{field[10][0]}}" name="{{field[10][2]}}">

                    </div>
                    <div *ngIf="readOnlyMode">
                        <div *ngIf="!importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalFullTimeEmployeeInBVI }} </label>
                        </div>
                        <div *ngIf="importOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalFullTimeEmployeeInBVIString }} </label>
                        </div>
                    </div>
                </div>

                </div>
            <div *ngIf="ShowHideResult(2)">
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['EmployeeDetail'] }">
                        <app-qualification-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[3].number"
                                                        [details]="economicsubstance.relevantActivities[index].employeeQualificationDetails"
                                                        [readOnlyMode]="readOnlyMode"
                                                        [importOnlyMode]="importOnlyMode"
                                                        [showHideCondition]="ShowHideResult(15)"
                                                        ></app-qualification-detail-table>
                    </div>
                </div>
            </div>
        </div>

        <!--Premises-->
        <div *ngIf="fieldDetails[4].enablment && ShowHideResult(20)">
            <div class="row-flex-justified ">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[4].number}}. {{l('PremisesTitle')}}</label>
            </div>
            <div class="row-flex-justified-rows">
                <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['PremisesTable'] }">
                    <app-address-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[4].number"
                                              [details]="economicsubstance.relevantActivities[index].premisesAddress"
                                              [corporateEntity]="corporateEntity"
                                              [country]="country" [readOnlyMode]="readOnlyMode">
                    </app-address-detail-table>
                </div>
            </div>
        </div>

        <!--High Risk-->
        <div *ngIf="fieldDetails[6].enablment">
            <div *ngIf="!ShowHideResult(10)">
                <div class="row-flex-justified ">
                    <label class="ac-header">9{{headingSeq}}.{{fieldDetails[6].number}}. {{l('HighRiskIpTitle')}}</label>
                </div>
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <div>9{{headingSeq}}.{{fieldDetails[6].number}}a. {{l('HighRiskIpA')}} <span class="required">*</span> </div>
                    </div>
                    <div class="row-flex-justified margin-left">
                        <div *ngIf="!readOnlyMode" class="radio-input"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IsLegalEntityHighRisk'] }">
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].isLegalEntityHighRisk" id="{{field[12][0]}}" name="{{field[12][2]}}">
                                </p-radioButton>
                                <span>YES</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].isLegalEntityHighRisk" id="{{field[12][1]}}" name="{{field[12][2]}}">
                                </p-radioButton>
                                <span>NO</span>
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].isLegalEntityHighRisk | yesNo }} </label>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(8)">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}b. {{l('HighRiskIpB')}} <label tooltip="To rebut the presumption a legal entity must be satisfied not only at the point in time at which the legal entity seeks to rebut the presumption but also during any historic periods when the legal entity was carrying on the IP business in question."><u>{{l("HighRiskIpB1")}}</u> </label> {{l('HighRiskIpB2')}} <span class="required">*</span></div>
                        </div>
                        <div class="row-flex-justified margin-left">
                            <div *ngIf="!readOnlyMode" class="radio-input"
                                 [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesEntityProvideEvidence'] }">
                                <div>
                                    <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityProvideEvidence" id="{{field[13][0]}}" name="{{field[13][2]}}">
                                    </p-radioButton>
                                    <span>YES</span>
                                </div>
                                <div>
                                    <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityProvideEvidence" id="{{field[13][1]}}" name="{{field[13][2]}}">
                                    </p-radioButton>
                                    <span>NO</span>
                                </div>
                            </div>
                            <div *ngIf="readOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].doesEntityProvideEvidence | yesNo }} </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(9)">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}c. {{l('HighRiskIpC')}}  <span class="required">*</span> </div>
                        </div>
                        <div class="row-flex-justified margin-left"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['UploadDocumentHighRisk'] }">
                            <app-upload-files [Documents]="economicsubstance.relevantActivities[index].highRiskDocuments"
                                              [DocumentTypeName]="highRiskDoc"
                                              [IsEconomicDocument]="false"
                                              [readOnlyMode]="readOnlyMode"
                                              [displayFromCa] ="displayFromCa ||historyDisplay"
                                              [ctspId]="ctspId"
                                              > </app-upload-files>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(8)">
                    <div class="row-flex-justified-rows" *ngIf="ShowHideResult(23)">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}d. {{l('HighRiskIpD')}}</div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                             [ngClass]="
                             {
                             'input-warnning-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalGrossAnnualIncome'],
                             'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalGrossAnnualIncomeE']
                             }"
                             >
                            <p-dropdown [options]="currency" optionLabel="currencyCode"
                                        (onChange)="SetCurrencyId($event)"
                                        [(ngModel)]="economicsubstance.currency" class="width-95" id="{{field[14][0]}}" name="{{field[14][2]}}">
                            </p-dropdown>

                            <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].totalGrossAnnualIncome"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[15][0]}}" name="{{field[15][2]}}"></p-spinner>


                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalGrossAnnualIncomeString"
                                   id="{{field[15][0]}}" name="{{field[15][2]}}">


                        </div>

                        <div *ngIf="readOnlyMode" class="margin-left"
                             [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                 'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                            <div class="col-flex-width20 ">
                                <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                            </div>
                            <div *ngIf="!importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalGrossAnnualIncome}}</label>
                                </div>
                            </div>

                            <div *ngIf="importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalGrossAnnualIncomeString}}</label>
                                </div>
                            </div>

                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                                <label class="p-readonly-label">USD</label>
                            </div>
                            <div *ngIf="displayFromCa &&economicsubstance.currency &&  economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                                <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].totalGrossAnnualIncome)}}</label>
                            </div>
                        </div>


                    </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}e. {{l('HighRiskIpE')}}</div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                             [ngClass]="{ 'input-warnning-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeRoyalities'],
                                          'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeRoyalitiesE']}"
                             >
                            <p-dropdown [options]="currency" optionLabel="currencyCode"
                                        (onChange)="SetCurrencyId($event)"
                                        [(ngModel)]="economicsubstance.currency" class="width-95" id="{{field[16][0]}}" name="{{field[16][2]}}">
                            </p-dropdown>
                            <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeRoyalities"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[17][0]}}" name="{{field[17][2]}}"></p-spinner>


                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeRoyalitiesString"
                                   id="{{field[17][0]}}" name="{{field[17][2]}}">

                        </div>
                        <div *ngIf="readOnlyMode" class="margin-left"
                             [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                 'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                            <div class="col-flex-width20 ">
                                <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                            </div>

                            <div *ngIf="!importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].grossIncomeRoyalities}}</label>
                                </div>
                            </div>

                            <div *ngIf="importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].grossIncomeRoyalitiesString}}</label>
                                </div>
                            </div>
                            <div *ngIf="displayFromCa &&economicsubstance.currency &&  economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                                <label class="p-readonly-label">USD</label>
                            </div>
                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                                <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].grossIncomeRoyalities)}}</label>
                            </div>
                        </div>


                    </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}f. {{l('HighRiskIpF')}}</div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left "
                             [ngClass]="{ 'input-warnning-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeGains'],
                                           'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeGainsE']
                             }"
                             >
                            <p-dropdown [options]="currency" optionLabel="currencyCode"
                                        (onChange)="SetCurrencyId($event)"
                                        [(ngModel)]="economicsubstance.currency" class="width-95" id="{{field[18][0]}}" name="{{field[18][2]}}">
                            </p-dropdown>
                            <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeGains"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[19][0]}}" name="{{field[19][2]}}"></p-spinner>

                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeGainsString"
                                   id="{{field[19][0]}}" name="{{field[19][2]}}">

                        </div>
                        <div *ngIf="readOnlyMode" class="margin-left"
                             [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                 'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                            <div class="col-flex-width20 ">
                                <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                            </div>

                            <div *ngIf="!importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].grossIncomeGains}}</label>
                                </div>
                            </div>

                            <div *ngIf="importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].grossIncomeGainsString}}</label>
                                </div>
                            </div>

                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                                <label class="p-readonly-label">USD</label>
                            </div>
                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                                <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].grossIncomeGains)}}</label>
                            </div>
                        </div>

                    </div>
                    <div class="row-flex-justified-rows" *ngIf="ShowHideResult(17)">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[6].number}}g. {{l('HighRiskIpG')}}</div>
                        </div>
                        <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left "
                             [ngClass]="{ 'input-warnning-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeOthers'],
                                           'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['GrossIncomeOthersE']  }">
                            <p-dropdown [options]="currency" optionLabel="currencyCode"
                                        (onChange)="SetCurrencyId($event)"
                                        [(ngModel)]="economicsubstance.currency" class="width-95" id="currencyID7{{headingSeq}}" name="currencyId7{{headingSeq}}">
                            </p-dropdown>
                            <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeOthers"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="grossIncomeOthersid{{headingSeq}}" name="grossIncomeOthersname{{headingSeq}}"></p-spinner>

                            <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].grossIncomeOthersString" id="grossIncomeOthersid{{headingSeq}}" name="grossIncomeOthersname{{headingSeq}}">

                        </div>
                        <div *ngIf="readOnlyMode" class="margin-left"
                             [ngClass]="{
                                 'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                 'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                 'row-flex-justified-width_100':!displayFromCa,
                                 'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                            <div class="col-flex-width20 ">
                                <label class="p-readonly-label">{{ economicsubstance.currency ? economicsubstance.currency.currencyCode : 'USD' }}</label>
                            </div>

                            <div *ngIf="!importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{ economicsubstance.relevantActivities[index].grossIncomeOthers }}</label>
                                </div>
                            </div>

                            <div *ngIf="importOnlyMode">
                                <div class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{ economicsubstance.relevantActivities[index].grossIncomeOthersString }}</label>
                                </div>
                            </div>

                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                                <label class="p-readonly-label">USD</label>
                            </div>
                            <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                                <label class="p-readonly-label">{{ GetTotalInUSD(economicsubstance.relevantActivities[index].grossIncomeOthers) }}</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(17)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[6].number}}h. {{l('TangibleAsset')}} <span class="required">*</span> </label>
                    </div>
                    <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                         [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TangibleAsset'] }">  
                        <input pInputText type="text" maxlength="255" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].tangibleAsset" id="tangibleAssetid{{headingSeq}}" name="tangibleAssetname{{headingSeq}}">
                    </div>
                    <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">  
                        <label class="p-readonly-label word-wrapping">{{economicsubstance.relevantActivities[index].tangibleAsset}}</label>  
                    </div>
                </div>
                <div *ngIf="ShowHideResult(17)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[6].number}}i. {{l('TangibleAssetIncome')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TangibleAssetIncome'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].tangibleAssetIncome" class="width-95" rows="3" id="tangibleAssetIncomeid{{headingSeq}}" name="tangibleAssetIncomename{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].tangibleAssetIncome" class="textarea-readonly-background" rows="3" id="tangibleAssetIncomeid{{headingSeq}}" name="tangibleAssetIncomename{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].tangibleAssetIncomeDocuments"
                        [DocumentTypeName]="tangibleAssetIncomeDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(17)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[6].number}}j. {{l('TangibleAssetEmployeeResponsibility')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TangibleAssetEmployeeResponsibility'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].tangibleAssetEmployeeResponsibility" class="width-95" rows="3" id="tangibleAssetEmployeeResponsibilityid{{headingSeq}}" name="tangibleAssetEmployeeResponsibilityname{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].tangibleAssetEmployeeResponsibility" class="textarea-readonly-background" rows="3" id="tangibleAssetEmployeeResponsibilityid{{headingSeq}}" name="tangibleAssetEmployeeResponsibilityname{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].tangibleAssetEmployeeResponsibilityDocuments"
                        [DocumentTypeName]="tangibleAssetEmployeeResponsibilityDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(17)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[6].number}}k. {{l('HistoryofStrategicDecisionsInBVI')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['HistoryofStrategicDecisionsInBVI'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].historyofStrategicDecisionsInBVI" class="width-95" rows="3" id="historyofStrategicDecisionsInBVIid{{headingSeq}}" name="historyofStrategicDecisionsInBVIname{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].historyofStrategicDecisionsInBVI" class="textarea-readonly-background" rows="3" id="historyofStrategicDecisionsInBVIid{{headingSeq}}" name="historyofStrategicDecisionsInBVIname{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].historyofStrategicDecisionsInBVIDocuments"
                        [DocumentTypeName]="historyofStrategicDecisionsInBVIDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>       
                <div *ngIf="ShowHideResult(17)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[6].number}}l. {{l('HistoryofTradingActivityIncome')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['HistoryofTradingActivityIncome'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].historyofTradingActivityIncome" class="width-95" rows="3" id="historyofTradingActivityIncomeid{{headingSeq}}" name="historyofTradingActivityIncomename{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].historyofTradingActivityIncome" class="textarea-readonly-background" rows="3" id="historyofTradingActivityIncomeid{{headingSeq}}" name="historyofTradingActivityIncomename{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].historyofTradingActivityIncomeDocuments"
                        [DocumentTypeName]="historyofTradingActivityIncomeDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>            
            </div>
        </div>

        <!--Other CIGA-->
        <div *ngIf="fieldDetails[7].enablment">
            <div *ngIf="!ShowHideResult(8)">
                <div class="row-flex-justified ">
                    <label class="ac-header">9{{headingSeq}}.{{fieldDetails[7].number}}. {{l('OtherCIGATitle')}}</label>
                </div>
                <div class="row-flex-justified-rows">

                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[7].number}}a. {{l('OtherCIGAA')}}  <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left">
                        <div *ngIf="!readOnlyMode" class="radio-input"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesLegalEntityConductCIGA'] }">
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].doesLegalEntityConductCIGA" id="{{field[20][0]}}" name="{{field[20][2]}}">
                                </p-radioButton>
                                <span>YES</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].doesLegalEntityConductCIGA" id="{{field[20][1]}}" name="{{field[20][2]}}">
                                </p-radioButton>
                                <span>NO</span>
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].doesLegalEntityConductCIGA | yesNo }} </label>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(10)">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <label>9{{headingSeq}}.{{fieldDetails[7].number}}b. {{l('OtherCIGAB')}}  <span class="required">*</span> </label>
                        </div>
                        <div class="row-flex-justified margin-left">
                            <div *ngIf="!readOnlyMode" class="radio-input"
                                 [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['DoesEntityProvideEvidenceroRebut'] }">
                                <div>
                                    <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityProvideEvidenceroRebut" id="{{field[21][0]}}" name="{{field[21][2]}}">
                                    </p-radioButton>
                                    <span>YES</span>
                                </div>
                                <div>
                                    <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].doesEntityProvideEvidenceroRebut" id="{{field[21][1]}}" name="{{field[21][2]}}">
                                    </p-radioButton>
                                    <span>NO</span>
                                </div>
                            </div>
                            <div *ngIf="readOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].doesEntityProvideEvidenceroRebut | yesNo }} </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(11)">
                    <div class="row-flex-justified-rows" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['UploadOtherCIGA'] }">
                        <div class="row-flex-justified-largefont margin-left">
                            <label>9{{headingSeq}}.{{fieldDetails[7].number}}c. {{l('OtherCIGAC')}}  <span class="required">*</span> </label>
                        </div>
                        <app-upload-files
                        [Documents]="economicsubstance.relevantActivities[index].otherCIGADocuments"
                        [DocumentTypeName]="OtherCIGADoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"
                        > </app-upload-files>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(18)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[7].number}}d. {{l('RelevantIPAsset')}} <span class="required">*</span> </label>
                    </div>
                    <div *ngIf="!readOnlyMode" class="row-flex-justified-width margin-left"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['RelevantIPAsset'] }">  
                        <input pInputText type="text" maxlength="255" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].relevantIPAsset" id="relevantIPAssetid{{headingSeq}}" name="relevantIPAssetname{{headingSeq}}">
                    </div>
                    <div *ngIf="readOnlyMode" class="row-flex-justified-width margin-left">  
                        <label class="p-readonly-label word-wrapping">{{economicsubstance.relevantActivities[index].relevantIPAsset}}</label>  
                    </div>
                </div>
                <div *ngIf="ShowHideResult(18)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[7].number}}e. {{l('IPAssetsInBVI')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IPAssetsInBVI'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].ipAssetsInBVI" class="width-95" rows="3" id="ipAssetsInBVIid{{headingSeq}}" name="ipAssetsInBVIname{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].ipAssetsInBVI" class="textarea-readonly-background" rows="3" id="ipAssetsInBVIid{{headingSeq}}" name="ipAssetsInBVIname{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].ipAssetsInBVIDocuments"
                        [DocumentTypeName]="ipAssetsInBVIDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(18)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[7].number}}f. {{l('IPAssetsEmployeeResponsibility')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['IPAssetsEmployeeResponsibility'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].ipAssetsEmployeeResponsibility" class="width-95" rows="3" id="ipAssetsEmployeeResponsibilityid{{headingSeq}}" name="ipAssetsEmployeeResponsibilityname{{headingSeq}}"id="ipAssetsEmployeeResponsibilityid{{headingSeq}}" name="ipAssetsEmployeeResponsibilityname{{headingSeq}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].ipAssetsEmployeeResponsibility" class="textarea-readonly-background" rows="3" id="ipAssetsEmployeeResponsibilityid{{headingSeq}}" name="ipAssetsEmployeeResponsibilityname{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].ipAssetsEmployeeResponsibilityDocuments"
                        [DocumentTypeName]="ipAssetsEmployeeResponsibilityDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>
                <div *ngIf="ShowHideResult(18)" class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <label>9{{headingSeq}}.{{fieldDetails[7].number}}g. {{l('ConcreteEvidenceDecisionInBVI')}} <span class="required">*</span> </label>
                    </div>
                    <div class="row-flex-justified margin-left"
                            [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['ConcreteEvidenceDecisionInBVI'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="1500" [(ngModel)]="economicsubstance.relevantActivities[index].concreteEvidenceDecisionInBVI" class="width-95" rows="3" id="concreteEvidenceDecisionInBVIid{{headingSeq}}" name="concreteEvidenceDecisionInBVIname{{headingSeq}}"> 
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].concreteEvidenceDecisionInBVI" class="textarea-readonly-background" rows="3"  id="concreteEvidenceDecisionInBVIid{{headingSeq}}" name="concreteEvidenceDecisionInBVIname{{headingSeq}}"></textarea>

                    </div>
                    <div class="row-flex-justified">
                        <app-upload-files [Documents]="economicsubstance.relevantActivities[index].concreteEvidenceDecisionInBVIDocuments"
                        [DocumentTypeName]="concreteEvidenceDecisionInBVIDoc"
                        [IsEconomicDocument]="false"
                        [readOnlyMode]="readOnlyMode"
                        [displayFromCa] ="displayFromCa ||historyDisplay"
                        [ctspId]="ctspId"> </app-upload-files>
                    </div>
                </div>
            </div>
        </div>

        <!--Core Income Generating-->
        <div *ngIf="ShowHideResult(14)">

            <div *ngIf="fieldDetails[8].enablment">
                <div class="row-flex-justified ">
                    <label class="ac-header">9{{headingSeq}}.{{fieldDetails[8].number}}. {{l('ESCoreIncomeTitle')}}</label>
                </div>
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified  margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['ConductedCIGAActivity'] }">
                        <app-ciga-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[8].number" [details]="economicsubstance.relevantActivities[index].conductedCIGAActivity" [cigalookup]="CIGAlookup" [readOnlyMode]="readOnlyMode" [showHideCondition]="ShowHideResult(15)"></app-ciga-detail-table>

                    </div>
                    <div *ngIf="isOtherActivitySelected() && !readOnlyMode">
                        <div class="row-flex-justified-largefont">
                            <label>Other option.<span class="required">*</span> </label>
                        </div>
                        <div class="row-flex-justified-width" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['CIGAOtherDetail'] }">
                            <input *ngIf="!readOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].cigaOtherDetail" maxlength="100"
                                   id="{{field[33][0]}}" name="{{field[33][2]}}">
                          
                        </div>
                    </div>

                    <div *ngIf="isOtherActivitySelected(true) && readOnlyMode">
                        <div class="row-flex-justified-largefont">
                            <label>Other option.<span class="required">*</span> </label>
                        </div>
                        <div class="row-flex-justified-width" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['CIGAOtherDetail'] }">
                          <label *ngIf="readOnlyMode" class="p-readonly-label word-wrapping">{{economicsubstance.relevantActivities[index].cigaOtherDetail}} </label>
                        </div>
                    </div>

                </div>
            </div>          
        </div>

         <!--Outsourcing-->
         <div *ngIf="ShowHideResult(3)">
            <div *ngIf="fieldDetails[9].enablment">
                <div class="row-flex-justified ">
                    <label class="ac-header">9{{headingSeq}}.{{fieldDetails[9].number}}. {{l('OutsourcingTitle')}}</label>
                </div>
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <div>9{{headingSeq}}.{{fieldDetails[9].number}}a. {{l('OutsourcingA')}} <span class="required">*</span> </div>
                    </div>
                    <div class="row-flex-justified margin-left">
                        <div *ngIf="!readOnlyMode" class="radio-input"
                             [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['HasAnyIncomeBeenOutsourced'] }">
                            <div>
                                <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].hasAnyIncomeBeenOutsourced" id="{{field[22][0]}}" name="{{field[22][2]}}">
                                </p-radioButton>
                                <span>YES</span>
                            </div>
                            <div>
                                <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].hasAnyIncomeBeenOutsourced" id="{{field[22][1]}}" name="{{field[22][2]}}">
                                </p-radioButton>
                                <span>NO</span>
                            </div>
                        </div>
                        <div *ngIf="readOnlyMode">
                            <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].hasAnyIncomeBeenOutsourced | yesNo }} </label>
                        </div>
                    </div>
                </div>
                <!--<div *ngIf="ShowHideResult(4)">
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[9].number}}b. {{l('OutsourcingB')}} <span class="required">*</span></div>
                        </div>
                        <div class="row-flex-justified margin-left">
                            <div *ngIf="!readOnlyMode" class="radio-input"
                                 [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['WasCIGAOutsourcedInBVI'] }">
                                <div>
                                    <p-radioButton [value]="true" [(ngModel)]="economicsubstance.relevantActivities[index].wasCIGAOutsourcedInBVI" id="{{field[23][0]}}" name="{{field[23][2]}}">
                                    </p-radioButton>
                                    <span>YES</span>
                                </div>
                                <div>
                                    <p-radioButton [value]="false" [(ngModel)]="economicsubstance.relevantActivities[index].wasCIGAOutsourcedInBVI" id="{{field[23][1]}}" name="{{field[23][2]}}">
                                    </p-radioButton>
                                    <span>NO</span>
                                </div>
                            </div>
                            <div *ngIf="readOnlyMode">
                                <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].wasCIGAOutsourcedInBVI != null?(economicsubstance.relevantActivities[index].wasCIGAOutsourcedInBVI?'Yes':'No'):'' }} </label>
                            </div>
                        </div>
                    </div>
                </div>-->
            <div *ngIf="ShowHideResult(4)">
                <div class="row-flex-justified-rows">
                    <div class="row-flex-justified margin-left"
                         [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['OutsourceTable'] }">
                        <app-outsource-detail-table [headingSeq]="headingSeq" [sequenceNo]="fieldDetails[9].number"
                                                    [details]="economicsubstance.relevantActivities[index].serviceProviders"
                                                    [country]="country" [readOnlyMode]="readOnlyMode">
                        </app-outsource-detail-table>
                    </div>
                </div>
            </div>
                    <div class="row-flex-justified-rows">
                        <div class="row-flex-justified-largefont margin-left">
                            <div>9{{headingSeq}}.{{fieldDetails[9].number}}c. {{l('OutsourcingD')}} <span *ngIf="ShowHideResult(15)" class="required">*</span></div>
                        </div>
                        <div class="row-flex-justified-width margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['TotalExpenditureIncurredInBVI'] }">

                            <p-dropdown *ngIf="!readOnlyMode" [options]="currency"
                                        optionLabel="currencyCode"
                                        [(ngModel)]="economicsubstance.currency"
                                        (onChange)="SetCurrencyId($event)"
                                        class="width-95"
                                        id="{{field[32][0]}}"
                                        name="{{field[32][2]}}">
                            </p-dropdown>
                            <div *ngIf="!readOnlyMode">
                                <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="economicsubstance.relevantActivities[index].totalExpenditureIncurredInBVI"  (keypress)="maskDecimal($event, 15, 2)" [min]="0" [max]="999999999999999.99" [step]="0.25" [formatInput]="true" thosandSeparator="," decimalSeparator="." id="{{field[24][0]}}" name="{{field[24][2]}}"></p-spinner>


                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="economicsubstance.relevantActivities[index].totalExpenditureIncurredInBVIString"
                                       id="{{field[24][0]}}" name="{{field[24][2]}}">
                            </div>

                            <div *ngIf="readOnlyMode" class="margin-left"
                                 [ngClass]="{'row-flex-justified-width_250':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode!== 'USD',
                                              'row-flex-justified-width_120':displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode === 'USD',
                                              'row-flex-justified-width_100':!displayFromCa,
                                              'row-flex-justified-width_121':displayFromCa && !economicsubstance.currency  }">
                                <div class="col-flex-width20 ">
                                    <label class="p-readonly-label">{{economicsubstance.currency?economicsubstance.currency.currencyCode:'USD'}}</label>
                                </div>

                                <div *ngIf="!importOnlyMode">
                                    <div class="col-flex-width70 ">
                                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpenditureIncurredInBVI}}</label>
                                    </div>
                                </div>

                                <div *ngIf="importOnlyMode">
                                    <div class="col-flex-width70 ">
                                        <label class="p-readonly-label">{{economicsubstance.relevantActivities[index].totalExpenditureIncurredInBVIString}}</label>
                                    </div>
                                </div>


                                <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width20">
                                    <label class="p-readonly-label">USD</label>
                                </div>
                                <div *ngIf="displayFromCa && economicsubstance.currency && economicsubstance.currency.currencyCode !='USD' " class="col-flex-width70 ">
                                    <label class="p-readonly-label">{{GetTotalInUSD(economicsubstance.relevantActivities[index].totalExpenditureIncurredInBVI)}}</label>
                                </div>
                            </div>

                        </div>
                    </div>
                
            </div>
        </div>

        <!--Equipment-->
        <div *ngIf="fieldDetails[10].enablment">
            <div class="row-flex-justified">
                <label class="ac-header">9{{headingSeq}}.{{fieldDetails[10].number}}. {{l('EquipmentTitle')}}</label>
            </div>
            <div class="row-flex-justified-rows">
                    <div class="row-flex-justified-largefont margin-left">
                        <div>9{{headingSeq}}.{{fieldDetails[10].number}}a. {{l('EquipmentC')}} <label tooltip="If there is no equipment located within the Virgin Islands, please add 'None' as the response."><u>{{l("EquipmentC1")}}</u> </label> {{l("EquipmentC2")}} <span class="required">*</span> </div>
                    </div>
                    <div class="row-flex-justified margin-left" [ngClass]="{ 'input-error-box': this._economicSubstanceService.step4Error[this.headingSeq] && this._economicSubstanceService.step4Error[this.headingSeq]['EquipmentDescription'] }">
                        <textarea *ngIf="!readOnlyMode" maxlength="255" [(ngModel)]="economicsubstance.relevantActivities[index].equipmentDescription" class="width-95" rows="3" id="{{field[30][0]}}" name="{{field[30][2]}}">
                            </textarea>

                        <textarea *ngIf="readOnlyMode" pInputTextarea readonly="true" [(ngModel)]="economicsubstance.relevantActivities[index].equipmentDescription" class="textarea-readonly-background" rows="3" id="{{field[30][0]}}" name="{{field[30][2]}}"></textarea>

                    </div>
             </div>
        </div>
    </p-card>



</div>
