import { Component, OnInit, Injector, ViewChild, EventEmitter, Output, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { ModalDirective } from 'ngx-bootstrap';
import { SelectItem } from 'primeng/api';
import { CIGALookUpDto } from '@shared/service-proxies/service-proxies';

@Component({
    selector: 'app-ciga-detail-dialog',
    templateUrl: './ciga-detail-dialog.component.html'
})
    
export class CigaDetailDialogComponent extends AppComponentBase implements OnInit
{
    @ViewChild('cigamodal', { static: true }) modal: ModalDirective;
    cigalookup: CIGALookUpDto[];

    @Output() submitted: EventEmitter<CIGALookUpDto[]> = new EventEmitter<CIGALookUpDto[]>();

    localSelectedGICA: any;
    localGICALookup: SelectItem[];
    constructor(injector: Injector)
    {
        super(injector);

    }
    ngOnInit()
    { }

    shown(): void
    { }

    show(items: any, item: any): void
    {
        this.localSelectedGICA = [];
        this.localGICALookup = items.map(y => ({ label: y.activityDetails, value: y, disabled: false }));
        this.localSelectedGICA = item;
       
        this.modal.show();
    }
    close(): void {
        this.modal.hide();
    }

    save(): void {
        this.submitted.emit(this.localSelectedGICA);
        this.close();
        return;
    }

    change(event:any)
    {
        if (event.value)
        {
            let obj1 = event.value.find(x => x.activityDetails === "None");
            if (obj1)
            {
                this.localGICALookup.forEach(x => { if (x.value.activityDetails !== "None") x.disabled = true });
                this.localSelectedGICA = this.localSelectedGICA.filter(x => x.activityDetails === "None");
            }
            else
                this.localGICALookup.forEach(x => { if (x.value.activityDetails !== "None") x.disabled = false });
            
        }
    }
}
