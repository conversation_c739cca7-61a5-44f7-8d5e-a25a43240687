<div class="ca-ra-selection-list-container">

    <div class="ca-ra-selection-list-toggle-section">
        <span>
            {{l('RASelectionList_SelectRAs')}}:
        </span>
        <span>
            <p-radioButton id="selectAll" label="{{l('All')}}" value="true" [(ngModel)]="selectAll" (click)="!isReadonly && onSelectAll()" [disabled]="isReadonly"></p-radioButton>
        </span>

        <span class="ca-ra-selection-list-selectnone-section">
            <p-radioButton id="selectNone" label="{{l('None')}}" value="true" [(ngModel)]="selectNone" (click)="!isReadonly && onSelectNone()" [disabled]="isReadonly"></p-radioButton>
        </span>
        <span>
            ({{l('RASelectionList_Selected', [selectedCount])}})
        </span>
    </div>
    <div class="ca-ra-selection-list-search-section">
        <input [(ngModel)]="searchText" id="ca-ra-selection-list-search" type="text" pInputText placeholder="{{l('RASelectionList_SearchRAs')}}">
    </div>
    <div *ngIf="ctspList" class="ca-ra-selection-list-grid">
        <div *ngFor="let ra of ctspList" title="{{ra.number}}">
            <div *ngIf="isMatchFilter(ra)" class="ca-ra-selection-list-grid-row ui-g">
                <div class="ca-ra-selection-list-grid-cell ui-g-1 ui-g-nopad">
                    <p-checkbox id="isSelected" class="ca-ra-selection-list-checkbox"
                                [(ngModel)]="ra.isSelected" binary="true"
                                (ngModelChange)="onSelectRA($event)"></p-checkbox>
                </div>
                <div class="ca-ra-selection-list-grid-cell ui-g-11 ui-g-nopad">
                    <span><i class="fa fa-circle" [ngClass]="{'ra-status-green-icon': ra.active, 'ra-status-gray-icon': !ra.active }"></i></span>
                    <span><a id="ID" (click)="showBasicDialog(ra.id)"> {{ra.caCtspName}} ({{ra.number}})</a></span>
                </div>
            </div>
        </div>
    </div>

</div>



<p-dialog header="CTSP Detail" [(visible)]="displayBasic" position="left" [modal]="true" [style]="{width: '20vw'}" [baseZIndex]="10000">
    <li *ngFor="let item of items">
        {{ item }}
    </li>
    <p-footer>
        <button id="ctspListClose" type="button" pButton icon="pi pi-times" (click)="displayBasic=false" label="Close" class="ui-button-secondary"></button>
    </p-footer>
</p-dialog>

