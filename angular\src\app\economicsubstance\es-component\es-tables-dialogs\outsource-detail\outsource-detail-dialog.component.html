<div bsModal #outsourcemodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="outsourcemodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Outsourcing Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #outsourceform="ngForm">
                    <p-card>
                        <ngFormValidation #outsourceFormValidation></ngFormValidation>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>
                                    Name of entity to whom outsourced <span class="required">*</span>
                                </label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="details.name" required maxlength="150"
                                       name="Name" id="sourceid" >
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Details of resources deployed by the entity in carrying out the activity on their behalf <span class="required">*</span></label>
                                <textarea class="form-control" [(ngModel)]="details.addressLine1"  pInputTextarea rows="2" maxlength="255" required
                                       name="DetailsOfResources" id="sourceaddress1id">
                                </textarea>
                            </div>
                        </div>
                       
                        
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Number of staff employed in carrying out CIGA for the entity <span class="required">*</span></label>


                                <p-spinner *ngIf="!importOnlyMode" (keypress)="maskDecimal($event, 15, 3)"
                                           [min]="0" [max]="999999999999999.99" [step]="0.25" precision="2" [(ngModel)]="details.numberOfStaffEmployed"
                                           name="numberofstaffedname" id="numberofstaffedid" required></p-spinner>


                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.numberOfStaffEmployedString"
                                                name="numberofstaffedstringname" id="numberofstaffedstringid" required>

                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Hours per month each person employed for this relevant activity <span class="required">*</span></label>
                                <p-spinner *ngIf="!importOnlyMode" (keypress)="maskDecimal($event, 15, 2)"
                                           [min]="0" [max]="999999999999999.99" [step]="0.25" [(ngModel)]="details.hoursPerMonthForEmployee"
                                           name="hourspermonthname" id="hourspermonthid" required></p-spinner>

                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.hoursPerMonthForEmployeeString"
                                        name="hourspermonthstringname" id="hourspermonthstringid" required>

                            </div>
                        </div>

                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Is the entity able to monitor and control carrying out of the outsourced activity? <span class="required">*</span></label>
                                <div class="radio-input">
                                    <div>
                                        <p-radioButton [value]="true" [(ngModel)]="details.isEntityAbleToMonitor" name="isEntityAbleToMonitorname" id="isEntityAbleToMonitoridy" required>
                                        </p-radioButton>
                                        <span>YES</span>
                                    </div>
                                    <div>
                                        <p-radioButton [value]="false" name="isEntityAbleToMonitorname" id="isEntityAbleToMonitorn"
                                                       [(ngModel)]="details.isEntityAbleToMonitor" required>
                                        </p-radioButton>
                                        <span>NO</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
