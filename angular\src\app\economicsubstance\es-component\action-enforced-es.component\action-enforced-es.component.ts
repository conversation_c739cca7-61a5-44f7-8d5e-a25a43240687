import { Component, OnInit, Input, Injector } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    CorporateEntityDto, EconomicSubstanceDeclarationDto,
    EconomicSubstanceInformationRequiredDto, RelevantActivity, ESCAAssessmentDto
} from '@shared/service-proxies/service-proxies';
import { EconomicDocumentsName, RelevantDocumentsName } from '@app/economicsubstance/EconomicSubstance';
@Component({
  selector: 'app-action-enforced-es',
  templateUrl: './action-enforced-es.component.html',
  styleUrls: ['./action-enforced-es.component.css']
})
export class ActionEnforcedEsComponent extends AppComponentBase  implements OnInit
{


    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() ctspId: any;
    @Input() esassessment: ESCAAssessmentDto;
    EnforcmentDocumentDoc: any;

    constructor(injector: Injector) { super(injector); }


    ngOnInit()
    {

        this.EnforcmentDocumentDoc = EconomicDocumentsName.EnforcmentDocumentDoc;
  }

}
