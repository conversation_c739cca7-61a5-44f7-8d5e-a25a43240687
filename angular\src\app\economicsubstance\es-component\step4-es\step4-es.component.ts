import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    EconomicSubstanceDeclarationDto, CountryDto, CIGALookUpDto,
    RelevantActivity, ESCAAssessmentDto,
    CorporateEntityDto
} from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { NgForm } from '@angular/forms';
import { RelaventActivityReviewStatus } from '@app/economicsubstance/EconomicSubstance';
import { AppConsts } from '@shared/AppConsts';
import * as moment from 'moment';
@Component({
    selector: 'app-step4-es',
    templateUrl: './step4-es.component.html',
    styleUrls: ['./step4-es.component.css']
})
export class Step4EsComponent extends AppComponentBase implements OnInit {
    @Input() public economicsubstance: EconomicSubstanceDeclarationDto; // this is typed as string, but you can use any type you want
    @Input() public economicsubstanceOld: EconomicSubstanceDeclarationDto = null;
    @Input() public corporateEntity: CorporateEntityDto;
    
    @Input() country: CountryDto;
    @Input() CIGALookup: any;
    @Input() currency: any;
    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() esassessment: ESCAAssessmentDto;
    @Input() relevantActivityStatus: RelaventActivityReviewStatus[];
    @Input() ctspId: any;
    @Input() importOnlyMode: boolean = false;
    @Input() historyDisplay: boolean;
    cigaLookup: any;
    generalBusiness: any;
    holdingBusiness: any;
    holdingBusiness_New: any; // Declaration 2.0
    intellectualBusiness: any;
    @ViewChild('#step4', { static: false }) form: NgForm;
       
    public lookupFiltered: CIGALookUpDto[];
    startchar: string="a";
    head2: string;
    constructor(injector: Injector) { super(injector); }
    headingListSeq: string[]=[];
    headingIndex: number = 0;
    ngOnInit()
    {
        this.FeildEnablement();

        for (let i = 0; i < 9; i++)
        {
            if (this.economicsubstance.relevantActivities[i].isChecked )
            {
                this.headingListSeq[i] = this.startchar;
                this.startchar = this.getNextLetter(this.startchar);
            }
            else
                this.headingListSeq[i] = "";
        }

            


    }

    public getNextLetter(char: string): string
    {
        let code = char.charCodeAt(0);
        code++;
       return String.fromCharCode(code);
      
    }
    getLookup(index: number)
    {
        if (!this.readOnlyMode) {
            this.cigaLookup = this.CIGALookup.filter(x => x.ativityEnumValue == this.economicsubstance.relevantActivities[index].releventActivityValue);
            this.cigaLookup.sort((a, b) => (a.activityOrder > b.activityOrder) ? 1 : -1);
        }
        return this.cigaLookup;
    }

    getItems(activityEnum: any)
    {
        let isDeclaration2_0 = moment(this.economicsubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day');

        switch (activityEnum)
        {
            case (RelevantActivity.BankBA):
            case (RelevantActivity.DisrBA):
            case (RelevantActivity.FinaBA):
            case (RelevantActivity.FundBA):
            case (RelevantActivity.HeadBA):
            case (RelevantActivity.ShipBA):
            case (RelevantActivity.InsuBA): return this.generalBusiness;
            case (RelevantActivity.HoldBA): return isDeclaration2_0 ? this.holdingBusiness_New  : this.holdingBusiness;
            case (RelevantActivity.IntelBA): return this.intellectualBusiness 

        }
    }
    FeildEnablement() {

        // banking business
        this.generalBusiness =
            [
            // turnover
            { "enablment": true, "number": 1 },
            //direction and management
            { "enablment": true, "number": 2 },
            
            //Expenditure
            { "enablment": true, "number": 3 },
            //employee
            { "enablment": true, "number": 4 },
            //premises
            { "enablment": true, "number": 5 },
            // holding
            { "enablment": false, "number": 1 },
            //highrisk
            { "enablment": false, "number": 6 },
            //other CIGA
            { "enablment": false, "number": 8 },
            //core income
            { "enablment": true, "number": 6 },
            // outsourcing
            { "enablment": true, "number": 7 },
            //equipment
            { "enablment": false, "number": 11 },
            //mobileincome
            { "enablment": false, "number": 12 },
            ];

        // holding business
        this.holdingBusiness =
            [ 
            // turnover
            { "enablment": false, "number": 1 },
            //direction and management
            { "enablment": false, "number": 2 },
            //Expenditure
            { "enablment": false, "number": 3 },
            //employee
            { "enablment": true, "number": 2 },
            //premises
            { "enablment": true, "number": 3 },
            // holding
            { "enablment": true, "number": 1 },
            //highrisk
            { "enablment": false, "number": 6 },
            //other CIGA
            { "enablment": false, "number": 8 },
            //core income
            { "enablment": false, "number": 6 },
            // outsourcing
            { "enablment": false, "number": 7 },
            //equipment
            { "enablment": false, "number": 11 },
            //mobileincome
            { "enablment": false, "number": 12 },
            ];

        // Declaration 2.0
        // holding business
        this.holdingBusiness_New =
            [ 
            // turnover
            { "enablment": true, "number": 1 },
            //direction and management
            { "enablment": false, "number": 2 },
            //Expenditure
            { "enablment": true, "number": 2 },
            //employee
            { "enablment": true, "number": 4 },
            //premises
            { "enablment": true, "number": 5 },
            // holding
            { "enablment": true, "number": 3 },
            //highrisk
            { "enablment": false, "number": 6 },
            //other CIGA
            { "enablment": false, "number": 8 },
            //core income
            { "enablment": false, "number": 6 },
            // outsourcing
            { "enablment": false, "number": 7 },
            //equipment
            { "enablment": false, "number": 11 },
            //mobileincome
            { "enablment": false, "number": 12 },
            ];

        // Intellectual
        this.intellectualBusiness=
            [
            // turnover
            { "enablment": true, "number": 1 },
            //direction and management
            { "enablment": true, "number": 2 },
            //Expenditure
            { "enablment": true, "number": 3 },
            //employee
            { "enablment": true, "number": 4 },
            //premises
            { "enablment": true, "number": 5 },
            // holding
            { "enablment": false, "number": 1 },
            //highrisk
            { "enablment": true, "number": 6 },
            //other CIGA
            { "enablment": true, "number": 7 },
            //core income
            { "enablment": true, "number": 8 },
            // outsourcing
            { "enablment": true, "number": 9 },
            //equipment
            { "enablment": true, "number": 10 },
             //mobileincome
            { "enablment": true, "number": 12 },
            ];
    }

}
