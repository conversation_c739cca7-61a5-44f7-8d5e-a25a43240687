import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { ValidationResultMainDto } from '@shared/service-proxies/service-proxies';

@Injectable({
    providedIn: "root"
})
export class EconomicSubstanceService {

    constructor(
    ) {
    }

    private isPrestine: boolean = false;
    validations$: Subject<ValidationResultMainDto[]> = new Subject<ValidationResultMainDto[]>();
    // string errorList for all errors
    errorList: string[] = [];
    warningList: string[] = [];
    warningFirst: boolean = true;

    // Step 1 - used by html template to determine if error exists or not
    step1Error: any = {};
    step2Error: any = {};

    // Step 3 - used by html template to determine if error exists or not
    step3Error: any = {};
    step4Error: any = {};
    step5Error: any = {};


    entityDetailsError: any = {};
    
    InformationRequired: any;
    ProvisionalTreatment: any;

    notifyValidations(v: ValidationResultMainDto[]){
        this.validations$.next(v);
    }

    setFormPrestineValue(value: boolean){
        this.isPrestine = value;
    }

    isFormPrestine(){
        return this.isPrestine;
    }

    hasError()
    {
        return this.errorList.length > 0;
    }

    hasWarning()
    {
        return this.warningList.length > 0;
    }

    initializeEmptyError(){
        this.initializeEmptyStep1Error();
        this.initializeEmptyStep2Error();
        this.initializeEmptyStep3Error();
        this.initializeEmptyStep4Error();
        this.initializeEmptyStep5Error();
        this.initializeInformationRequiredError();
        this.initializeProvisionalTreatmentError();
        this.initializeEmptyEntityDetailsError();
        this.errorList = [];
        this.warningList = [];
    }


    initializeInformationRequiredError()
    {
        this.InformationRequired= false
    }

    initializeProvisionalTreatmentError() {
        this.ProvisionalTreatment = false
    }


    initializeEmptyStep4Error(){
        let headingSeqArr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i'];
        this.step4Error = {};
        headingSeqArr.forEach(heading => {
            this.step4Error[heading] = {
                IsActivityDirectedInBVI: false,
                PersonDetail:false,
                TotalTurnover: false,
                TotalExpediture: false,
                TotalExpeditureInBVI: false,
                TotalNoFullTimeEmployee: false,
                TotalFullTimeEmployeeInBVI: false,
                TotalExpenditureIncurredInBVI: false,
                DoesBusinessRequireEquipment: false,
                EquipmentInJurisdiction: false,
                EquipmentDescription: false,
                IsLegalEntityHighRisk: false,
                DoesEntityProvideEvidence: false,
                UploadDocumentHighRisk: false,
                DoesLegalEntityConductCIGA: false,
                DoesEntityProvideEvidenceroRebut: false,
                UploadOtherCIGA: false,
                PremisesTable: false,
                CIGATable: false,
                HasAnyIncomeBeenOutsourced: false,
                WasCIGAOutsourcedInBVI: false,
                OutsourceTable: false,
                DoesEntityComplyItsStatutoryObligations: false,
                DoesEntityManageEquity: false,
                DoesEntityHaveAdequateEmployee:false,
                ConductedCIGAActivity: false,
                CIGAOtherDetail: false,
                MeetingTable: false,
                NoofConductedMeeting: false,
                NoofMeetingHeldInBVI: false,
                TotalGrossAnnualIncome: false,
                GrossIncomeGains: false,

                TotalGrossAnnualIncomeE: false,
                GrossIncomeRoyalitiesE: false,
                GrossIncomeGainsE:false,

                //BVI Declaration Changes 2.0               
                GrossIncomeType: false,
                TotalAssetsValue: false,
                NetAssetsValue:false,
                NoofQuorumBoardMeeting:false,
                IsQuorumBoardMeetingInBVI:false,
                TotalNoCorporateLegalEmployee:false,
                TangibleAsset:false,
                TangibleAssetIncome:false,
                TangibleAssetEmployeeResponsibility:false,
                HistoryofStrategicDecisionsInBVI:false,
                HistoryofTradingActivityIncome:false,
                RelevantIPAsset:false,
                IPAssetsInBVI:false,
                IPAssetsEmployeeResponsibility:false,
                ConcreteEvidenceDecisionInBVI:false,
                IsMeetingMinutesInBVI:false,
                EmployeeDetail:false,
                GrossIncomeOthers: false,
                GrossIncomeOthersE: false
            }
        })
    }

    initializeEmptyStep2Error(){
        this.step2Error = {};
        this.step2Error = {
            RelevantActivities: false,
            
        };
    }

    initializeEmptyStep1Error(){
        this.step1Error = {};
        this.step1Error = {
            IsFinaincialCanChange: false,
            FiscalEndDate: false,
            FiscalStartDate: false,
            FiscalEndDateWarning:false
        };
    }

    initializeEmptyEntityDetailsError(){
        this.entityDetailsError = {};
        this.entityDetailsError = {
            EsEntityDetails: false,
            IdentificationNumber: false,
            IsSameAsRegisteredAddress: false,
            AddressLine1: false,
            Country: false,           
            MNEGroupName:false,
            DoesEntityHaveUltimateParent: false,           
            DoesEntityHaveImmediateParent: false,
            UltimateParent: false,           
            ImmediateParent: false,
            TotalGrossAnnualIncome: false,            
            TotalGrossAnnualIncomeE: false,
        };
    }


    initializeEmptyStep3Error()
    {
        this.step3Error = {};
        this.step3Error = {
            DoesEntityMakeClaimOutisedBVI: false,
            JurisdictionTaxResident: false,
            DoesEntityHaveParentEntity: false,
            ParentEntityName: false,
            ParentEntityAlternativeName: false,
            ParentJurisdiction: false,
            EntityIncorporationNumber: false,
            IsEvidenceNonResidenceOrTreatment: false,
            ResidanceDocument:false,
            TaxPayerIdentificationNumber:false
        };
    };

    initializeEmptyStep5Error()
    {
        this.step5Error = {};
        this.step5Error = {
            SupportingComments: false            
        };
    };
}

export enum EsAction{
    Save,
    Next,
    Previous,
    Discard,
    Submit,
    Edit,
    Print,
    Exit
    
}


