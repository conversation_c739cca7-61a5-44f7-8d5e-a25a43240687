<div>
    <ngFormValidation #securityQuestionsFormValidation displayMode="2"></ngFormValidation>

    <form #securityQuestionsForm="ngForm">
        <div>
            <label for="SecurityQuestion1">{{"SecurityQuestion1" | localize}}</label>
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityQuestion1') }">
                <p-dropdown #securityQuestion1Select id="SecurityQuestion1" name="SecurityQuestion1"
                            [(ngModel)]="securityQuestions.securityQuestion1Id" [options]="securityQuestionList1"
                            (change)="updateSecurityQuestionLists()" [disabled]="securityQuestions.hasTwoFactorCode" required>
                </p-dropdown>
            </div>
        </div>
        <div class="margin-top-5">
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityAnswer1') }">
                <input id="SecurityAnswer1" name="SecurityAnswer1" class="form-control" [(ngModel)]="securityQuestions.securityAnswer1" type="text"
                       #securityAnswer1="ngModel" placeholder="Answer" [disabled]="securityQuestions.hasTwoFactorCode" required securityQuestionAnswer>
            </div>
        </div>
        <div class="margin-top-5">
            <label for="SecurityQuestion2">{{"SecurityQuestion2" | localize}}</label>
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityQuestion2') }">
                <p-dropdown #securityQuestion2Select id="SecurityQuestion2" name="SecurityQuestion2"
                            [(ngModel)]="securityQuestions.securityQuestion2Id" [options]="securityQuestionList2"
                            (change)="updateSecurityQuestionLists()" [disabled]="securityQuestions.hasTwoFactorCode" required>
                </p-dropdown>
            </div>
        </div>
        <div class="margin-top-5">
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityAnswer2') }">
                <input id="SecurityAnswer2" name="SecurityAnswer2" class="form-control" [(ngModel)]="securityQuestions.securityAnswer2" type="text"
                       #securityAnswer2="ngModel" placeholder="Answer" [disabled]="securityQuestions.hasTwoFactorCode" required securityQuestionAnswer>
            </div>
        </div>
        <div class="margin-top-5">
            <label for="SecurityQuestion3">{{"SecurityQuestion3" | localize}}</label>
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityQuestion3') }">
                <p-dropdown #securityQuestion3Select id="SecurityQuestion3" name="SecurityQuestion3"
                            [(ngModel)]="securityQuestions.securityQuestion3Id" [options]="securityQuestionList3"
                            (change)="updateSecurityQuestionLists()" [disabled]="securityQuestions.hasTwoFactorCode" required>
                </p-dropdown>
            </div>
        </div>
        <div class="margin-top-5">
            <div [ngClass]="{ 'input-error': securityQuestionHasError('SecurityAnswer3') }">
                <input id="SecurityAnswer3" name="SecurityAnswer3" class="form-control" [(ngModel)]="securityQuestions.securityAnswer3" type="text"
                       #securityAnswer3="ngModel" placeholder="Answer" [disabled]="securityQuestions.hasTwoFactorCode" required securityQuestionAnswer>
            </div>
        </div>

        <div class="margin-top-5">
            <label for="TwoFactorProvider">{{"Send the Verification Code to" | localize}}</label>
            <div>
                <p-dropdown id="securityQuestionsTwoFactorProviders" [options]="twoFactorProviders" [disabled]="securityQuestions.hasTwoFactorCode"
                            [(ngModel)]="securityQuestions.twoFactorProvider" name="TwoFactorProvider">
                </p-dropdown>
            </div>
        </div>
        <div *ngIf="securityQuestions.hasTwoFactorCode" class="margin-top-5">
            <label for="TwoFactorCode">{{"Verification Code" | localize}}</label>
            <div>
                <input id="TwoFactorCode" name="TwoFactorCode" class="form-control" [(ngModel)]="securityQuestions.twoFactorCode"
                       #twoFactorCodeInput="ngModel" type="text">
            </div>
            <span class="remaining-time-counter">
                {{"RemainingTime" | localize}}: {{securityQuestionsDisplayMinutes | number : '1.0-0'}}:{{securityQuestionsDisplaySeconds | number : '2.0-0'}}.
            </span>
        </div>
    </form>
    <div class="row-flex-space margin-top">
        <button id="securityQuestionsCancel" pButton type="button" (click)="resetSecurityQuestionsForm()" [disabled]="savingSecurityQuestions" label="{{l('Cancel')}}"></button>
        <button id="securityQuestionsSave" pButton type="button" (click)="saveSecurityQuestions()" [disabled]="savingSecurityQuestions" label="{{l('Save')}}"></button>
    </div>
</div>
