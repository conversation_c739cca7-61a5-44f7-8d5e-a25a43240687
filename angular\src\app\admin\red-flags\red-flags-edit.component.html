<ngFormValidation #settingFormValidation></ngFormValidation>

<p-card class="p-card-properties_form">
    <p-header class="row-flex-space">
        <p *ngIf="'Ca.RedFlagSettings.Update' | permission">{{l("Edit Setting")}}</p>
        <p *ngIf="!('Ca.RedFlagSettings.Update' | permission)">{{l("View Setting")}}</p>
        
    </p-header>
    <div class="row-flex-space">
        <div class="col-flex rf-form-container">
            <form #settingForm="ngForm">
                <div>
                    <label for="Category">{{"Category" | localize}}</label>
                    <div>
                        <input id="Category" #categoryInput="ngModel" type="text" disabled name="Category" class="form-control" [(ngModel)]="redFlagSetting.category">
                    </div>
                </div>
                <div>
                    <label for="Title">{{"Event Type" | localize}} *</label>
                    <div>
                        <input id="Title" #titleInput="ngModel" type="text" disabled name="Title" class="form-control" [(ngModel)]="redFlagSetting.title">
                    </div>
                </div>
                <div>
                    <label for="DisplayOrder">{{"Display Priority" | localize}} *</label>
                    <div [ngClass]="{ 'input-error': hasError('DisplayPriority') }">
                        <input id="DisplayOrder" #displayOrderInput="ngModel" type="text" name="DisplayPriority" class="form-control" [(ngModel)]="redFlagSetting.displayOrder" required>
                    </div>
                </div>
                <div>
                    <div [ngClass]="{ 'input-error': hasError('Description') }">
                        <textarea id="Description" #descriptionInput="ngModel" rows="4" name="Description" class="form-control" [(ngModel)]="redFlagSetting.description" required>
                        </textarea>
                    </div>
                </div>
                <div *ngIf="redFlagSetting.parameterType != noneParameterType">
                    <label for="Parameter">{{"Value" | localize}} *</label>
                    <div [ngClass]="{ 'input-error': hasError('Value') }">
                        <input id="Parameter" #parameterInput="ngModel" type="text" name="Value" class="form-control" [(ngModel)]="redFlagSetting.parameter" required>
                    </div>
                </div>
                <div>
                    <label for="IsActive">{{"Active" | localize}} *</label>
                    <input id="IsActive" #isActiveInput="ngModel" type="checkbox" name="IsActive" style="padding-left:5px" [(ngModel)]="redFlagSetting.isActive">
                </div>
            </form>

            <div *ngIf="redFlagSettingId != null && 'Ca.RedFlagSettings.Update' | permission" class="row-flex margin-top">
                <button id="redFlagsEditCancel" pButton type="button" (click)="clear()"
                        label="{{l('Cancel')}}" class="ui-button-rounded ui-button-secondary"></button>
                <button id="redFlagsEditSave" pButton type="button" (click)="save()"
                        label="{{l('Save')}}" class="ui-button-rounded ui-button-warning margin-left"></button>
            </div>
        </div>
    </div>
</p-card>

<entityChangeHistoryModal #entityChangeHistoryModal></entityChangeHistoryModal>
