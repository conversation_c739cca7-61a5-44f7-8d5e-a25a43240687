import {
    Component,
    Injector,
    Input,
    OnInit,
    HostListener,
    ViewChild,
} from "@angular/core";
import { AppComponentBase } from "@shared/common/app-component-base";
import {
    AssessmentActionType,
    CaEconomicSubstanceAuditDto,
    WrokflowServiceProxy,
    EconomicSubstanceDeclarationDto,
    ESCAAssessmentInputDto,
    AuditEventServiceProxy,
    EsCaAssessmentCommentsDto,
    CorporateEntityDto,
    EconomicSubstanceInformationRequiredDto,
    ESCAAssessmentDto,
    UserListDto,
    DocumentsDto,
    CASearchServiceServiceProxy,
} from "@shared/service-proxies/service-proxies";
import { Location } from "@angular/common";
import { DialogService } from "primeng/api";
import { DisplayCaReviewCommentComponent } from "@app/economicsubstance/es-component/display-ca-review-comment.component/display-ca-review-comment.component";
import { AppConsts } from "@shared/AppConsts";
import * as _ from "lodash";
import { PrintService } from "@shared/services/print.service";
import { NgFormValidationComponent } from "@shared/utils/validation/ng-form-validation.component";
import { FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
    EconomicDocumentsName,
    RelaventActivityReviewStatus,
} from "@app/economicsubstance/EconomicSubstance";
import { forkJoin, Observable } from "rxjs";
import { CaReopenDialogComponent } from "./ca-reopen-dialog/ca-reopen-dialog.component";

@Component({
    selector: "ca-review",
    templateUrl: "./ca-review.component.html",
    styleUrls: ["./ca-review.component.less"],
})
export class CaReviewComponent extends AppComponentBase implements OnInit {
    @Input() corporateEntity: CorporateEntityDto;
    @Input() currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    @Input() esassessment: ESCAAssessmentDto;
    @Input() esUserlist: UserListDto[];
    @Input() ctspId: any;
    @Input() relevantActivityStatus: RelaventActivityReviewStatus[];

    @Input() informationRequired: EconomicSubstanceInformationRequiredDto;
    @Input()
    informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[];

    @Input() redFlagEventResult: string[];
    escaassessment: ESCAAssessmentInputDto = new ESCAAssessmentInputDto();
    currentComments: EsCaAssessmentCommentsDto =
        new EsCaAssessmentCommentsDto();

    escaassessmentInputDto: ESCAAssessmentInputDto =
        new ESCAAssessmentInputDto();
    ESCAAssessmentDocumentsDoc: any;

    // invalidDates: Array<Date>
    minimumDate: Date;

    @ViewChild("esessmentform", { static: true }) esessmentform: FormGroup;
    @ViewChild("esReviewFormValidation", { static: true })
    esReviewFormValidation: NgFormValidationComponent;

    @ViewChild(CaReopenDialogComponent, { static: false }) reopenModel;

    constructor(
        injector: Injector,
        private router: Router,
        private route: ActivatedRoute,
        private location: Location,
        private _wrokflowServiceProxy: WrokflowServiceProxy,
        private _auditEventService: AuditEventServiceProxy,
        private _caSearchServiceServiceProxy: CASearchServiceServiceProxy,
        private _dialogService: DialogService,
        public printService: PrintService
    ) {
        super(injector);
    }

    @HostListener("window:popstate", ["$event"])
    onPopState(event) {
        // on browser back button press
        this.setAssessmentFilterState();
    }

    ngOnInit() {
        this.esReviewFormValidation.formGroup = this.esessmentform;
        this.escaassessmentInputDto.action = AssessmentActionType.AddComment;
        this.ESCAAssessmentDocumentsDoc =
            EconomicDocumentsName.ESCAAssessmentDocumentsDoc;

        if (!this.currentComments.escaAssessmentDocuments)
            this.currentComments.escaAssessmentDocuments = [];

        if (!this.currentComments.esCaCspInformationRequestDocuments)
            this.currentComments.esCaCspInformationRequestDocuments = [];

        this.minimumDate = new Date();
    }

    SaveAssessment(auditType: any) {
        let self = this;
        self.escaassessmentInputDto.id = self.esassessment.id;
        self.escaassessmentInputDto.esReviewStatus =
            self.esassessment.esReviewStatus;
        self.escaassessmentInputDto.esReviewStatusGuid =
            self.esassessment.esReviewStatusGuid;
        self.escaassessmentInputDto.assessmentLevel =
            self.esassessment.assessmentLevel;
        self.escaassessmentInputDto.prTreatmentSubmissionDateString =
            self.esassessment.prTreatmentDueDateString;
        self.escaassessmentInputDto.penaltyApplied =
            self.esassessment.penaltyApplied;
        self.escaassessmentInputDto.economicSubstanceId =
            self.currentEconomicSubstance.id;
        self.escaassessmentInputDto.comments = self.esassessment.comments;
        self.escaassessmentInputDto.ctspId = self.ctspId;
        // What does this do????
        if (self.currentComments.escaAssessmentDocuments)
            self.escaassessmentInputDto.escaAssessmentDocuments =
                self.currentComments.escaAssessmentDocuments;
        if (self.currentComments.esCaCspInformationRequestDocuments)
            self.escaassessmentInputDto.esCaCspInformationRequestDocuments =
                self.currentComments.esCaCspInformationRequestDocuments;

        self._wrokflowServiceProxy
            .updateESReviewStatus(self.escaassessmentInputDto)
            .subscribe((result) => {
                // lets Audit here
                if (auditType === 1) {
                    let auditInput = new CaEconomicSubstanceAuditDto();
                    auditInput.ctspNumber = self.ctspId;
                    auditInput.id = self.currentEconomicSubstance.id;

                    self._auditEventService
                        .auditCaProvisionalAssesmentDeclarationEvent(auditInput)
                        .subscribe();
                } else if (auditType === 5) {
                    let auditInput = new CaEconomicSubstanceAuditDto();
                    auditInput.ctspNumber = self.ctspId;
                    auditInput.id = self.currentEconomicSubstance.id;
                    self._auditEventService
                        .auditCaCloseAssessmentDeclarationEvent(auditInput)
                        .subscribe();
                } else {
                    let auditInput = new CaEconomicSubstanceAuditDto();
                    auditInput.ctspNumber = self.ctspId;
                    auditInput.id = self.currentEconomicSubstance.id;

                    self._auditEventService
                        .auditCaUpdateAssessmentDeclarationEvent(auditInput)
                        .subscribe();
                }
                forkJoin([
                    self._caSearchServiceServiceProxy.getEconomicSubstanceDetailById(
                        self.ctspId,
                        self.currentEconomicSubstance.id
                    ),
                    self._wrokflowServiceProxy.getESAssessment(
                        self.ctspId,
                        self.currentEconomicSubstance.id,
                        undefined
                    ),
                ]).subscribe((responseList) => {
                    // What is this for???
                    self.currentComments.escaAssessmentDocuments = null;
                    self.currentComments.esCaCspInformationRequestDocuments =
                        null;
                    self.currentEconomicSubstance = responseList[0];
                    self.esassessment = responseList[1];
                    self.esassessment.currentAssignedUser =
                        self.esUserlist.find(
                            (x) => x.userName == self.esassessment.reviewerName
                        );
                    self.esessmentform.reset({
                        userlistid: self.esassessment.currentAssignedUser,
                        Evidenceduedate:
                            self.esassessment.prTreatmentDueDateString,
                    });
                });
            });
    }
    onUpdate() {
        let self = this;

        // need to validate it here
        if (!self.esReviewFormValidation.isFormValid()) {
            return;
        }

        // update 3 cases
        // assign user need to add comments
        // add comments by itself
        // provisiola treatment accept or reject
        // add doumnets need to ad comments to save

        // need to check if the provisional treatment due date was changed then set
        // AssessmentActionType to make decision insted of add comment

        self.escaassessmentInputDto.action =
            self.currentEconomicSubstance.approveRejectProvisionalTreatment &&
            self.currentEconomicSubstance.prTreatmentDueDateString !==
                self.esassessment.prTreatmentDueDateString
                ? AssessmentActionType.MakeDecision
                : self.escaassessmentInputDto.action;

        switch (self.escaassessmentInputDto.action) {
            case AssessmentActionType.MakeDecision: {
                self.escaassessmentInputDto.approveRejectProvisionalTreatment =
                    self.esassessment.approveRejectProvisionalTreatment;
                self.escaassessmentInputDto.economicSubstanceId =
                    self.currentEconomicSubstance.id;

                // we don't need to show confirmation message if we need only to
                // update the due date
                if (
                    self.currentEconomicSubstance
                        .approveRejectProvisionalTreatment
                ) {
                    self.SaveAssessment(0);
                    break;
                }
                // nee to show th confirmation otherwise
                abp.message.confirm(
                    AppConsts.messageList.ESReopenProvisionalDeclaration,
                    "",
                    function (isConfirmed) {
                        if (isConfirmed) {
                            if (
                                self.esassessment
                                    .approveRejectProvisionalTreatment
                            ) {
                                self.SaveAssessment(1);
                            } else self.reopenDeclaration(false);
                        }
                    }
                );
                break;
            }

            case AssessmentActionType.AssignUser: {
                self.SaveAssessment(2);

                break;
            }

            case AssessmentActionType.AddComment: {
                self.SaveAssessment(3);

                break;
            }
        }
    }
    onExit() {
        this.setAssessmentFilterState();
        this.location.back();
    }

    setAssessmentFilterState() {
        // set assessment filter's previous state
        this.topbarService.assessmentListFilterState =
            this.topbarService.assessmentListFilterPreviousState;
    }

    onPrint() {
        this.printService.print();
    }

    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                display: "none",
            };
        }

        return {};
    }

    onShowComments() {
        this._wrokflowServiceProxy
            .getCaReviewComments(
                this.ctspId,
                this.esassessment.economicSubstanceDeclarationId
            )
            .subscribe((data) => {
                this._dialogService.open(DisplayCaReviewCommentComponent, {
                    data: {
                        escaAssessmentCommentsDto: data,
                        ctspId: this.ctspId,
                    },
                    header: this.l("Assessment History"),
                    width: "80%",
                    style: { overflow: "auto" },
                });
            });
    }

    onRequireResubmission() {
        this.reopenDeclaration(true);
    }

    reopenDeclaration(displayConfirmationMessage: boolean) {
        let self = this;
        self.escaassessmentInputDto.ctspId = self.ctspId;
        self.escaassessmentInputDto.id = self.currentEconomicSubstance.id;
        self.escaassessmentInputDto.economicSubstanceId =
            self.currentEconomicSubstance.id;

        let auditInput = new CaEconomicSubstanceAuditDto();
        auditInput.ctspNumber = self.ctspId;
        auditInput.id = self.currentEconomicSubstance.id;

        // ask for confirmation before submitting
        if (displayConfirmationMessage) {
            // need to change to show dialog box with required comment
            self.reopenModel.show(self.escaassessmentInputDto);
        } else {
            self._auditEventService
                .auditCaRequireResubmissionDeclarationEvent(auditInput)
                .subscribe();
            self._wrokflowServiceProxy
                .resubmissionEconomic(self.escaassessmentInputDto)
                .subscribe((x) => {
                    self.location.back();
                });
        }
    }

    /// repoen Emit Function

    reopenSubmit(item: any) {
        let auditInput = new CaEconomicSubstanceAuditDto();
        auditInput.ctspNumber = this.ctspId;
        auditInput.id = this.currentEconomicSubstance.id;
        auditInput.commentsToCSP = item.comments;

        this._auditEventService
            .auditCaRequireResubmissionDeclarationEvent(auditInput)
            .subscribe();
        this._wrokflowServiceProxy.resubmissionEconomic(item).subscribe((x) => {
            this.location.back();
        });
    }

    ApproveRejectPT(item: any) {
        this.escaassessmentInputDto.action = AssessmentActionType.MakeDecision;
        this.escaassessmentInputDto.approveRejectProvisionalTreatment =
            this.esassessment.approveRejectProvisionalTreatment;
    }

    assessmentGetUpdated(event: any) {
        let self = this;
        if (event) {
            forkJoin([
                self._caSearchServiceServiceProxy.getEconomicSubstanceDetailById(
                    self.ctspId,
                    self.currentEconomicSubstance.id
                ),
                self._wrokflowServiceProxy.getESAssessment(
                    self.ctspId,
                    self.currentEconomicSubstance.id,
                    undefined
                ),
            ]).subscribe((responseList) => {
                self.currentEconomicSubstance = responseList[0];
                self.esassessment = responseList[1];
                if (typeof self.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined") {
                    self.informationRequired = null;
                    self.informationRequiredsHistory = null;
                }
                else {
                    let result = self.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
                    if (result != null && result.length > 1) {
                        self.informationRequired = result[0];
                        self.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.id !== this.informationRequired.id);
                    }
                    else {
                        self.informationRequired = null
                        self.informationRequiredsHistory = self.currentEconomicSubstance.economicSubstanceInformationRequired;
                    }
                }
                self.esassessment.currentAssignedUser = self.esUserlist.find(
                    (x) => x.userName == self.esassessment.reviewerName
                );
            });
        }
    }

    SetAssignedName(event: any) {
        this.escaassessmentInputDto.action = AssessmentActionType.AssignUser;
        this.escaassessmentInputDto.assignedUser = event.value.userName;
        // Added for TFS 2095
        if (
            this.escaassessmentInputDto.assignedUser ===
                this.appSession.user.userName &&
            !this.esassessment.comments
        ) {
            this.esassessment.comments = "Assigned assessment to self.";
        }
    }

    onClose() {
        let self = this;

        abp.message.confirm(
            AppConsts.messageList.ESClosedDeclaration,
            "",
            function (isConfirmed) {
                if (isConfirmed) {
                    self.escaassessmentInputDto.action =
                        AssessmentActionType.Closed;
                    self.esassessment.comments =
                        "The status of the declaration set to closed";
                    self.SaveAssessment(5);
                }
            }
        );
    }
}
