<div bsModal #qualificationmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="qualificationmodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Employee Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #qualificationform="ngForm">
                    <p-card>
                        <ngFormValidation #qualificationFormValidation></ngFormValidation>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Name<span class="required">*</span></label>
                                <input *ngIf="!importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.name" required maxlength="150"
                                       name="Name" id="qualid">

                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.name" required 
                                       name="Name2" id="qualid2">
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Qualification <span class="required">*</span></label>
                                <input *ngIf="!importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.qualification" required maxlength="100"
                                       name="Qualification" id="qualqualificationid">

                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="details.qualification" required 
                                       name="Qualification2" id="qualqualificationid2">

                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Years of relevant experience <span class="required">*</span></label>
                                <p-spinner *ngIf="!importOnlyMode" size="25" [min]="0" [max]="999999999999999.99" [step]="0.25" precision="2"
                                           [(ngModel)]="details.yearRelevantExperience" (keypress)="maskDecimal($event, 2, 2)"
                                           required name="YearOfExperience" id="yearsofexpid"></p-spinner>

                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control"
                                       [(ngModel)]="details.yearRelevantExperienceString" name="YearOExperiencestring" id="yearsofexpstringid" required>

                            </div>
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
