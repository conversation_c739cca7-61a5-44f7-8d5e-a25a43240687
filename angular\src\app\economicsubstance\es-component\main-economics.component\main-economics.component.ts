import { Location } from '@angular/common';
import { Component, HostListener, Injector, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ESActionStatus, ESRouting, EntityDescription, SubmittedLocation } from '@app/economicsubstance/EconomicSubstance';
import { EconomicSubstanceService, EsAction } from '@app/economicsubstance/economicsubstance.service';
import { SharedComponent } from '@app/economicsubstance/sharedfunctions';
import { ComponentCanDeactivate } from '@app/shared/common/auth/auth-route-guard';
import { AppConsts } from '@shared/AppConsts';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    CorporateEntityDto, CorporateEntityServiceProxy, EconomicSubstanceDeclarationDto, EconomicSubstanceServiceProxy, RelevantActivity,
    EconomicSubstanceStepDto, LookUpMainServiceProxy, RelevantActivityDetailDto, ValidationResultMainDto, CIGALookUpDto,
    AuditEventServiceProxy, SingleFieldInputOfGuid, AddressDto, CountryDto, CurrencyDto, EconomicSubstanceStatus, EntityType, GetEntitiesSearchInput, EsEntityDetailDto
} from '@shared/service-proxies/service-proxies';
import * as _ from 'lodash';
import { MessageService } from 'primeng/api';
import { forkJoin, Observable } from 'rxjs';
import { Address } from 'cluster';
import * as moment from 'moment';
import { PrintService } from '@shared/services/print.service';
import { downloadFile } from '@app/main/viewmodel/utils';
import { isNullOrUndefined } from 'util';

@Component({

    templateUrl: './main-economics.component.html',
    styleUrls: ['./main-economics.component.css']
})
export class MainEconomicsComponent extends AppComponentBase implements OnInit, OnDestroy, ComponentCanDeactivate {

    esroutinglocal: ESRouting;



    economicSubstanceStepDto: EconomicSubstanceStepDto = new EconomicSubstanceStepDto();

    economicSubstanceDeclarationOldValue: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();
    //new varaibale

    currentEconomicSubstance: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();


    //isLoading: boolean;

    corporateEntity: CorporateEntityDto;
    selectedActivity: RelevantActivityDetailDto[] = [];

    corporateEntityAddress: AddressDto;

    currencies: CurrencyDto[];
    countries: CountryDto[];
    euListcountries: CountryDto[];
    cigaLookup: CIGALookUpDto[];
    headerValues: any;
    licensedUnderAct: any;
    fiscalyearResult: any;
    registeredAddress: any;

    localFiscalStartDate: any;
    localFiscalEndDate: any;

    country: CountryDto;
    head1: string;
    head2: string;
    fileId: any;
    oldSubmissionDate: any;
    esEntityDetail: EsEntityDetailDto;

    constructor(injector: Injector,
        private router: Router, private location: Location, private route: ActivatedRoute,
        private _lookUpMainServiceServiceProxy: LookUpMainServiceProxy,
        private _economicSubstanceServiceProxy: EconomicSubstanceServiceProxy,
        private _corporateEntityServiceProxy: CorporateEntityServiceProxy,
        private _sharedComponet: SharedComponent,
        private _messageService: MessageService,
        public _economicSubstanceService: EconomicSubstanceService,
        private _auditEventService: AuditEventServiceProxy,
        public printService: PrintService
    ) {
        super(injector);
        this.esroutinglocal = new ESRouting();



        this.route.params.subscribe(x => {

            this.esroutinglocal.id = x.entityid;
            this.esroutinglocal.action = x.action;

            if (x.action === '3') {
                this.fileId = x.uploadid;
            }
        })

        // need to define for both add and edit

        this.callTheMainServices(this.esroutinglocal);
    }

    // @HostListener allows us to also guard against browser refresh, close, etc.
    @HostListener('window:beforeunload')
    canDeactivate(): Observable<boolean> | boolean {
        // TODO:
        // insert logic to check if there are pending changes here;
        // returning true will navigate without confirmation
        // returning false will show a confirm dialog before navigating away
        // return this._sharedComponet.CompareOldNewValue(this.es_step1.economicSubstanceDeclarationDto, this.economicSubstanceDeclarationOldValue);
        // for now
        return true;
    }

    @HostListener("window:afterprint", [])
    onWindowAfterPrint() {
        if (this.currentEconomicSubstance && this.currentEconomicSubstance.id) {
            let auditInput = new SingleFieldInputOfGuid();
            auditInput.value = this.currentEconomicSubstance.id;
            this._auditEventService.auditCtspEconomicSubstancePrintEvent(auditInput).subscribe();
        }
    }

    getButtonBarStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'display': 'none',
            };
        }

        return {};
    }

    getDeclarationFormStyle(isPrintMode: boolean) {
        if (isPrintMode) {
            return {
                'max-height': 'initial',
            };
        }

        return {};
    }

    isCtspPoratal() {
        if (AppConsts.isCtspPortal) return true;
    }

    generateExcel() {

        //Inilitialize
        var request = new GetEntitiesSearchInput();

        if (this.currentEconomicSubstance && this.currentEconomicSubstance.id) {
            request.corporateEntityId = this.currentEconomicSubstance.corporateEntityId;
            request.economicDeclarationId = this.currentEconomicSubstance.id;
            request.financialPeriodEndYear = parseInt(moment(this.currentEconomicSubstance.fiscalEndDate).format("YYYY"));
            request.includeRequiringDeclaration = true;
            request.skipCount = 0;
            this._corporateEntityServiceProxy.generateExportFile(request).subscribe(x => {
                if (x.content != null && x.fileName != null) {
                    downloadFile(x.content, x.fileName)
                }

            });
        }
    }

    //call the services
    callTheMainServices(esroutinglocal: ESRouting) {
        //this.isLoading = true;


        this.corporateEntity = new CorporateEntityDto();


        this.corporateEntityAddress = new AddressDto();
        // update ES

        this.esEntityDetail = new EsEntityDetailDto();

        if (esroutinglocal.action.toString() === '2' || esroutinglocal.action.toString() === '3') {
            forkJoin([
                // difference
                this._economicSubstanceServiceProxy.getEconomicSubstanceDetailById(esroutinglocal.id, undefined, true),
                this._lookUpMainServiceServiceProxy.getAllCountries(undefined, undefined),
                this._lookUpMainServiceServiceProxy.geTCIGALookUp(0),
                this._lookUpMainServiceServiceProxy.getAllCurrencies(undefined),
                this._lookUpMainServiceServiceProxy.getEUCountryList(undefined)
            ]).subscribe(responseList => {
                this.currentEconomicSubstance = responseList[0];
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, responseList[0]);

                this.corporateEntity = responseList[0].corporateEntity;

                this.countries = this._sharedComponet.sort_country(responseList[1]);
                this.country = this.countries.find(x => x.countryCode == 'VGB');

                this.cigaLookup = responseList[2];
                this.currencies = this._sharedComponet.sort_currency(responseList[3]);

                this.euListcountries = this._sharedComponet.sort_country(responseList[4], false);

                this.localFiscalStartDate = _.cloneDeep(this.currentEconomicSubstance.fiscalStartDateString);
                this.localFiscalEndDate = _.cloneDeep(this.currentEconomicSubstance.fiscalEndDateString);

                // keep the old value
                this.economicSubstanceDeclarationOldValue = _.cloneDeep(this.currentEconomicSubstance);

                this.handleSubmissionDate(this.currentEconomicSubstance.submissionDate);

                this.esEntityDetail = responseList[0].esEntityDetails.find(x => x.economicSubstanceDeclarationId == esroutinglocal.id);

                if (this.esEntityDetail === undefined) {
                    this.esEntityDetail = new EsEntityDetailDto();
                    this.esEntityDetail.corporateEntityId = this.corporateEntity.id;
                }
            });

            let auditInput = new SingleFieldInputOfGuid();
            auditInput.value = this.esroutinglocal.id;
            this._auditEventService.auditCtspEconomicSubstanceViewEvent(auditInput).subscribe();
        }
        if (esroutinglocal.action.toString() === '1') {
            // adding new ES

            forkJoin([
                //coperative
                this._corporateEntityServiceProxy.getEntity(esroutinglocal.id),
                this._lookUpMainServiceServiceProxy.getAllCountries(undefined, undefined),
                this._lookUpMainServiceServiceProxy.geTCIGALookUp(0),
                this._lookUpMainServiceServiceProxy.getAllCurrencies(undefined),
                this._economicSubstanceServiceProxy.getInitialRelevantActivity(esroutinglocal.id),
                this._lookUpMainServiceServiceProxy.getEUCountryList(undefined)

            ]).subscribe(responseList => {
                this.corporateEntity = responseList[0];

                this.countries = this._sharedComponet.sort_country(responseList[1]);
                this.cigaLookup = responseList[2];
                this.currencies = this._sharedComponet.sort_currency(responseList[3]);
                this.currentEconomicSubstance.corporateEntityId = esroutinglocal.id;
                // need to clon the relevantactivity and also get the first and last date

                this.currentEconomicSubstance = responseList[4];
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, responseList[4]);

                this.currentEconomicSubstance.id = undefined;
                this.currentEconomicSubstance.fiscalStartDate = undefined;
                this.currentEconomicSubstance.fiscalEndDate = undefined;
                this.euListcountries = this._sharedComponet.sort_country(responseList[5], false);
                this.country = this.countries.find(x => x.countryCode == 'VGB');

                this.localFiscalStartDate = _.cloneDeep(this.currentEconomicSubstance.fiscalStartDateString);
                this.localFiscalEndDate = _.cloneDeep(this.currentEconomicSubstance.fiscalEndDateString);


                this.economicSubstanceDeclarationOldValue = _.cloneDeep(this.currentEconomicSubstance);

                this.currentEconomicSubstance.status = EconomicSubstanceStatus.Draft;

                this.esEntityDetail.corporateEntityId = esroutinglocal.id;
            });
        }
    }





    onPrint() {
        this.performAction(EsAction.Print);

    }

    ngOnInit() {

        //this.topbarService.notifyTitle(this.l('Economic Substance Declaration'));
        this._economicSubstanceService.initializeEmptyError();

    }

    // perform close 
    onClose() {



        this.performAction(EsAction.Exit);
    }

    handleCancel() {

        if (this.esroutinglocal.action.toString() === '1') {
            this.location.back();
            this.location.back();
        }
        if (this.esroutinglocal.action.toString() === '2') {
            this.location.back();
        }
        if (this.esroutinglocal.action.toString() === '3') {
            this.router.navigate(['app/main/filetriage/' + this.fileId]);
        }
    }


    ngOnDestroy() {
        this.topbarService.clearValues();
    }

    activeIndex: number = 0;
    newStep: number = 0;
    disableButtons: boolean = false;





    steps = [
        { number: 1, label: "Financial Period" },
        { number: 2, label: "Entity Details" },
        { number: 3, label: "Relevant Activity" },
        { number: 4, label: "Tax Residency" },
        { number: 5, label: "Activity Details" },
        { number: 6, label: "Supporting Details" },
        { number: 7, label: "Confirmation" },
    ];

    onSaveDraft() {
        this.performAction(EsAction.Save);
        this.handleSubmissionDate(this.currentEconomicSubstance.submissionDate)
    }

    onNextStep() {
        this.performAction(EsAction.Next);
    }

    onPreviousStep() {
        this.performAction(EsAction.Previous);
    }

    onDiscardDraft() {

        this.performAction(EsAction.Discard);
    }
    onEdit() {
        this.performAction(EsAction.Edit);
    }
    onSubmitForReview() {

        this.performAction(EsAction.Submit);
    }
    enablestep4() { return this.activeIndex == 6; }

    IsDiscardEnabled(): boolean {
        if (this.currentEconomicSubstance.id) return true;
        return false;
    }

    handleSubmissionDate(submissionDate: any) {
        this.oldSubmissionDate = moment(new Date(submissionDate
            .format('YYYY-MM-DD HH:mm:ss') + ' UTC'))
            .local().format('YYYY-MM-DD HH:mm:ss');
    }

    IsNullorUndefined(input: any) {
        return typeof input === "undefined" || input === null;
    }

    determinNextStep(currentIndex): number {
        if (currentIndex === 0) {
            if (moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day')) return 1;
            else currentIndex = 1;
        }

        if (currentIndex === 2) {
            // first condition if the current index ==2 and the user select none then jump to step 5
            let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);
            if (object.length > 0) return 5;
        }

        if (currentIndex === 3) {
            if (!this.IsNullorUndefined(this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) && this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) return 5;
        }


        return currentIndex + 1
    }

    determinPreviousStep(currentIndex): number {
        if (currentIndex === 2) {
            if (moment(this.currentEconomicSubstance.fiscalEndDateString, this.momentDateFormatString).isSameOrAfter(AppConsts.declarationChanges_2_0_Date, 'day')) return 1;
            else currentIndex = 1;
        }

        if (currentIndex === 5) {
            let object = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked && x.releventActivityValue === RelevantActivity.None);
            if (object.length > 0) return 2;
            if (!this.IsNullorUndefined(this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) && this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) return 3;
        }

        return currentIndex - 1
    }

    performAction(action: EsAction) {
        this.disableButtons = true;
        this.economicSubstanceStepDto.stepLevel = this.activeIndex;
        //this.economicSubstanceStepDto.onStore = false;
        this.economicSubstanceStepDto.economicSubstanceDeclarationDto = _.cloneDeep(this.currentEconomicSubstance);


        // clear and push object
        this.economicSubstanceStepDto.economicSubstanceDeclarationDto.esEntityDetails = [];
        this.currentEconomicSubstance.esEntityDetails = [];
        if (this.esEntityDetail !== undefined) {
            this.economicSubstanceStepDto.economicSubstanceDeclarationDto.esEntityDetails.push(this.esEntityDetail);
            this.currentEconomicSubstance.esEntityDetails.push(this.esEntityDetail);
        }
        else {
            this.esEntityDetail = new EsEntityDetailDto();
            this.economicSubstanceStepDto.economicSubstanceDeclarationDto.esEntityDetails.push(this.esEntityDetail);
            this.currentEconomicSubstance.esEntityDetails.push(this.esEntityDetail);

        }

        if (action === EsAction.Submit) {
            let self = this;
            // ask for confirmation before submitting
            abp.message.confirm(
                AppConsts.messageList.EsSubmitConfirmation,
                'Are you sure?',
                function (isConfirmed) {
                    if (isConfirmed) {
                        if (self.currentEconomicSubstance.status == EconomicSubstanceStatus.Draft)
                            self.saveESDraft(EconomicSubstanceStatus.Submitted);

                        if (self.currentEconomicSubstance.status == EconomicSubstanceStatus.ReOpen)
                            self.saveESDraft(EconomicSubstanceStatus.ReSubmitted);

                    }
                }
            );
        }


        if (action === EsAction.Edit) {
            this.newStep = 4;
            this.updateStep();
        }
        // On Save Draft click
        if (action === EsAction.Save) {
            if (this.activeIndex !== 0) this.saveESDraft(this.currentEconomicSubstance.status, false);
            else {

                this._economicSubstanceService.initializeEmptyError();
                this._economicSubstanceServiceProxy.singleStepValidation(this.economicSubstanceStepDto).subscribe((data: ValidationResultMainDto[]) => {
                    if (data) this._economicSubstanceService.notifyValidations(data);
                    this.newStep = this.determinNextStep(this.activeIndex);
                    this.checkErrorAndSave(false);
                },
                    err => {
                        console.error(err);
                    });
            }
        }


        // on Next button click
        if (action === EsAction.Next) {
            this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);



            this._economicSubstanceService.initializeEmptyError();
            this._economicSubstanceServiceProxy.singleStepValidation(this.economicSubstanceStepDto).subscribe((data: ValidationResultMainDto[]) => {
                if (data) this._economicSubstanceService.notifyValidations(data);
                this.newStep = this.determinNextStep(this.activeIndex);
                this.checkErrorAndSave();
            },
                err => {
                    console.error(err);
                });

        }

        // on Previous button click
        if (action === EsAction.Previous) {
            this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
            this.newStep = this.determinPreviousStep(this.activeIndex);

            this.updateStep();
        }

        if (action === EsAction.Print) {
            this.printService.print();
            this.disableButtons = false;
        }
        // on Discard Draft
        if (action === EsAction.Discard) {
            let self = this;
            if (this.currentEconomicSubstance.id) {

                // ask for confirmation before discarding
                abp.message.confirm(
                    AppConsts.messageList.EsDiscardConfirmation,
                    'Are you sure you want to Discard the draft?',
                    function (isConfirmed) {
                        if (isConfirmed) {
                            //abp.ui.setBusy();

                            self.deleteDraft();


                        }
                        else {
                            // console.log("about to disable button")
                            self.disableButtons = false;
                        }
                    }
                );
            }
        }

        // on close and exit
        if (action === EsAction.Exit) {
            // need to check if there is no changes then show up a message otherwise don't
            let self = this;
            if (!_.isEqual(this.currentEconomicSubstance, this.economicSubstanceDeclarationOldValue)) {

                // ask for confirmation before discarding
                abp.message.confirm(
                    AppConsts.messageList.EsClosedConfirmation,
                    ' Are you sure you want to discard these changes?',
                    function (isConfirmed) {
                        if (isConfirmed) {
                            //abp.ui.setBusy();

                            self.handleCancel();


                        }
                        else {
                            // console.log("about to disable button")
                            self.disableButtons = false;
                        }
                    }
                );
            }
            else { self.handleCancel(); }



        }

    }

    onWarningFirstChanged(value: boolean): void {
        this._economicSubstanceService.warningFirst = value;
      }

    checkErrorAndSave(needTochangeStep: boolean = true) {

        if (!this._economicSubstanceService.hasError()) {
            // need to check for the warnning
            if (!this._economicSubstanceService.hasWarning()) {
                // Perform current step related operations

                this.saveESDraft(this.currentEconomicSubstance.status, needTochangeStep);
            }
            else {


                this.disableButtons = false;
                if (!this._economicSubstanceService.warningFirst) {
                    this._economicSubstanceService.warningFirst = true;
                    this.saveESDraft(this.currentEconomicSubstance.status, needTochangeStep);
                }
                else {
                    this._economicSubstanceService.warningFirst = false;
                }
            }
        }
        else {
            // need to scroll to top for user to see validations
            window.scroll(0, 0);
            console.log("Validation error")
            this.disableButtons = false;
        }
    }


    updateStep(needTochangeStep: boolean = true) {
        if (needTochangeStep && this.newStep > -1 && this.newStep < 7) this.activeIndex = this.newStep;
        this._economicSubstanceService.initializeEmptyError();
        this.disableButtons = false;
    }

    deleteDraft() {
        this._economicSubstanceServiceProxy.removeDraftEconomicSubstance(this.currentEconomicSubstance.id, undefined).
            subscribe((data: any) => {
                this.handleCancel();
            }, err => { return false; }
            );
    }


    ClearOldData() {
        if (!this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {
            this.currentEconomicSubstance.jurisdictionTaxResident = null;
            this.currentEconomicSubstance.jurisdictionTaxResidentId = null;
            this.currentEconomicSubstance.doesEntityHaveParentEntity = null;
            this.currentEconomicSubstance.parentEntityName = null;
            this.currentEconomicSubstance.parentEntityAlternativeName = null;
            this.currentEconomicSubstance.parentJurisdiction = null;
            this.currentEconomicSubstance.parentJurisdictionId = null;
            this.currentEconomicSubstance.entityIncorporationNumber = null;
            this.currentEconomicSubstance.isEvidenceNonResidenceOrTreatment = null;
            this.currentEconomicSubstance.taxPayerIdentificationNumber = null;


            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceOfNonResidanceDocument) {
                this.currentEconomicSubstance.evidenceOfNonResidanceDocument.forEach(x => { x.isDeleted = true; })
            }
            if (this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments) {
                this.currentEconomicSubstance.evidenceProvisionalTreatmentDocuments.forEach(x => { x.isDeleted = true; })
            }
        }
        else {
            if (!this.currentEconomicSubstance.doesEntityHaveParentEntity) {
                this.currentEconomicSubstance.parentEntityName = null;
                this.currentEconomicSubstance.parentEntityAlternativeName = null;
                this.currentEconomicSubstance.parentJurisdiction = null;
                this.currentEconomicSubstance.parentJurisdictionId = null;
                this.currentEconomicSubstance.entityIncorporationNumber = null;
            }
        }
        let activities = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
        activities.forEach(x => {
            if (!x.isCarriedForPartFinancialPeriod) { x.startDateString = null; x.endDateString = null; x.startDate = null, x.endDate = null; }
            if (this.currentEconomicSubstance.doesEntityMakeClaimOutisedBVI) {

                x.isActivityDirectedInBVI = null;
                x.noofConductedMeeting = null;
                x.noofMeetingHeldInBVI = null;
                x.isMeetingMinutesInBVI = null;
                x.totalTurnover = null;
                x.totalExpediture = null;
                x.totalExpeditureInBVI = null;
                x.totalNoFullTimeEmployee = null;
                x.totalFullTimeEmployeeInBVI = null;
                x.cigaOtherDetail = null;
                x.hasAnyIncomeBeenOutsourced = null;
                x.wasCIGAOutsourcedInBVI = null;
                x.totalExpenditureIncurredInBVI = null;


                x.doesEntityComplyItsStatutoryObligations = null;
                x.doesEntityManageEquity = null;
                x.doesEntityHaveAdequateEmployee = null;

                // intelect
                x.isLegalEntityHighRisk = null;
                x.doesEntityProvideEvidence = null;
                x.totalGrossAnnualIncome = null;
                x.grossIncomeRoyalities = null;
                x.grossIncomeGains = null;
                x.doesLegalEntityConductCIGA = null;
                x.doesEntityProvideEvidenceroRebut = null;
                x.doesBusinessRequireEquipment = null;
                x.equipmentInJurisdiction = null;
                x.equipmentDescription = null;

                if (x.conductedCIGAActivity) x.conductedCIGAActivity = null;
                if (x.premisesAddress) x.premisesAddress = null;
                if (x.serviceProviders) x.serviceProviders = null;
                if (x.managementDetails) x.managementDetails = null;
                if (x.attendMeetingDetails) x.attendMeetingDetails = null;
                if (x.employeeQualificationDetails) x.managementDetails = null;
                if (x.otherCIGADocuments) x.otherCIGADocuments = null;
                if (x.highRiskDocuments) x.highRiskDocuments = null;

                //BVI Declaration Changes 2.0                
                x.grossIncomeType = null;
                x.totalAssetsValue = null;
                x.netAssetsValue = null;
                x.noofQuorumBoardMeeting = null;
                x.isQuorumBoardMeetingInBVI = null;
                x.totalNoCorporateLegalEmployee = null;
                x.tangibleAsset = null;
                x.tangibleAssetIncome = null;
                x.tangibleAssetEmployeeResponsibility = null;
                x.historyofStrategicDecisionsInBVI = null;
                x.historyofTradingActivityIncome = null;
                x.relevantIPAsset = null;
                x.ipAssetsInBVI = null;
                x.ipAssetsEmployeeResponsibility = null;
                x.concreteEvidenceDecisionInBVI = null;
                x.grossIncomeOthers = null;

                if (x.tangibleAssetIncomeDocuments) x.tangibleAssetIncomeDocuments = null;
                if (x.tangibleAssetEmployeeResponsibilityDocuments) x.tangibleAssetEmployeeResponsibilityDocuments = null;
                if (x.historyofStrategicDecisionsInBVIDocuments) x.historyofStrategicDecisionsInBVIDocuments = null;
                if (x.historyofTradingActivityIncomeDocuments) x.historyofTradingActivityIncomeDocuments = null;
                if (x.ipAssetsInBVIDocuments) x.ipAssetsInBVIDocuments = null;
                if (x.ipAssetsEmployeeResponsibilityDocuments) x.ipAssetsEmployeeResponsibilityDocuments = null;
                if (x.concreteEvidenceDecisionInBVIDocuments) x.concreteEvidenceDecisionInBVIDocuments = null;

            }

        });

        //BVI Declaration Changes 2.0
        this.currentEconomicSubstance.esEntityDetails = [];
    }

    saveESDraft(status: EconomicSubstanceStatus, needTochangeStep: boolean = true) {

        this.ClearOldData();

        this.currentEconomicSubstance.status = status;
        this.currentEconomicSubstance.localSubmissionDate = moment();
        this.currentEconomicSubstance.submissionDate = moment.utc();
        this.currentEconomicSubstance.esEntityDetails.push(this.esEntityDetail);


        if (this.currentEconomicSubstance.id) {

            //abp.ui.setBusy();   
            this._economicSubstanceServiceProxy.updateEconomicSubstance(this.currentEconomicSubstance).subscribe((data: any) => {

                this.currentEconomicSubstance = data;

                this.localFiscalStartDate = _.cloneDeep(this.currentEconomicSubstance.fiscalStartDateString);
                this.localFiscalEndDate = _.cloneDeep(this.currentEconomicSubstance.fiscalEndDateString);


                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, data);
                this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);


                if (status === EconomicSubstanceStatus.Submitted || status === EconomicSubstanceStatus.ReSubmitted) {

                    let submitterLocation = this.esroutinglocal.action.toString() === '1' ? SubmittedLocation.FromNew : SubmittedLocation.FromDraft
                    this.router.navigate(['app/economicsubstance/display/' + this.currentEconomicSubstance.id + '/' + submitterLocation])


                }

                this.economicSubstanceDeclarationOldValue = _.cloneDeep(this.currentEconomicSubstance);
                this.esEntityDetail = _.cloneDeep(this.currentEconomicSubstance.esEntityDetails.find(x => x.economicSubstanceDeclarationId == this.currentEconomicSubstance.id));

                this.updateStep(needTochangeStep);

            },
                err => {
                    this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to Save. Please try again' });

                });
        }
        else {

            //abp.ui.setBusy();
            this._economicSubstanceServiceProxy.addEconomicSubstance(this.currentEconomicSubstance).subscribe((data: any) => {
                this.currentEconomicSubstance = data;
                this.localFiscalStartDate = _.cloneDeep(this.currentEconomicSubstance.fiscalStartDateString);
                this.localFiscalEndDate = _.cloneDeep(this.currentEconomicSubstance.fiscalEndDateString);

                this.selectedActivity = this.currentEconomicSubstance.relevantActivities.filter(x => x.isChecked);
                this._sharedComponet.convertDatetime(this.currentEconomicSubstance, data);

                this.economicSubstanceDeclarationOldValue = _.cloneDeep(this.currentEconomicSubstance);

                this.esEntityDetail = _.cloneDeep(this.currentEconomicSubstance.esEntityDetails.find(x => x.economicSubstanceDeclarationId == this.currentEconomicSubstance.id));

                if (status == EconomicSubstanceStatus.Submitted) this._messageService.add({ severity: 'success', summary: 'Success', detail: 'Submitted' });

                this.updateStep(needTochangeStep);

            },
                err => {
                    this._messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to Save. Please try again' });
                    //this.disableButtons = false;
                    //abp.ui.clearBusy();
                });
        }
        // need to quit after saving we will enable later

        //this.handleCancel()
    }



}
