<div [@routerTransition]>
    <div class="ess-ac-card">

        <div class="ess-ac-card-header">CHANGE PASSWORD</div>

        <div class="ess-ac-card-body">
            <ngFormValidation #resetPassFormValidation displayMode="2"></ngFormValidation>

            <form #resetPassForm="ngForm" method="post" (ngSubmit)="save()">

                <div class="ess-ca-form-control col-flex">
                    <label>{{"NewPassword" | localize}}</label>
                    <input #passwordInput="ngModel" placeholder="Password"
                           [(ngModel)]="model.password" type="password"
                           id="NewPassword" name="NewPassword" required passwordComplexity validateEqual="ConfirmNewPassword" reverse="true"
                           [ngClass]="{ 'ess-ca-error': hasError('NewPassword') }" />
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label>{{"ConfirmNewPassword" | localize}}</label>
                    <input #confirmPasswordInput="ngModel" placeholder="Password"
                           [(ngModel)]="model.passwordRepeat" type="password"
                           id="ConfirmNewPassword" name="ConfirmNewPassword" required validateEqual="NewPassword"
                           [ngClass]="{ 'ess-ca-error': hasError('ConfirmNewPassword') }" />
                </div>

                <div class="ess-ca-form-control col-flex" *ngIf="model.hasTwoFactorCode">
                    <label for="TwoFactorCode">{{"Verification Code" | localize}}</label>
                    <input id="resetPasswordTwoFactorCode" #twoFactorCodeInput="ngModel" placeholder="Code"
                           [(ngModel)]="model.twoFactorCode" type="text"
                           name="TwoFactorCode">
                    <span>
                        Time Remaining: <strong>{{displayMinutes | number : '1.0-0'}}:{{displaySeconds | number : '2.0-0'}}</strong>
                    </span>
                </div>

                <div class="ess-ca-form-control">
                    <button id="resetPasswordSubmit" pButton [disabled]="!resetPassForm.form.valid" type="submit" label="Continue"
                            class="ui-button-rounded ui-button-warning"></button>
                </div>

                <div class="ess-ca-form-control row-flex-center" >
                    <span class="ess-ac-password-requirements">
                        <span innerHTML="{{l('PasswordRequirementsHtml')}}"></span>
                    </span>
                </div>

                <div class="ess-ca-form-control">
                    <bdo-needhelp></bdo-needhelp>
                </div>
            </form>
        </div>
    </div>
</div>
