<div class="col-flex">
    <!--<form #step4="ngForm">-->
    
     <div *ngIf="!economicsubstance.doesEntityMakeClaimOutisedBVI">
         <div *ngFor="let item of economicsubstance.relevantActivities">
             <div *ngIf="item.releventActivityValue !== 10">
                 <div *ngIf="item.isChecked">
                     <app-relevant-activity
                     [headingSeq]="headingListSeq[item.releventActivityValue-1]"
                     [economicsubstance]="economicsubstance"
                     [economicsubstanceOld]="economicsubstanceOld"
                     [corporateEntity]="corporateEntity"
                     [index]="item.releventActivityValue-1"
                     [country]="country"
                     [CIGAlookup]="getLookup(item.releventActivityValue-1)"
                     [sectionEnablement]="getItems(item.releventActivityValue)"
                     [currency]="currency"
                     [readOnlyMode]="readOnlyMode"
                     [displayFromCa]="displayFromCa"
                     [importOnlyMode]="importOnlyMode"
                     [esassessment]="esassessment"
                     [relevantActivityStatus]="relevantActivityStatus"
                     [historyDisplay]="historyDisplay"
                     [ctspId]="ctspId">
                    </app-relevant-activity>
                 </div>
             </div>
             </div>
     </div>
</div>
