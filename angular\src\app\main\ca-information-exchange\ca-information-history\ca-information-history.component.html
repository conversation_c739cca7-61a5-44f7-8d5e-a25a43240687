<div bsModal #informationExchageHistoryModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
     aria-labelledby="informationExchageHistoryModal"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <span>{{"Information Excchange History" | localize}}</span>
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="primeng-datatable-container" [busyIf]="primengTableHelper.isLoading">
                    <p-table #eshistoryDataTable [value]="primengTableHelper.records"
                             rows="{{primengTableHelper.defaultRecordsCountPerPage}}"
                             [paginator]="false" [lazy]="true"
                             [scrollable]="true" ScrollWidth="100%">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 75px">
                                    {{'Entity Name' | localize}}
                                </th>
                                <th style="width: 75px">
                                    {{'Incorporation#/Formation#' | localize}}
                                </th>
                                <th style="width: 75px">
                                    {{'Status' | localize}}
                                </th>
                                <th style="width: 75px">
                                    {{'Created At' | localize}}
                                </th>
                                <th style="width: 75px">
                                    {{'Action' | localize}}
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit">
                            <tr [pSelectableRow]="record">
                                <td style="width: 75px">
                                    {{record.informationExchage.entityName}}
                                </td>
                                <td style="width: 75px">
                                    {{record.informationExchage.companyFormationNumber}}

                                </td>
                                <td style="width: 75px">
                                    {{record.informationExchage.informationExchangeStatus |informationeschangestatus}}
                                </td>
                                <td style="width: 75px">

                                    {{returnDate(record.createdAt)  | date: "yyyy-MM-dd HH:mm:ss"}}
                                </td>
                                <td style="width: 75px">
                                    <a (click)="viewHistrory(record)"><i class="pi pi-search"></i></a>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="primengTableHelper.totalRecordsCount == 0">
                        {{'NoData' | localize}}
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="primengTableHelper.defaultRecordsCountPerPage"
                                     #eshistoryPaginator
                                     (onPageChange)="getRecords($event)"
                                     [totalRecords]="primengTableHelper.totalRecordsCount">
                        </p-paginator>
                        <span class="total-records-count">
                            {{'TotalRecordsCount' | localize:primengTableHelper.totalRecordsCount}}
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" (click)="close()">{{"Close" | localize}}</button>
            </div>
        </div>
    </div>
</div>


