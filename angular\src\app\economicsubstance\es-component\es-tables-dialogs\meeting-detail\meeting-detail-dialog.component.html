<div bsModal #meetingmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="meetingmodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Meeting Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #meetingform="ngForm">
                    <p-card>
                        <ngFormValidation #meetingFormValidation></ngFormValidation>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Meeting # <span class="required">*</span></label>

                                <p-spinner *ngIf="!importOnlyMode" [(ngModel)]="meetingDetails.meetingNo" [min]="0" [max]="999" name="Meeting" id="meetingDetailsNoid" required maxlength="3"></p-spinner>

                                <input *ngIf="importOnlyMode" pInputText type="text" class="form-control" [(ngModel)]="meetingDetails.meetingNoString" name="Meeting" id="meetingDetailsNoStringid" required >
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Name <span class="required">*</span></label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="meetingDetails.name"  maxlength="150"
                                       name="meetingName" id="meetingDetailsid" required>
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Physically Present? <span class="required">*</span></label>
                                <div class="radio-input">
                                    <div>
                                        <p-radioButton [value]="true" [(ngModel)]="meetingDetails.isPhysicallyPresent" name="isPhysicallyPresent" id="isPhysicallyPresentidy" required>
                                        </p-radioButton>
                                        <span>YES</span>
                                    </div>
                                    <div>
                                        <p-radioButton [value]="false" [(ngModel)]="meetingDetails.isPhysicallyPresent" name="isPhysicallyPresent" id="isPhysicallyPresentidn" required>
                                        </p-radioButton>
                                        <span>NO</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Relation to the entity <span class="required">*</span></label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="meetingDetails.relationToEntity" name="relationToEntityName" id="relationToEntityid" maxlength="100" required>
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Qualification <span class="required">*</span></label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="meetingDetails.qualification" name="qualification" id="qualificationid" maxlength="100" required>
                            </div>
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
