import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-entity-add-page',
  templateUrl: './entity-add-page.component.html',
  styleUrls: ['./entity-add-page.component.css']
})
export class EntityAddPageComponent implements OnInit {

  constructor(private router: Router, private route: ActivatedRoute) {

  }

  ngOnInit() {
  }

  handleCancel() {
    console.log('add new entity cancelled')
  }

  handleReview(id) {
    this.router.navigate(['../searchresult'], {
      relativeTo: this.route, queryParams: {
        type: "SingleEntity",
        id: id
      }
    })
  }

}
