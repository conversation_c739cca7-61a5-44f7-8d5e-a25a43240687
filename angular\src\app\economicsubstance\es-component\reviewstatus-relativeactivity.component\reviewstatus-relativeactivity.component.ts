import { Component, OnInit, Injector } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/api';
import {
    WrokflowServiceProxy, ESCAAssessmentInputDto, RelativeActivityStatusDto, AuditEventServiceProxy,
    AssessmentActionType, RelativeActivitySelection, CaEconomicSubstanceAuditDto, EconomicSubstanceDeclarationDto
} from '@shared/service-proxies/service-proxies';
import { RelativeActivityChange } from '@app/economicsubstance/EconomicSubstance'
import * as moment from 'moment';

@Component({
  selector: 'app-reviewstatus-relativeactivity',
  templateUrl: './reviewstatus-relativeactivity.component.html',
  styleUrls: ['./reviewstatus-relativeactivity.component.css']
})
export class ReviewstatusRelativeactivityComponent extends AppComponentBase implements OnInit
{
    
    escaassessmentInputDto: ESCAAssessmentInputDto = new ESCAAssessmentInputDto();
    economicsubstance: EconomicSubstanceDeclarationDto = new EconomicSubstanceDeclarationDto();
    constructor(injector: Injector,
                public config: DynamicDialogConfig,
                public ref: DynamicDialogRef,
        private _wrokflowServiceProxy: WrokflowServiceProxy,
        private _auditEventService: AuditEventServiceProxy
    )
    {
        super(injector);
        this.escaassessmentInputDto = config.data.value;
        this.economicsubstance = config.data.economicsubstance;
      }

  ngOnInit() {
  }

 onChangeStatus()
 {
     this._wrokflowServiceProxy.updateESReviewStatus(this.escaassessmentInputDto).subscribe(result =>
     {
           // need to audit the relevant activity status
         // need to pass the following
         // the relevant activity id, activity pass fail value
         let auditInput = new CaEconomicSubstanceAuditDto();
         auditInput.ctspNumber = this.escaassessmentInputDto.ctspId;
         auditInput.id = this.economicsubstance.id;
         auditInput.relativeActivityStatus = this.escaassessmentInputDto.currentRelativeActivityStatus;
         this._auditEventService.auditRelevantActivityAssesmentDeclaration(auditInput).subscribe();
         this.ref.close();
     });
 }

    onClose() {
        this.ref.close();
    }
}
