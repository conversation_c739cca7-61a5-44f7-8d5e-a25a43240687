<div bsModal #reopenmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="reopenmodal"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    {{("Reopen Dialog")}}
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">
                <ngFormValidation #reopenFormValidation></ngFormValidation>
                <form #reopenform="ngForm">
                    <div>
                        <label>
                            Please note that the following will be performed if this action is completed: <br>
                            1. System will reopen the declaration for CSP to update and the declaration will only be visible to ITA again after it is resubmitted by CSP.<br>
                            2. The current information on the declaration form can only be reviewed by CA through View Assessment History.
                        </label>
                    </div>
                    <div width="90%">
                        <div class="col-flex-wmargin">
                            <div>
                                <label>{{l("CTSPCommentsForTitle")}} <span class="required">*</span> </label>
                                <textarea pInputTextarea rows="3"
                                          required emptyspace
                                          id="commentid"
                                          name="Comments for CSP"
                                          [(ngModel)]="assessmentDetail.comments">

                                    </textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Yes"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
