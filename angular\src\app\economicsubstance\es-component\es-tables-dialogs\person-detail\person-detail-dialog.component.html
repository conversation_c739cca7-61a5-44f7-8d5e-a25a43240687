
<div bsModal #personalmodal="bs-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="personalmodal"
     (onShown)="shown()"
     aria-hidden="true" [config]="{backdrop: 'static'}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    Personal Details
                </h4>
                <button type="button" class="close" (click)="close()" [attr.aria-label]="l('Close')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form #personalForm="ngForm">
                    <p-card>
                        <ngFormValidation #personFormValidation></ngFormValidation>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Name<span class="required">*</span></label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="personDetails.name" required maxlength="150"
                                       name="Name" id="persondetailid">
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Resident in the Virgin Islands? <span class="required">*</span> </label>
                                <div class="radio-input">
                                    <div>
                                        <p-radioButton [value]="true" [(ngModel)]="personDetails.isResidentInBVI" name="ResidentInBVI" id="isResidentInBVIidy" required>
                                        </p-radioButton>
                                        <span>YES</span>
                                    </div>
                                    <div>
                                        <p-radioButton [value]="false" name="ResidentInBVI" id="isResidentInBVIidn"
                                                       [(ngModel)]="personDetails.isResidentInBVI" required>
                                        </p-radioButton>
                                        <span>NO</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row-flex-justified">
                            <div class="col-flex full-width">
                                <label>Relation to the entity <span class="required">*</span></label>
                                <input pInputText type="text" class="form-control" [(ngModel)]="personDetails.relationToEntity" name="RelationToEntity" id="relationToEntityPid" maxlength="100" required>
                            </div>
                        </div>
                    </p-card>
                </form>
            </div>
            <div class="modal-footer">
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="save()" label="Ok"> </button>
                <button pButton type="button" class="ui-button-rounded btnclass" (click)="close()" label="Cancel"></button>
            </div>
        </div>
    </div>
</div>
