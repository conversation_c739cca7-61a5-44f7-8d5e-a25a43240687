import { Component, OnInit, Injector, NgModule, Injectable, Inject, Optional } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, NgForm } from '@angular/forms';
import { AppComponentBase } from '@shared/common/app-component-base';
import { CaSearchViewModel, CaSearchType } from '../viewmodel/ca-search-viewmodel';
import { Router, ActivatedRoute } from '@angular/router';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import * as moment from 'moment';
import { CtspListDto, CtspServiceProxy, CtspActivateInputDto, EconomicSubstanceSearchServiceProxy, CaSearchInput, CASearchServiceServiceProxy } from '@shared/service-proxies/service-proxies';
import { CTSPListItem } from '../ctsp-list/ctsp-list-item';
import { getRandomKey } from '../viewmodel/utils';
import { ReportModel } from '../ca-home-report/ca-home-report-model';
import { downloadFile, setSearchValueToStorageNewTab } from "@app/main/viewmodel/utils";
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { NgMultiSelectDropDownModule } from "ng-multiselect-dropdown";
import { NotificationService } from '@shared/services/notification-service.service';
import { merge } from 'lodash';
import { Observable, Subscription } from 'rxjs';
import { SignalRMessage } from 'shared/models/signal-rmessage.model';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { Pipe, PipeTransform } from '@angular/core';
import * as internal from 'assert';


// @NgModule({

//   imports: [
//     ToastModule,
//     NgMultiSelectDropDownModule.forRoot()

//   ],
//   providers: [

//   ]
// })

@Component({
  selector: 'app-ca-search',
  templateUrl: './ca-search.component.html',
  styleUrls: ['./ca-search.component.css'],
  animations: [appModuleAnimation()],
})
export class CaSearchComponent extends AppComponentBase implements OnInit {

  viewModel: CaSearchViewModel = new CaSearchViewModel(x => this.l(x));
  supportAdvancedSearch: boolean = true; // temporary disabled it until all advanced search issue fixed
  isAdvancedSearch: boolean = false;
  isReport: boolean = false;
  ctspItems: CTSPListItem[];
  reportModel = new ReportModel();

  downloadMessageSubscription: Subscription;
  connectionEstablishedSub: Subscription;
  connectionIdSubscription: Subscription;

  dropdownList = [];
  selectedItems = [];
  dropdownSettings: IDropdownSettings;
  selectedStatus: [];
  notifications: any[] = [];

  observReport: Observable<number>;

  htmlContent: string = "";
  fileName: string = "";
  displayModal: boolean;
  idConnection: string = "";

  constructor(injector: Injector, private router: Router,
    private route: ActivatedRoute,
    private _ctspServiceProxy: CtspServiceProxy,
    private _economicSubstanceSearchServiceProxy: EconomicSubstanceSearchServiceProxy,
    private _cASearchServiceServiceProxy: CASearchServiceServiceProxy,

    private notificationSignalRService: NotificationService
  ) {
    super(injector);

    this._ctspServiceProxy.getAllCtsps().subscribe(result => {
      this.ctspItems = result.map(x => {
        let item = new CTSPListItem();
        item.id = x.id;
        item.number = x.number;
        item.name = x.name;
        item.isProduction = x.isProduction;
        item.emailAddress = x.emailAddress;
        item.phoneNumber = x.phoneNumber;
        item.itPhoneNumber = x.itPhoneNumber;
        item.comments = x.comments;
        item.active = x.active;
        item.address = x.address;
        item.caCtspName = x.caCtspName;
        return item;
      });
    });
  }

  ngOnInit() {

    this.dropdownSettings = {
      singleSelection: false,
      idField: 'item_id',
      textField: 'item_text',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 3,
      allowSearchFilter: false
    };
  }
  onItemSelect(item: any) {
    console.log(item);
  }
  onSelectAll(items: any) {
    console.log(items);
  }

  setSelectedRAs(selectedRAList: CTSPListItem[]) {
    this.viewModel.essSearch.ctspIds = selectedRAList.map(x => {
      let ss = x.id;
      return ss;
    });
  }


  onSubmit(f: NgForm) {
    const vm = this.viewModel
    //For now we assume there is only one type whichis ESS
    this.viewModel.searchType = CaSearchType.ESS;
    let randomKey = getRandomKey();
    let queryParams;
    queryParams = { type: this.viewModel.searchType, randomKey: randomKey, ...this.viewModel.essSearch };

    const queryParamsFiltered = Object.keys(queryParams).reduce(
      (acc, key) => ((queryParams[key] != null && queryParams[key] !== false) ? { ...acc, [key]: queryParams[key] } : acc), {})
    this.router.navigate(['../casearchresult'], { relativeTo: this.route, queryParams: queryParamsFiltered });
  }

  onAdvSearch(params: any) {
    const vm = this.viewModel
    //For now we assume there is only one type whichis ESS
    this.viewModel.searchType = CaSearchType.ESS;
    let queryParams;
    queryParams = { type: this.viewModel.searchType, ...this.viewModel.essSearch };
    if (this.isAdvancedSearch) {
      // need to make a copy of the params, otherwise JSON.stringify() not works for added ctspIds
      let obj = JSON.parse(JSON.stringify(params))
      obj.ctspIds = this.viewModel.essSearch.ctspIds;
      if (params["isForNewTab"] && params["tabId"]) {        
        this.openNewTab(params);
        return;
      }
      this.setSearchValueToStorage(obj);
      queryParams = { type: params.type, isAdvancedSearch: true };
      const queryParamsFiltered = Object.keys(queryParams).reduce(
        (acc, key) => ((queryParams[key] != null && queryParams[key] !== false) ? { ...acc, [key]: queryParams[key] } : acc), {});

      this.router.navigate(['../casearchresult'], { relativeTo: this.route, queryParams: queryParamsFiltered });
    }
  }

  setSearchValueToStorage(value: any) {
    if (typeof (Storage) !== "undefined") {
      if (value) {
        localStorage.setItem('advancedSearchCriteria', JSON.stringify(value));
        return value;
      }
    }
  }

  openNewTab(criteria: any) {
    criteria.ctspIds = this.viewModel.essSearch.ctspIds;
    const obj = Object.assign({}, criteria);
    setSearchValueToStorageNewTab(obj);
    let queryParams = { type: criteria.type, isAdvancedSearch: true, tabId: criteria["tabId"] };
    const queryParamsFiltered = Object.keys(queryParams).reduce(
      (acc, key) =>
        queryParams[key] != null && queryParams[key] !== false
          ? { ...acc, [key]: queryParams[key] }
          : acc,
      {}
    );
    const url = this.router.serializeUrl(this.router.createUrlTree(["../casearchresult"], {
      relativeTo: this.route,
      queryParams: queryParamsFiltered,
    }));
    window.open(url, '_blank');
  }

  resetform() {
    this.reportModel.CSPNumber = null;
    this.reportModel.EsName = null;
    this.reportModel.FinancialPeriodDate = null;
    this.reportModel.AssessmentStatus = null;
    this.reportModel.RelevantActivity = null;
    this.reportModel.NonResidenceDeclaration = null;
    this.reportModel.SelectedRelevantActivity = null;
  }


  generateESReport() {
    //Inilitialize
    this.displayModal = true;
    const vm = this.viewModel
    this.selectedItems = [];

    let randomKey = getRandomKey();
    let queryParams;
    queryParams = { type: 'ESS', randomKey: randomKey, ...this.viewModel.essSearch }


    if (this.reportModel.SelectedRelevantActivity) {
      this.reportModel.SelectedRelevantActivity.forEach((element) => {
        this.selectedItems.push(element.item_text);
      });
    }


    var connId = this.notificationSignalRService.GetConnectionIdValue();

    var input = new CaSearchInput({
      searchType: queryParams.type.toLowerCase(),
      companyNumber: this.reportModel.CSPNumber ? this.reportModel.CSPNumber.toString() : "",
      entityName: this.reportModel.EsName,
      reviewStatuses: [],
      financialPeriodEndYear: queryParams.year,
      includeRequiringDeclaration: queryParams.includeDeclaration,
      ctspIds: queryParams.ctspIds ? (!Array.isArray(queryParams.ctspIds) ? [queryParams.ctspIds] : queryParams.ctspIds) : null,
      maxResultCount: 0,
      skipCount: 0,
      sorting: '',
      isSearch: false,
      randomKey: queryParams.randomKey,
      financialPeriodEndYearForLess: false,
      relevantActivity: this.selectedItems,
      nonResidenceDeclaration: this.reportModel.NonResidenceDeclaration,
      financialPeriodEndDate: this.reportModel.FinancialPeriodDate,
      fileName: this.idConnection,
      assessmentStatus: this.reportModel.AssessmentStatus == undefined ? 0 : this.reportModel.AssessmentStatus,
      connectionId: connId
    });

    this._cASearchServiceServiceProxy.generateESReport(input).subscribe();

  }
}
