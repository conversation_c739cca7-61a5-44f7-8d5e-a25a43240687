<div [@routerTransition]>
    <div class="ess-ac-card">
        <div class="ess-ac-card-header">VERIFICATION</div>

        <div class="ess-ac-card-body">
            <form #twoFactorForm="ngForm" method="post" (ngSubmit)="submit()">

                <div class="ess-ca-form-control col-flex">
                    <label>Please enter verification code</label>

                    <input id="validateVerificationCode" #codeInput="ngModel" placeholder="Code"
                           [(ngModel)]="code" autoFocus type="text"
                           autocomplete="new-password" name="code" required maxlength="16"
                           [ngClass]="{ 'ess-ca-error': (codeInput.touched && codeInput.invalid) }" />

                    <span *ngIf="remainingSeconds >= 0">
                        Time Remaining: <strong>{{displayMinutes | number : '1.0-0'}}:{{displaySeconds | number : '2.0-0'}}</strong>
                    </span>
                </div>

                <div class="ess-ca-form-control">
                    <button id="validateVerificationCodeSubmit" pButton [disabled]="!twoFactorForm.form.valid" type="submit" label="Submit"
                            class="ui-button-rounded ui-button-warning"></button>
                </div>

                <div class="ess-ca-form-control">
                    <div>
                        <a id="validateCodeAnotherMethod" routerLink="/account/login">Use Another Method</a>
                    </div>
                </div>

                <div class="ess-ca-form-control">
                    <bdo-needhelp></bdo-needhelp>
                </div>
            </form>
        </div>
    </div>
</div>
