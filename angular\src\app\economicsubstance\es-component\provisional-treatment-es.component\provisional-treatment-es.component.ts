import { Component, OnInit, Injector, Input, ViewChild } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';
import { EconomicSubstanceDeclarationDto, DocumentsDto } from '@shared/service-proxies/service-proxies';
import { EconomicDocumentsName } from '@app/economicsubstance/EconomicSubstance';
import { NgForm } from '@angular/forms';
import { EconomicSubstanceService } from '@app/economicsubstance/economicsubstance.service';

@Component({
  selector: 'app-provisional-treatment-es',
  templateUrl: './provisional-treatment-es.component.html',
  styleUrls: ['./provisional-treatment-es.component.css']
})
export class ProvisionalTreatmentEsComponent extends AppComponentBase implements OnInit {


    @Input() public economicsubstance: EconomicSubstanceDeclarationDto;
    @Input() readOnlyMode: boolean;
    @Input() displayFromCa: boolean;
    @Input() ctspId: any;
    ProvisionalTreatmentDocumentDoc: any;

    
    @ViewChild('#provisionalForm', { static: false }) form: NgForm;
    constructor(injector: Injector,
        public _economicSubstanceService: EconomicSubstanceService
    ) { super(injector); }

    checkIfProvisionalTreatmentSubmission(item):boolean
    {
       return (typeof item.provisionalTreatmentSubmissionDate !== "undefined");
    }

    ngOnInit() {


        this.ProvisionalTreatmentDocumentDoc = EconomicDocumentsName.ProvisionalTreatmentDocumentDoc;
    }

}
