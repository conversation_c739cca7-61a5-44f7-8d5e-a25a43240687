import { Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AuditRecordModalComponent } from '@app/shared/common/audit/audit-record-modal.component';
import { appModuleAnimation } from '@shared/animations/routerTransition';
import { AppComponentBase } from '@shared/common/app-component-base';
import { AuditEntityDto, AuditServiceProxy, AuditUserDto } from '@shared/service-proxies/service-proxies';
import * as moment from 'moment';
import { LazyLoadEvent } from 'primeng/components/common/lazyloadevent';
import { Paginator } from 'primeng/components/paginator/paginator';
import { Table } from 'primeng/components/table/table';
import { finalize } from 'rxjs/operators';
import { NgFormValidationComponent } from '../../../shared/utils/validation/ng-form-validation.component';
import { SelectItem } from 'primeng/api';
import { CustomPaginatorComponent } from '@shared/common/components/custom-paginator/custom-paginator.component';

@Component({
    templateUrl: './audit.component.html',
    styleUrls: ['./audit.component.less'],
    encapsulation: ViewEncapsulation.None,
    animations: [appModuleAnimation()]
})
export class AuditComponent extends AppComponentBase {
    @ViewChild('auditSearchFormValidation', { static: true }) auditSearchFormValidation: NgFormValidationComponent;
    @ViewChild('auditSearchForm', { static: true }) auditSearchForm: FormGroup;
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('auditRecordModal', { static: true }) auditRecordModal: AuditRecordModalComponent;
    @ViewChild(CustomPaginatorComponent, { static: true }) paginationComponent: CustomPaginatorComponent;
    paginator: Paginator;
    users: AuditUserDto[];
    actions: AuditEntityDto[];

    dateFrom: moment.Moment;
    dateTo: moment.Moment;

    entityName: string;
    fiscalYear: number;
    fiscalYears: SelectItem[] = [{ label: 'Any', value: '' }];

    selectedUsers: AuditUserDto[];
    selectedActions: AuditEntityDto[];

    isSearch = false;
    hasSearched = false;

    searchDateFrom: moment.Moment;
    searchDateTo: moment.Moment;
    searchEntityName: string;
    searchFiscalYear: number;
    searchSelectedUsers: number[];
    searchSelectedActions: number[];

    constructor(
        injector: Injector,
        private _auditService: AuditServiceProxy
    ) {
        super(injector);

        if (this.hasFullAccess()) {
            this._auditService.getAuditableUsers().subscribe(users => {
                this.users = users;
            });
        }

        this._auditService.getAuditableActions().subscribe(actions => {
            this.actions = actions;
        });

        this._auditService.getFiscalYears().subscribe(years => {
            this.fiscalYears.push(...years.map(x => { return { label: x.toString(), value: x.toString() } }));
        });
    }

    ngOnInit() {
        this.auditSearchFormValidation.formGroup = this.auditSearchForm;
        if (this.paginationComponent) {
            this.paginator = this.paginationComponent.getPaginator();

         }
    }

    hasError(fieldName: string): boolean {
        return this.auditSearchFormValidation.fieldHasErrors(fieldName);
    }

    validateDateRange(): void {
        var dateFromControl = this.auditSearchForm.controls["AuditStartDate"];
        var dateToControl = this.auditSearchForm.controls["AuditEndDate"];
        if (dateFromControl && dateToControl) {
            dateFromControl.markAsTouched();
            dateToControl.markAsTouched();

            if (this.dateFrom == null && this.dateTo == null) {
                dateFromControl.setErrors({
                    customErrors: {
                        errors: [this.l('AuditStartDate') + ' or ' + this.l('AuditEndDate') + ' is required.']
                    }
                });
                dateToControl.setErrors({
                    customErrors: {
                        errors: []
                    }
                });
            } else {
                dateFromControl.setErrors(null);
                dateToControl.setErrors(null);
            }
        }
    }

    getRecords(event?: LazyLoadEvent) {
        if (!this.hasSearched) {
            return;
        }

        if (this.primengTableHelper.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            return;
        }

        this.primengTableHelper.showLoadingIndicator();

        this._auditService.getAuditRecords(
            this.searchDateFrom,
            this.searchDateTo,
            this.searchEntityName,
            this.searchFiscalYear,
            this.searchSelectedUsers,
            this.searchSelectedActions,
            [],
            this.isSearch,
            this.primengTableHelper.getSorting(this.dataTable),
            this.primengTableHelper.getMaxResultCount(this.paginator, event),
            this.primengTableHelper.getSkipCount(this.paginator, event)
        ).pipe(finalize(() => this.primengTableHelper.hideLoadingIndicator())).subscribe(result => {
            this.isSearch = false;
            this.primengTableHelper.totalRecordsCount = result.totalCount;
            this.primengTableHelper.records = result.items;
            this.primengTableHelper.hideLoadingIndicator();
        });
    }

    search() {
        this.validateDateRange();
        if (!this.auditSearchFormValidation.isFormValid()) {
            return;
        }

        this.isSearch = true;
        this.hasSearched = true;

        this.searchDateFrom = this.dateFrom
        this.searchDateTo = this.dateTo;
        this.searchEntityName = this.entityName;
        this.searchFiscalYear = this.fiscalYear;
        this.searchSelectedUsers = this.selectedUsers == null ? [] : this.selectedUsers.map(x => x.id);
        this.searchSelectedActions = this.selectedActions == null ? [] : this.selectedActions.map(x => x.entity);

        this.getRecords();
    }

    reloadPage(): void {
    }

    onRowSelect(record: any) {
        this.auditRecordModal.show(
            this.searchDateFrom,
            this.searchDateTo,
            this.searchEntityName,
            this.searchFiscalYear,
            this.searchSelectedUsers,
            this.searchSelectedActions,
            record.data.id);
    }

    hasFullAccess(): boolean {
        return this.isGrantedAny("Ca.Audit.Full");
    }
}
