using System;

namespace Bdo.Ess.Dtos.FileUpload
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class ValidationAttribute : Attribute
    {
        public ValidationAttribute(ValidationPropertyType type, params string[] parameters)
        {
            Type = type;
            Parameters = parameters;
        }

        public ValidationPropertyType Type { get; private set; }
        public string[] Parameters { get; private set; }

        public override object TypeId
        {
            get
            {
                return this;
            }
        }
    }
}
