import { Component, OnInit, Injector } from '@angular/core';
import { DocumentsDto} from '@shared/service-proxies/service-proxies';
import { tap } from 'rxjs/operators';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/api';
import { AppComponentBase } from '@shared/common/app-component-base';

@Component({
  selector: 'app-document-preview-dialog',
    templateUrl: './document-preview-dialog.component.html',
    styleUrls: ['./document-preview-dialog.component.css']
})
export class DocumentPreviewDialogComponent extends AppComponentBase implements OnInit
{

  document: DocumentsDto

  constructor(
    injector: Injector,
    public config: DynamicDialogConfig,
      public ref: DynamicDialogRef)
  {
      super(injector)
      const documentPrv = config.data.doucmnet;
      config.header = `${this.l("Document Preview")} - ${documentPrv.fileName}`
      this.document = documentPrv;
  }

    ngOnInit() {   }

}
