<p-card class="p-card-properties_form">
    <p-header class="row-flex-space">
        <div>{{l("CaAssessmentTitle")}}</div>


        <div [ngStyle]="getButtonBarStyle(printService.isPrintMode)" class="row-flex-justified">
            <button id="print" pButton type="button" icon="pi pi-print" class="ui-button-rounded ui-button-secondary margin-left bigger-icons" iconPos="left"
                    tooltip="Print" (click)="onPrint()"></button>
            <button id="close" pButton type="button" class="ui-button-rounded ui-button-secondary margin-left bigger-icons"
                    icon="pi pi-times" tooltip="Close" (click)="onExit()"> </button>
        </div>
    </p-header>

    <div class="row-flex-justified">

       


        <div class="col-flex ten-percent-width">
            <label>{{l("CaAssessmentReason")}} </label>
            <li *ngFor="let item of redFlagEventResult">
                {{ item }}
            </li>
        </div>
        <div class="col-flex fortyfive-percent-width">
            <label style="font-size:14px">{{l("CaAssessmentStatus")}}  {{esassessment.esReviewStatus ?esassessment.esReviewStatus.name:'Not started'}}</label>
            <div *ngIf="esassessment.assessmentLevel!=0">
                <label style="font-size:14px">{{l("CaAssignTo")}} Level {{esassessment.assessmentLevel}}</label>
            </div>
            <label style="font-size:14px" for="binary">{{l("Penalty Applied: ")}} {{esassessment.penaltyApplied?'Yes':'No' }} </label>
           
        </div>

        <div class="col-flex fortyfive-percent-width">
            <div class="col-flex">
                <label>{{l("CaAssessmentComments")}} </label>
                <div class="row-flex-space">
                    <button id="CaReviewAssessmentComments" pButton type="button"
                            icon="pi pi-undo" tooltip="Assessment History" (click)="onShowComments()"> </button>
                </div>

            </div>

        </div>
    </div>



</p-card>

<p-card class="p-card-properties_form">
    <app-main-economics-reo [corporateEntity]="corporateEntity"
                            [currentEconomicSubstance]="currentEconomicSubstance"
                            [informationRequired]="informationRequired"
                            [informationRequiredsHistory]="informationRequiredsHistory"
                            [esassessment]="esassessment"
                            [relevantActivityStatus]="relevantActivityStatus"
                            [displayHeader]="true"
                            [displayFromCa]="true"
                            [ctspId]="ctspId" >
    </app-main-economics-reo>
    
</p-card>
