<div [@routerTransition]>
    <div class="ess-ac-card">
        <div class="ess-ac-card-left">
            <a id="forgetPasswordBackToLogin" routerLink="/account/login">&lt; Back to Login</a>
        </div>

        <div class="ess-ac-card-header">RESET PASSWORD</div>

        <div class="ess-ac-card-body">
            <form #forgotPassForm="ngForm" method="post" novalidate (ngSubmit)="save()">

                <div class="ess-ca-form-control col-flex">
                    <label>Username or Email *</label>
                    <input id="forgetPasswordUsernameOrEmail" #userNameOrEmailAddressInput="ngModel" placeholder="Username or Email"
                           [(ngModel)]="model.userName" autoFocus type="text"
                           autocomplete="new-password" name="userName" required
                           [ngClass]="{ 'ess-ca-error': (userNameOrEmailAddressInput.touched && userNameOrEmailAddressInput.invalid) }" />
                </div>

                <div class="ess-ca-form-control col-flex">
                    <label>Method *</label>
                    <p-dropdown id="forgetPasswordMethod" [options]="methods" [(ngModel)]="selectedMethod" name="method"></p-dropdown>
                </div>

                <div class="ess-ca-form-control">
                    <p *ngIf="selectedMethod == emailMethod">
                        {{"SendPasswordResetLink_Information" | localize}}
                    </p>
                </div>

                <div class="ess-ca-form-control">
                    <button id="forgetPasswordSubmit" pButton [disabled]="!forgotPassForm.form.valid" type="submit" label="Continue"
                            class="ui-button-rounded ui-button-warning"></button>
                </div>

                <div class="ess-ca-form-control col-flex">
                    <bdo-needhelp></bdo-needhelp>
                </div>
            </form>
        </div>
    </div>
</div>
