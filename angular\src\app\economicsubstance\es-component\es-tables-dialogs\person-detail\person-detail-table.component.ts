import { Component, OnInit, Input, Injector } from '@angular/core';
import { PersonDetailsDto } from '@shared/service-proxies/service-proxies';
import { AppComponentBase } from '@shared/common/app-component-base';
import * as _ from 'lodash';
import { AppConsts } from '@shared/AppConsts';

@Component({
  selector: 'app-person-detail-table',
  templateUrl: './person-detail-table.component.html',
  styleUrls: ['./person-detail-table.component.css']
})
export class PersonDetailTableComponent extends AppComponentBase implements OnInit {

    @Input() personDetails: PersonDetailsDto[];
    @Input() headingSeq: string;
    @Input() sequenceNo: string;
    @Input() readOnlyMode: boolean;

  constructor(injector: Injector) { super(injector); }

    ngOnInit() {
    }



    removesource(id: any): void
    {
        let self = this;
        abp.message.confirm(
            AppConsts.messageList.EsDeletedConfirmation,
            'Are you sure you want to delete it?',
            function (isConfirmed) {
                if (isConfirmed) {
                    self.handleDelete(id);
                }
            }
        );
    }

    handleDelete(id: any) {
        let index = this.personDetails.findIndex(x => x.id == id);
        this.personDetails[index].isDeleted = index != -1 ? true : this.personDetails[index].isDeleted;
    }
    //update table
    updateSource(sp: PersonDetailsDto)
    {
        let persondetail = _.cloneDeep(sp);
        let index = this.personDetails.findIndex(x => x.id == sp.id);
        if (index == -1) this.personDetails.push(persondetail);
        else this.personDetails[index] = persondetail;
    }

    returnyesno(item: any):string
    {
        if (item === undefined || item === null) return '';
        return item ? 'Yes' : 'No';
        
    }
}
