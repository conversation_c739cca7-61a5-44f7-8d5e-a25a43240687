import { Component, HostListener, Injector, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ComponentCanDeactivate } from '@app/shared/common/auth/auth-route-guard';
import { AppComponentBase } from '@shared/common/app-component-base';
import {
    AuditEventServiceProxy, CaEconomicSubstanceAuditDto, CASearchServiceServiceProxy, ESCAAssessmentDto,
    EconomicSubstanceInformationRequiredDto, RedFlagReportServiceProxy,
    RelativeActivitySelection,
    CorporateEntityDto, EconomicSubstanceDeclarationDto, UserListDto, WrokflowServiceProxy
} from '@shared/service-proxies/service-proxies';

import { RelaventActivityReviewStatus } from '@app/economicsubstance/EconomicSubstance';

import { forkJoin, Observable } from 'rxjs';


@Component({
    selector: 'app-es-ca-review',
    templateUrl: './es-ca-review.component.html',
    styleUrls: ['./es-ca-review.component.css']
})



export class EsCaReviewComponent extends AppComponentBase implements OnInit, ComponentCanDeactivate {
    
    id: any;
    ctspid: any;
    IsReady: boolean = false;
    corporateEntity: CorporateEntityDto;
    esassessment: ESCAAssessmentDto;
    esUserlist: UserListDto[] = [];
    currentEconomicSubstance: EconomicSubstanceDeclarationDto;
    informationRequired: EconomicSubstanceInformationRequiredDto;
    informationRequiredsHistory: EconomicSubstanceInformationRequiredDto[] = [];

    relevantActivityStatus: RelaventActivityReviewStatus[];

    selectedActivityStatus: RelaventActivityReviewStatus = new RelaventActivityReviewStatus();
    redFlagEventResult: string[];

    constructor(injector: Injector,
        private router: Router,
        private route: ActivatedRoute,
        private _caSearchServiceServiceProxy: CASearchServiceServiceProxy,
        private _auditEventService: AuditEventServiceProxy,
        private _redFlagReportServiceProxy: RedFlagReportServiceProxy,
        private _wrokflowServiceProxy: WrokflowServiceProxy
    ) {
        super(injector);
        this.route.params.subscribe(x => {
            this.id = x.economicsubid;
            this.ctspid = x.ctspid;

            
            forkJoin([
                // difference
                this._caSearchServiceServiceProxy.getEconomicSubstanceDetailById(this.ctspid, this.id),
                this._wrokflowServiceProxy.getESAssessment(this.ctspid, this.id, undefined),
                this._wrokflowServiceProxy.getAssessmentUsers(undefined, undefined),
                this._redFlagReportServiceProxy.getRedFlagsForESDeclaration(this.ctspid, this.id)
            ]).subscribe(responseList => {

                
                this.corporateEntity = responseList[0].corporateEntity;
                this.currentEconomicSubstance = responseList[0];
                this.esassessment = responseList[1];
                this.esUserlist = this.addSelectUser(responseList[2]);

                this.esassessment.currentAssignedUser = this.esUserlist.find(x => x.userName == this.esassessment.reviewerName);
                
                _wrokflowServiceProxy.getESReviewStatusByESID(this.ctspid, this.currentEconomicSubstance.id, undefined).subscribe(result => {
                    this.currentEconomicSubstance.esReviewStatus = result;
                });

                if (typeof this.currentEconomicSubstance.economicSubstanceInformationRequired === "undefined") {
                    this.informationRequired = null;
                    this.informationRequiredsHistory = null;
                }
                else {
                    let result = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.isInformationRequired);
                    if (result != null && result.length > 1) {
                        this.informationRequired = result[0];
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired.filter(x => x.id !== this.informationRequired.id);
                    }
                    else {
                        this.informationRequired = null
                        this.informationRequiredsHistory = this.currentEconomicSubstance.economicSubstanceInformationRequired;
                    }
                }

                this.redFlagEventResult = responseList[3];
                this.IsReady = true;

            });
           
            let auditInput = new CaEconomicSubstanceAuditDto();
            auditInput.ctspNumber = this.ctspid;
            auditInput.id = this.id;
            this._auditEventService.auditCaEconomicSubstanceViewEvent(auditInput).subscribe();
        });
    }


    addSelectUser(userlist: UserListDto[]): UserListDto[] {
        let unselectuser = new UserListDto();

        unselectuser.displayName = "Select user to assign"
        userlist.unshift(unselectuser);
        return userlist
    }


    @HostListener('window:beforeunload')
    canDeactivate(): Observable<boolean> | boolean {

        return true;

    }

    ngOnInit()
    {
        this.relevantActivityStatus =
            [{ label: 'Select', value: undefined },
                { label: 'Pass', value: RelativeActivitySelection.Pass },
                { label: 'Fail', value: RelativeActivitySelection.Fail }
            ];

    }

}
