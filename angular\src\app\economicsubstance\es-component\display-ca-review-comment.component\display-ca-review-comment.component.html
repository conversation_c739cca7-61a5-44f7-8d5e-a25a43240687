
    <p-table [value]="escaAssessmentCommentsDto" [paginator]="true" [rows]="10" >
        <ng-template pTemplate="header">
            <tr>
                <th>Action Type</th>
                <th>Assigned To</th>
                <th>Assessment Status</th>
                <th>Comments</th>
                <th>Attachments</th>
                <th>Updated By</th>
                <th>Updated At</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr>
                <td>
                    {{GeTAssessmentType(item.actionType)}}
                </td>
                <td>{{item.reviewerFullName}}</td>
                <td>{{item.esReviewStatus.name }}</td>
                <td> {{item.comments}}</td>
                <!-- Attachments to CTSP -->
                <td >
                    <!-- Attachments to CTSP 
                    <label *ngIf="item.esCaCspInformationRequestDocuments && item.esCaCspInformationRequestDocuments.length>0">To CTSP</label>
                    <div *ngIf="item.esCaCspInformationRequestDocuments && item.esCaCspInformationRequestDocuments.length>0">
                        <li *ngFor="let itemx of item.esCaCspInformationRequestDocuments">
                            
                            <a href="javascript:;" (click)="PreviewDoc(itemx.documentsId)">{{GetShortFileName(itemx.fileName)}}</a>
                        </li>
                    </div>-->
                      <!-- Internal Attachments 
                    <label *ngIf="item.escaAssessmentDocuments && item.escaAssessmentDocuments.length>0">Internal</label>-->
                    <div *ngIf="item.escaAssessmentDocuments && item.escaAssessmentDocuments.length>0">
                        <li *ngFor="let itemx of item.escaAssessmentDocuments">
                            
                            <a href="javascript:;" (click)="PreviewDoc(itemx.documentsId)">{{GetShortFileName(itemx.fileName)}}</a>
                        </li>
                    </div>
                </td>
                
                <td>{{item.submitterFullName}}</td>
                <td>{{returnDate(item.createdAt) | date: "yyyy-MM-dd HH:mm:ss"}}</td>

            </tr>
        </ng-template>
    </p-table>

