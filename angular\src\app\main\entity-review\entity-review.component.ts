import { Component, OnInit, Injector, Input } from '@angular/core';
import { AppComponentBase } from '@shared/common/app-component-base';

@Component({
  selector: 'app-entity-review',
  templateUrl: './entity-review.component.html',
  styleUrls: ['./entity-review.component.css']
})
export class EntityReviewComponent extends AppComponentBase implements OnInit {

  @Input() entity: any

  constructor(injector: Injector) {
    super(injector)
  }

  ngOnInit() {
  }

  ngOnDestroy() {
  }

}
